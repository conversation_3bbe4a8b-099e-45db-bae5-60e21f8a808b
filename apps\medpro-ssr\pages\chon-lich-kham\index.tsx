import BookingApp from '../../src/page-content/booking-app'
import BookingTree from '../../src/page-content/booking'

import { GetServerSidePropsContext } from 'next'
import { title } from 'process'
const Booking = (props: any) => {
  return props.isMobile ? <BookingApp /> : <BookingTree {...props} />
}
export default Booking
Booking.breadcrumb = [{ title: 'Chọn thông tin khám' }]
export async function getServerSideProps(context: GetServerSidePropsContext) {
  const userAgent = context.req.headers['user-agent']
  const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent)
  return {
    props: {
      isMobile,
      robots: 'noindex, nofollow'
    }
  }
}
