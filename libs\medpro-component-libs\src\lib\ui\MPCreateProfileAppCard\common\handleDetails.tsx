import { Form, Input, Select } from 'antd'
import styles from '../styles.module.less'
import { Valid, sexData } from '@medpro-libs/libs'
import { size } from 'lodash'
import cx from 'classnames'

// export interface ListFormIF {}

const valid = new Valid()
const { Option } = Select

export const handleDetails = (
  openSelect: (field: any) => void,
  province: any[],
  form: any,
  age: number,
  partnerId?: string,
  toggleAge?: any
) => {
  const label_code_ID = 'Mã định danh/CCCD/Passport'
  const handleRequireInput = (label: string, require: boolean) => {
    if (require) {
      return (
        <>
          {label} <sup className={styles['requireInput']}>*</sup>
        </>
      )
    }
    return <>{label}</>
  }

  const list = [
    {
      id: 'name',
      type: 'text',
      label: 'Họ và tên (có dấu)',
      placeholder: 'Nhập họ và tên',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.name }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'mobile',
      type: 'text',
      label: 'Số điện thoại',
      placeholder: 'Nhập số điện thoại',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.mobile }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'birthdate',
      type: 'text',
      label: 'Ngày sinh',
      placeholder: 'Nhập ngày sinh',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.birthdate }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              onChange={() => {
                toggleAge(form.getFieldValue('birthdate'))
              }}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '50%'
    },
    {
      id: 'sex',
      type: 'select',
      label: 'Giới tính',
      placeholder: 'Chọn giới tính',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.sex }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              open={false}
              onClick={() => openSelect({ id })}
              className={
                form.getFieldValue('sex') === 0 ||
                form.getFieldValue('sex') === 1
                  ? styles['validInput']
                  : ''
              }
            >
              {sexData?.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '50%'
    },
    {
      id: 'cmnd',
      type: 'text',
      label: label_code_ID,
      placeholder: `Vui lòng nhập ${label_code_ID}`,
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              {
                validator: valid.cccd
              }
            ]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]: form.getFieldValue('cmnd') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'city_id',
      type: 'select',
      label: 'Tỉnh/Thành phố',
      placeholder: 'Nhập tỉnh / thành phố',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.province }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              open={false}
              onClick={() =>
                openSelect({ id, title: 'Chọn tỉnh/thành', data: province })
              }
              className={form.getFieldValue('city_id') && styles['validInput']}
            >
              {size(province) > 0 &&
                province?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    }
  ]
  return list
}
