// .shadow {
//   position: absolute;
//   bottom: -69px;
//   background: linear-gradient(0deg, #fdfdfe 0%, #cde7f9 100%);
//   height: 69px;
//   width: 100%;

//   @media (max-width: 768px) {
//     display: block;
//     margin-top: -30px;
//   }
// }
.Rectangle3462 {
  background: linear-gradient(180deg, #ffffff 0%, #ebf2ff 100%);
  height: 68.15px;
  @media (max-width: 768px) {
    background: linear-gradient(180deg, #ffffff 0%, #e8f4fd 100%);
    height: 40.45px;
  }
}
.Rectangle4123 {
  background: linear-gradient(0deg, #ffffff 0%, #ebf2ff 100%);
  height: 68.15px;
  @media (max-width: 768px) {
    background: linear-gradient(0deg, #ffffff 0%, #e8f4fd 100%);
    height: 40.45px;
  }
}
.skeleton {
  display: flex;
  gap: 24px;
  margin-bottom: 50px;
  margin-top: 24px;
  @media (max-width: 768px) {
    margin-bottom: 26px;
    gap: 12px;
  }
}

.content_mobileFeatures {
  display: none;
  @media (max-width: 768px) {
    display: block;
  }
}

.notFoundContainer {
  display: flex;
  align-items: center;
  justify-content: center;

  .content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 500;

    .btnHome {
      padding: 8px 16px !important;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      background: transparent;
      color: #3498db;
      font-size: 0.875rem;

      &:hover {
        color: #2980b9;
      }
      svg {
        margin-right: 5px;
        padding: 2px;
        background: #fff;
        border-radius: 50%;
        font-size: 1.2rem;
        color: #2980b9;
        fill: #2980b9;
      }
    }
  }
}
.start {
  padding-bottom: 0 !important;
}
.main-ecomerce {
  background: #e8f4fd !important;
  // padding: 45px 0;
  padding-bottom: 0 !important;
}
.end {
  padding-top: 8px;
  @media (max-width: 768px) {
    padding-bottom: 0px !important;
  }
}
.main {
  width: 100vw;
  background: white;
  font-family: 'Roboto', sans-serif !important;
  display: flex;
  flex-direction: column;
  @media (max-width: 768px) {
    .dowloadApp {
      background-color: white;
    }
  }

  .mobileFeatures {
    padding: 0 16px !important;
    margin-top: -40px;
    @media (max-width: 768px) {
      display: block;
      margin-top: -60px;
    }
    @media (max-width: 375px) {
      margin-top: -70px;
    }
  }
  .content_mobileFeatures {
    display: none;
    @media (max-width: 768px) {
      display: block;
    }
  }
  .header {
    position: relative;
    font-weight: 400;
    font-size: 20px;
    line-height: 23px;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    @media (max-width: 1440px) {
      height: 47vw;
    }
    @media (max-width: 1024px) {
      margin-top: 0;
      // margin-bottom: 120px;
    }
    @media (max-width: 768px) {
      height: 395px;
      margin-bottom: 0;
      align-items: normal;
    }
    
    @media (max-width: 390px) {
      height: 395px;
    }
    @media (max-width: 375px) {
      height: 395px;
    }

    .service {
      @media (max-width: 992px) {
        margin-top: 0;
      }
      @media (min-width: 516px) and (max-width: 767px) {
        margin-top: -3%;
      }
      @media (max-width: 768px) {
        margin-top: 0;
        display: none;
      }
      max-width: 1225px !important;
      width: 100%;
      margin-top: -6%;
    }
  }
  .homeStatistic {
    width: 100%;
    max-width: 1180px;
    margin: auto;
    display: flex;
    flex-direction: column;
    @media (max-width: 768px) {
      // order: 5;
      padding: 0 15px;
    }
  }
  .ecomerce {
    max-width: 1200px !important;
  }
  .homeContainer {
    width: 100%;
    max-width: 1180px;
    margin: auto;
    display: flex;
    flex-direction: column;
    @media (max-width: 768px) {
      // order: 5;
    }

    .supportMP {
      padding: 0 16px 0 16px;
      @media (max-width: 768px) {
        padding: 0;
      }
    }
  }

  .support {
    position: fixed;
    bottom: 75px;
    right: 35px;
    background: #ffffff;
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    /* Drop shadow */
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.04),
      0px 2px 6px 0px rgba(0, 0, 0, 0.04), 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
    border-radius: 50%;
    cursor: pointer;
  }
}

.section_download {
  background: linear-gradient(
    180deg,
    #fff 52.32%,
    rgba(255, 255, 255, 0) 100%,
    #e8f2f7 100%
  );
}

.Achievement {
  @media (max-width: 768px) {
    // order: 5;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 1) 36%,
      rgba(205, 231, 249, 0.01724439775910369) 55%
    );
  }
}
.modal {
  border-radius: 16px !important;
  :global {
    .ant-modal-header {
      background-color: #fff !important;
      border-radius: 16px;
      border: none;
      padding: 16px 24px 0 24px;
    }
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-body {
      color: var(--primary-body-text, #003553);
      font-family: 'Roboto' !important;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .ant-modal-close-x {
      svg {
        fill: #000;
      }
    }
    .ant-modal-title {
      text-align: center;
      font-family: 'Roboto' !important;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      background: var(
        --primary-gradient-title,
        linear-gradient(84deg, #00b5f1 33.34%, #00e0ff 113.91%)
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
