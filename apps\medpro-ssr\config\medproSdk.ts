import { Client } from 'medpro-sdk-v2'
import { currentEnv } from './envs'
import { API_URL_LOCAL } from './envs/environment'

const apiRoot = currentEnv.API_BE
const appId = currentEnv.APP_ID
export const initOptions = {
  appid: appId,
  apiRoot,
  platform: 'pc',
  locale: 'vi'
}

const client = new Client(initOptions)
if (typeof window !== 'undefined') {
  const searchParams = new URLSearchParams(window.location.search)
  const refCode =
    searchParams.get('refCode') || localStorage.getItem('refCode') || ''
  client.setRefCode(refCode)
  client.setToken(localStorage.getItem('token'))
}

let server: Client
if (typeof window === 'undefined') {
  console.log('API_URL_LOCAL: ', API_URL_LOCAL)
  server = new Client({
    apiRoot: API_URL_LOCAL || apiRoot,
    platform: 'pc',
    locale: 'vi'
  })
}

export default client
export { server }
