import { combineReducers } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import userReducer from './user/userSlice'
import hospitalFee from './hospital-fee/hospitalFeeSlice'
import filterCheck from './filter-check/filterCheckSlice'
import hospitalSlice from './hospital/hospitalSlice'
import patientSlice from './patient/patientSlice'
import { bookingReducer } from './booking/slice'
import { totalReducer } from './total/slice'
import { invoiceReducer } from './invoice/invoiceSlice'
import { featureReducer } from './feature/slice'
import paymentReducer from './payment/slice'
import { reExamReducer } from './reexam/reexamSlice'

export const rootReducer = combineReducers({
  user: userReducer,
  booking: persistReducer(
    {
      key: 'booking',
      storage,
      // whitelist: ['selectedFeature', 'multiSchedules', 'schedule'],
      blacklist: ['sharePayment', 'reexam', 'paymentInfo', 'bookingTreeState']
    },
    bookingReducer
  ),
  hospital: hospitalSlice,
  patient: patientSlice,
  total: totalReducer,
  hospitalFee: persistReducer(
    {
      key: 'hospitalFee',
      storage,
      blacklist: ['paymentInfo']
    },
    hospitalFee
  ),
  filterCheck: filterCheck,
  invoice: invoiceReducer,
  feature: featureReducer,
  reexam: reExamReducer,
  payment: paymentReducer
})

// Persistor config
export const rootPersistConfig = {
  key: 'root',
  version: 1,
  storage,
  whitelist: ['booking', 'patient', 'filterCheck', 'invoice', 'feature', 'reexam'],
  blacklist: []
}
