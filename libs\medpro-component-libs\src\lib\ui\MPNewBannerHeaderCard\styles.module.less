.bannerHeader {
  width: 100%;
  height: 100%;
  max-width: 1980px;
  aspect-ratio: 1366/435;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  font-family: 'Montserrat', sans-serif !important;
  min-height: 266px;
  background-size: cover;
  background-image: url('https://cdn-pkh.longvan.net/prod-partner/df4e7e5a-5af2-4110-81d9-c45b3cb7be90-banner.webp');

  @media screen and (max-width: 768px) {
    min-height: fit-content;
    background-position: top;
    background-size: cover;
    background-image: url('https://cdn-pkh.longvan.net/prod-partner/6061e6b3-aee3-4b9a-b32b-2e5fc0da8ac9-backgroundbannermobile.webp');
  }
  .container {
    max-width: 1180px;
    margin: auto;
  }

  .contentLeft {
    @media (max-width: 1440px) {
      padding-left: 28px;
    }
    @media (max-width: 1024px) {
      padding: 95px 0 0 28px;
    }
    @media (max-width: 992px) {
      padding: 85px 0 0;
    }
    @media (max-width: 768px) {
      padding: 75px 0 0;
    }
    @media (max-width: 768px) {
      padding: 30px 0 0;
    }
    padding: 95px 0;

    .wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      @media (max-width: 992px) {
        // width: 415px;
        padding: 0 32px;
      }
      @media (max-width: 768px) {
        // width: 412px;
        padding: 0 22px;
      }
      @media (max-width: 768px) {
        padding: 0 16px;
      }
      padding: 0 16px;
      max-width: 100%;
    }

    .tag {
      color: var(--text-blue, #11a2f3);
      font-size: 25px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      @media (max-width: 768px) {
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
      margin-bottom: 8px;
    }

    .title {
      color: #003553;
      text-shadow: 0px 4px 4px rgba(104, 125, 199, 0.2);
      font-family: 'Montserrat' !important;
      font-size: 36px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin: 0;
      display: block;
      svg {
        vertical-align: middle;
      }
      @media (max-width: 992px) {
        line-height: 42px;
        font-size: 26px;
      }
      @media (max-width: 768px) {
        white-space: pre;
        color: var(--primary-body-text, #003553);
        text-align: center;
        text-shadow: 0px 4px 4px rgba(104, 125, 199, 0.2);
        font-size: 20px;
        line-height: 25px;
      }
      @media (max-width: 375px) {
        font-size: 20px;
        line-height: normal;
      }

      .line_2 {
        font-family: 'Montserrat' !important;
        white-space: nowrap;
        @media (max-width: 992px) {
          line-height: 42px;
          font-size: 26px;
        }
        @media (max-width: 587px) {
          font-size: 21px;
        }
        @media (max-width: 375px) {
          font-size: 17px;
        }
      }
    }
    .benefit {
      margin-top: 20px;
      .desc {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #003553;
        // font-size: 16px;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 0;
        display: none;
      }
    }

    .button {
      .btnBooking {
        @media (max-width: 992px) {
          padding: 10px 30px;
        }
        @media (max-width: 390px) {
          font-size: 12px;
        }
        @media (max-width: 768px) {
          line-height: normal;
          padding: 10px;
        }
        background: #ffb54a;
        border: none;
        border-radius: 30px;
        padding: 16px 40px;
        font-size: 16px;
        line-height: 18.75px;
        font-weight: 500;

        &:hover {
          background: #df8e1c;
        }
      }
    }
  }
}

@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(0);
  }
  50% {
    opacity: 0.5;
    transform: translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
