import { API_ROOT } from './common/constants'
import type { ClientOptions } from './interfaces'
import type { IAppId } from './modules/app-id/app-id'
import { AppId } from './modules/app-id/app-id'
import type { IBooking } from './modules/booking/booking'
import { Booking } from './modules/booking/booking'
import type { IBookingTree } from './modules/booking-tree/booking-tree'
import { BookingTree } from './modules/booking-tree/booking-tree'
import type { ICity } from './modules/city/city'
import { City } from './modules/city/city'
import type { IClinic } from './modules/clinic/clinic'
import { Clinic } from './modules/clinic/clinic'
import type { ICountry } from './modules/country/country'
import { Country } from './modules/country/country'
import type { ICskh } from './modules/cskh/cskh'
import { Cskh } from './modules/cskh/cskh'
import type { IDistrict } from './modules/district/district'
import { District } from './modules/district/district'
import type { IEvent } from './modules/event/event'
import { Event } from './modules/event/event'
import type { IFeature } from './modules/feature/feature'
import { Feature } from './modules/feature/feature'
import type { IFilterCheck } from './modules/filter-check/filter-check'
import { FilterCheck } from './modules/filter-check/filter-check'
import type { IHospital } from './modules/hospital/hospital'
import { Hospital } from './modules/hospital/hospital'
import type { IMedproId } from './modules/medpro-id/medpro-id'
import { MedproId } from './modules/medpro-id/medpro-id'
import type { INation } from './modules/nation/nation'
import { Nation } from './modules/nation/nation'
// Home Page Get
import { IHomePageGet } from './modules/homepage-get/homepageget'
import { HomePageGet } from './modules/homepage-get/homepageget'
// **********************
import type { INotificationDKKB } from './modules/notification-dkkb/notification-dkkb'
import { NotificationDKKB } from './modules/notification-dkkb/notification-dkkb'
import type { IPartnerConfig } from './modules/partner-config/partner-config'
import { PartnerConfig } from './modules/partner-config/partner-config'
import type { IPatient } from './modules/patient/patient'
import { Patient } from './modules/patient/patient'
import type { IPatientGuide } from './modules/patient-guide/patient-guide'
import { PatientGuide } from './modules/patient-guide/patient-guide'
import type { IPayment } from './modules/payment/payment'
import { Payment } from './modules/payment/payment'
import type { IPaymentFee } from './modules/payment-fee/payment-fee'
import { PaymentFee } from './modules/payment-fee/payment-fee'
import type { IProfession } from './modules/profession/profession'
import { Profession } from './modules/profession/profession'
import type { IReExam } from './modules/re-exam/re-exam'
import { ReExam } from './modules/re-exam/re-exam'
import type { IRelation } from './modules/relation/relation'
import { Relation } from './modules/relation/relation'
import type { IStorage } from './modules/storage/storage'
import { Storage } from './modules/storage/storage'
import type { ITrackingOrder } from './modules/tracking-order/tracking-order'
import { TrackingOrder } from './modules/tracking-order/tracking-order'
import type { IUser } from './modules/user/user'
import { User } from './modules/user/user'
import type { IWard } from './modules/ward/ward'
import { Ward } from './modules/ward/ward'
import type { IXnc } from './modules/xnc/xnc'
import { Xnc } from './modules/xnc/xnc'
import { Doctor, IDoctor } from './modules/doctor/doctor'
import { SearchService } from './modules/search/search'
import { DynamicLink, IDynamicLink } from './modules/dynamic-link/doctor'
import { IServicePrice, ServicePrice } from './modules/service-price/app-id'
import { ISurvey, Survey } from 'libs/medpro-sdk-v2/src/modules/survey/survey'
import { Deal, IDeal } from './modules/deal/deal'
import { ITotal, Total } from './modules/total/total'
import { IPackageDoctor, PackageDoctor } from './modules/package-doctor/doctor'
import { IRecruitment, Recruitment } from './modules/recruitment/recruitment'

export class Client {
  options: ClientOptions

  patient: IPatient

  medproId: IMedproId

  bookingTree: IBookingTree

  booking: IBooking

  user: IUser

  notification: INotificationDKKB

  notify: IEvent

  paymentMethod: IPayment

  cskh: ICskh

  reExam: IReExam

  paymentFee: IPaymentFee

  trackingOrder: ITrackingOrder

  partner: IHospital

  patientGuide: IPatientGuide

  xuatCanh: IXnc

  feature: IFeature

  partnerConfig: IPartnerConfig

  relation: IRelation

  filterCheck: IFilterCheck

  appId: IAppId

  ward: IWard

  district: IDistrict

  city: ICity

  country: ICountry

  nation: INation

  homePageGet: IHomePageGet

  recruitment: IRecruitment

  profession: IProfession

  storage: IStorage

  clinic: IClinic

  doctor: IDoctor

  searchService: SearchService

  dynamicLink: IDynamicLink

  servicePrice: IServicePrice

  survey: ISurvey

  deal: IDeal

  total: ITotal
  packageDoctor: IPackageDoctor

  constructor(options?: ClientOptions) {
    this.options = {
      ...{
        apiRoot: API_ROOT,
        partnerid: '',
        appid: '',
        osid: '',
        ostoken: '',
        locale: '',
        token: '',
        cskhtoken: '',
        platform: '',
        version: '',
        refcode: ''
      },
      ...(options || {})
    }
    this.init()
  }

  init() {
    this.patient = new Patient(this.options)
    this.medproId = new MedproId(this.options)
    this.bookingTree = new BookingTree(this.options)
    this.user = new User(this.options)
    this.booking = new Booking(this.options)
    this.notification = new NotificationDKKB(this.options)
    this.notify = new Event(this.options)
    this.paymentMethod = new Payment(this.options)
    this.cskh = new Cskh(this.options)
    this.reExam = new ReExam(this.options)
    this.paymentFee = new PaymentFee(this.options)
    this.trackingOrder = new TrackingOrder(this.options)
    this.partner = new Hospital(this.options)
    this.patientGuide = new PatientGuide(this.options)
    this.xuatCanh = new Xnc(this.options)
    this.feature = new Feature(this.options)
    this.partnerConfig = new PartnerConfig(this.options)
    this.relation = new Relation(this.options)
    this.filterCheck = new FilterCheck(this.options)
    this.appId = new AppId(this.options)
    this.ward = new Ward(this.options)
    this.district = new District(this.options)
    this.city = new City(this.options)
    this.country = new Country(this.options)
    this.nation = new Nation(this.options)
    this.homePageGet = new HomePageGet(this.options)
    this.recruitment = new Recruitment(this.options)
    this.profession = new Profession(this.options)
    this.storage = new Storage(this.options)
    this.clinic = new Clinic(this.options)
    this.doctor = new Doctor(this.options)
    this.searchService = new SearchService(this.options)
    this.dynamicLink = new DynamicLink(this.options)
    this.servicePrice = new ServicePrice(this.options)
    this.survey = new Survey(this.options)
    this.deal = new Deal(this.options)
    this.total = new Total(this.options)
    this.packageDoctor = new PackageDoctor(this.options)
  }

  setDomain(url: string) {
    this.options.apiRoot = url
    this.init()
  }

  setToken(token: string) {
    this.options.token = token
    this.init()
  }

  setPartner(partnerid: string) {
    this.options.partnerid = partnerid
    this.init()
  }

  setAppId(appid: string) {
    this.options.appid = appid
    this.init()
  }

  setCskhToken(cskhtoken: string) {
    this.options.cskhtoken = cskhtoken
    this.init()
  }

  setOneSignal(osid: string, ostoken: string) {
    this.options.osid = osid
    this.options.ostoken = ostoken
    this.init()
  }

  setLocale(locale: string) {
    this.options.locale = locale
    this.init()
  }

  setVersion(version: string) {
    this.options.version = version
    this.init()
  }

  setPlatform(platform: string) {
    this.options.platform = platform
    this.init()
  }

  setRefCode(refCode: string) {
    this.options.refcode = refCode
    this.init()
  }

  getOptions(): ClientOptions {
    return this.options
  }
}
