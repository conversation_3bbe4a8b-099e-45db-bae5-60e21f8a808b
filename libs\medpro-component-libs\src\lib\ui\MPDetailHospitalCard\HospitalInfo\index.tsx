import {
  Button,
  Carousel,
  CarouselProps,
  Col,
  Popover,
  Rate,
  Row,
  Space
} from 'antd'
import cx from 'classnames'
import { size } from 'lodash'
import Image from 'next/image'
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import { HiCheckBadge, HiOutlineClock } from 'react-icons/hi2'

import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight
} from 'react-icons/md'
import MPNewServiceHeader from '../../../shapes/MPNewServiceHeader'
import { OkModal } from '../../common/Modal'
import errBanner from './errImg.svg'
import styles from './styles.module.less'
// import Link from 'next/link'
import MPButton from '../../MPButton'
import Down from '../../MPNewPostCard/common/images/direction-up.svg'
// import MPIcon from '../../MPIcon'
import { useWindowResize } from '../../../common/func'
import MPIcon from '../../MPIcon'
import { defaultImage } from '../../../common/constant'
import DefaultDrawer from '../../DefaultDrawer'
import Link from 'next/link'
import { useRouter } from 'next/router'

interface IF_HospitalInfo {
  data: any
  onWarning: any
  onFinish: any
  features: any[]
  onBooking: any
  partnerSlug: string
  openFeature: any
  featuresDrawerVisible: boolean
  setFeaturesDrawerVisible: any
}

const HospitalInfo = ({
  data,
  onWarning,
  onFinish,
  features,
  onBooking,
  partnerSlug,
  openFeature,
  featuresDrawerVisible,
  setFeaturesDrawerVisible
}: IF_HospitalInfo) => {
  const router = useRouter()
  const isMobile = useWindowResize(576)
  const { partnerInfo = {}, hospitalDescription } = data
  const rating = Number(hospitalDescription?.rating)
  const trigger = !isMobile ? 'hover' : 'click'
  const [Visible, setVisible] = useState(false)
  const [selectedData, setSelectedData] = useState<any>()
  const [height, setHeight] = useState(0)
  const [showFull, setShowFull] = useState(true)
  const [drawerCashBack, setDrawerCashBack] = useState(false)

  const checkSelect = (item: any) => {
    if (item.feature?.disabled) {
      onWarning({
        message: item.feature?.message,
        feature: item.feature,
        index: item.index
      })
    } else if (item.feature?.warningMessage) {
      setSelectedData(item)
    } else {
      onFinish({
        message: 'Thành công',
        feature: item.feature,
        index: item.index
      })
    }
  }
  const heightRef = React.useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (
      heightRef?.current?.offsetHeight &&
      heightRef?.current?.offsetHeight < 800
    ) {
      setShowFull(true)
    }
  }, [])
  const elementRef: any = useRef(null)

  useLayoutEffect(() => {
    setHeight(elementRef.current.getBoundingClientRect().height)
  }, [])

  const onShow = (e: any) => {
    e.stopPropagation()
    if (trigger === 'click') {
      setDrawerCashBack(true)
    }
  }
  const onHidden = (e: any) => {
    e.stopPropagation()
    setDrawerCashBack(false)
  }

  const renderBanner = () => {
    if (hospitalDescription?.bannerLink) {
      return (
        <Link href={hospitalDescription?.bannerLink}>
          <a target='_blank'>
            <Image
              src={hospitalDescription?.banner || errBanner}
              className={styles['imageBanner']}
              layout='fill'
              objectFit='cover'
              alt='Banner chi tiết bệnh viện'
            />
          </a>
        </Link>
      )
    } else {
      return (
        <Image
          src={hospitalDescription?.banner || errBanner}
          className={styles['imageBanner']}
          layout='fill'
          objectFit='cover'
          alt='Banner chi tiết bệnh viện'
        />
      )
    }
  }

  return (
    <div className={cx(styles['InfoWrapper'])}>
      <Row gutter={[40, 24]}>
        <Col
          xl={8}
          sm={24}
          xs={24}
          className={cx(styles['firstWraps'], styles['item1'])}
        >
          {partnerInfo?.isCashBack && (
            <div className={styles['tagCashBack']} onClick={onShow}>
              <Popover
                showArrow={true}
                overlayClassName={styles['popoverCashBack']}
                overlayInnerStyle={{ width: 510 }}
                content={
                  partnerInfo?.popup?.content && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: partnerInfo?.popup?.content
                      }}
                    />
                  )
                }
                onOpenChange={(visible) => {
                  if (!isMobile && visible) {
                    setVisible(true)
                  } else {
                    setVisible(false)
                  }
                }}
                open={Visible}
                placement='bottomLeft'
              >
                Hoàn tiền
              </Popover>
            </div>
          )}
          <div className={styles['logo']}>
            <span className={styles['imageLogo']}>
              <Image
                src={partnerInfo?.image || defaultImage}
                alt={partnerInfo?.name}
                width={150}
                height={156}
                objectFit='contain'
                layout='responsive'
              />
            </span>
            <div className={styles['logo_name']}>
              <h1 className={styles['name']}>
                {partnerInfo.name}
                {partnerInfo?.listingPackagePaid && (
                  <HiCheckBadge color='#0097FF' size={18} />
                )}
              </h1>
              <p
                className={cx(styles['rating'], !rating && styles['ratingOff'])}
              >
                ({rating || 'Chưa đánh giá'})
                <Rate
                  disabled
                  allowHalf
                  defaultValue={rating}
                  style={{
                    fontSize: 18,
                    marginLeft: 4,
                    color: '#FFB54A'
                  }}
                />
              </p>
            </div>
          </div>
          <hr className={styles['line']} />
          <div className={styles['info']}>
            <div className={styles['infoItem']}>
              <Space align='start' style={{ marginLeft: '1px' }}>
                <HiOutlineLocationMarker
                  color='#FFB54A'
                  size={22}
                  className={styles['icon']}
                  style={{ marginLeft: '-1px' }}
                />
                <h5>{partnerInfo.address || 'Đang cập nhật'}</h5>
              </Space>
            </div>

            <div className={styles['infoItem']}>
              <Space align='start'>
                <HiOutlineClock
                  color='#FFB54A'
                  size={22}
                  className={styles['icon']}
                />
                <h5>{hospitalDescription?.workingTime || 'Đang cập nhật'}</h5>
              </Space>
            </div>
            <div className={styles['infoItem']}>
              <Space align='start'>
                <MPIcon name='PhoneNew' size={16} />
                <h5>
                  Tổng đài đặt khám nhanh:{' '}
                  <a
                    href={
                      hospitalDescription?.hotlineMedpro
                        ? `tel:${data?.hotlineMedpro?.replace(/\D/g, '')}`
                        : 'tel:19002115'
                    }
                  >
                    {hospitalDescription?.hotlineMedpro || '19002115'}
                  </a>
                </h5>
              </Space>
            </div>
            <div className={styles['booking']}>
              <MPButton
                onClick={() => onBooking(partnerInfo)}
                className={styles['button']}
              >
                Đặt khám ngay
              </MPButton>
            </div>
          </div>
        </Col>
        <Col
          xl={16}
          sm={24}
          xs={24}
          className={cx(styles['slider'], styles['item2'])}
          order={1}
        >
          <Carousel {...settings}>
            {hospitalDescription?.images?.map((item: any, index: any) => {
              return (
                <div key={index}>
                  <figure>
                    <Image
                      src={item}
                      alt={`Chi tiết bệnh viện`}
                      width={780}
                      height={490}
                      layout='responsive'
                      className={styles['image']}
                      objectFit='fill'
                    />
                  </figure>
                </div>
              )
            })}
          </Carousel>
        </Col>
        <Col
          xl={8}
          sm={24}
          xs={24}
          className={cx(styles['item'], styles['item3'])}
        >
          {renderBanner()}
        </Col>
        <Col xl={16} sm={24} xs={24} className={styles['item4']}>
          {size(features) > 0 && (
            <div className={styles['service']}>
              <h2>Các dịch vụ</h2>
              <MPNewServiceHeader
                data={features}
                handleClick={checkSelect}
                limit={4}
              />
            </div>
          )}
        </Col>
        <Col
          xl={8}
          sm={24}
          xs={24}
          className={cx(styles['leftWrap'], styles['item6'])}
        >
          <div
            ref={elementRef}
            style={{ paddingLeft: '10px', paddingRight: '10px' }}
          >
            <Row gutter={[20, 20]}>
              <Col span={24} className={styles['description']}>
                <h3>Mô tả</h3>
                <p>{hospitalDescription?.description || 'Đang cập nhật'}</p>
              </Col>

              <Col span={24} className={styles['map']}>
                <iframe
                  id='map-hospital'
                  src={partnerInfo.googleMap}
                  loading={'lazy'}
                  title='Hopital Map'
                />
              </Col>
            </Row>
          </div>
        </Col>
        <Col
          xl={16}
          sm={24}
          xs={24}
          className={cx(styles['secondWraps'], styles['item5'])}
        >
          <div
            className={cx(styles['introduce'])}
            style={{
              height: showFull
                ? `${height > 900 ? height + 16 : height + 40}px`
                : '100%'
            }}
          >
            <div
              ref={heightRef}
              className={cx(
                styles['paragraph'],
                isMobile && showFull && styles['hiddenContent']
              )}
              dangerouslySetInnerHTML={{
                __html: hospitalDescription?.body || 'Đang cập nhật'
              }}
            />
            {isMobile && showFull && (
              <div
                onClick={() => {
                  setShowFull(false)
                }}
                className={styles['viewMore']}
              >
                Xem tiếp
                <div className={styles['image']}>
                  <Image
                    src={Down}
                    width={24}
                    height={24}
                    alt='Icon Down'
                    layout='fixed'
                  />
                </div>
              </div>
            )}
          </div>
        </Col>
      </Row>
      <OkModal
        style={{ top: '30%' }}
        title={'Thông báo'}
        open={!!selectedData}
        onOk={() =>
          onFinish({
            message: 'Thành công',
            feature: selectedData.feature,
            index: selectedData.index
          })
        }
        onCancel={() => setSelectedData(undefined)}
      >
        <div
          dangerouslySetInnerHTML={{
            __html: selectedData?.feature?.warningMessage || ''
          }}
        />
      </OkModal>
      {
        <DefaultDrawer
          title={hospitalDescription?.popup?.title || 'Thông tin hoàn tiền'}
          className={styles['modalCashBack']}
          open={drawerCashBack}
          onClose={onHidden}
          children={
            <div
              dangerouslySetInnerHTML={{
                __html: partnerInfo?.popup?.content
              }}
            />
          }
          height={'calc(70%)'}
          style={{ zIndex: 999999999 }}
        />
      }
      {featuresDrawerVisible && (
        <DefaultDrawer
          onClose={() => {
            router.replace({ pathname: `/${partnerSlug}` }, undefined, {
              shallow: false
            })
            setFeaturesDrawerVisible(false)
          }}
          open={featuresDrawerVisible}
          title='Chọn hình thức đặt khám'
          height='maxContent'
          className={styles['mobileFeature']}
        >
          <div className={styles['title']}>{openFeature?.item?.name}</div>
          {features?.map((item, index) => {
            return (
              <div
                className={styles['card']}
                key={index}
                onClick={() => checkSelect({ feature: item, index })}
              >
                {item?.image && (
                  <figure className={styles['cardImage']}>
                    <img src={item?.image} width={18} height={18} alt={''} />
                  </figure>
                )}
                <div className={styles['name']}>{item?.name}</div>
              </div>
            )
          })}
        </DefaultDrawer>
      )}
    </div>
  )
}
export const settings: CarouselProps = {
  dots: true,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 4000,
  speed: 1000,
  slidesToShow: 1,
  slidesToScroll: 1,
  arrows: true,
  prevArrow: (
    <Button
      type='ghost'
      className={styles['buttonSlide']}
      icon={<MdOutlineKeyboardArrowLeft size={24} fill='#030303' />}
    />
  ),
  nextArrow: (
    <Button
      type='ghost'
      className={styles['buttonSlide']}
      icon={<MdOutlineKeyboardArrowRight size={24} fill='#030303' />}
    />
  )
}
export default HospitalInfo
