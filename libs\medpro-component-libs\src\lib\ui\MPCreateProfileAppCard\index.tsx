import * as React from 'react'
import { useEffect, useState } from 'react'
import styles from './styles.module.less'
// import Image from 'next/image'
// import scanIcon from './common/scanIcon.svg'
// import line from './common/line.svg'
import { Col, Form, Modal, Row } from 'antd'
import { handleDetails } from './common/handleDetails'
import cx from 'classnames'
import MPButton from '../MPButton'
import SexSelectDrawer from '../common/SexSelectDrawer'
import moment from 'moment'
import MobileSelectDrawer from '../common/MobileSelectDrawer'
import { BiLeftArrowAlt } from 'react-icons/bi'
import { useRouter } from 'next/router'

export interface Props {
  province: any[]
  handleSubmit: (values: any) => void
  showPopup: boolean
  idForUpdate: string
  partnerId?: string
}

const MPCreateProfileAppCard = ({
  province,
  handleSubmit,
  showPopup,
  idForUpdate,
  partnerId
}: Props) => {
  const router = useRouter()
  const [dataSelect, setDataSelect] = useState<any>({})
  const [openPickSex, setOpenPickSex] = useState(false)
  const [openPickCity, setOpenPickCity] = useState(false)
  const [age, setAge] = useState(-1)

  const [form] = Form.useForm()

  const [disabledBtn, setDisabledBtn] = useState(true)
  const [showNotePopup, setShowNotePopup] = useState(showPopup)

  useEffect(() => {
    setShowNotePopup(showPopup)
  }, [showPopup])

  const handleFormChange = () => {
    const valueForm = form.getFieldsValue()
    if (partnerId === 'bvmathcm' && age < 14) {
      delete valueForm.cmnd
    }
    const values = Object.values(valueForm)

    const hasErrors = values.some((item) => item === undefined || item === '')
    setDisabledBtn(hasErrors)
  }

  const openSelect = (field: any) => {
    switch (field.id) {
      case 'sex':
        setOpenPickSex(true)
        return
      case 'city_id':
        setDataSelect({ ...field })
        setOpenPickCity(true)
        return
      default:
        return
    }
  }

  const onFinish = (values: any) => {
    // console.log(values)
    const day = values.birthdate.split('/')[0]
    const month = values.birthdate.split('/')[1]
    const year = values.birthdate.split('/')[2]

    const data = {
      ...values,
      birthdate: moment(`${year}-${month}-${day}`, 'YYYY-MM-DD')
        .utc(true)
        .toISOString()
    }
    handleSubmit(data)
  }

  const toggleSexDrawer = () => {
    setOpenPickSex((prev) => !prev)
  }

  const toggleCityDrawer = () => {
    setOpenPickCity((prev) => !prev)
  }

  const toggleAge = (value: string) => {
    const isValid = moment(value, 'DD/MM/YYYY', true).isValid()
    if (isValid) {
      setAge(moment().diff(moment(value, 'DD/MM/YYYY'), 'years'))
    }
  }

  return (
    <div className={styles['main']}>
      <div className={styles['headerTitle']}>
        <BiLeftArrowAlt
          size={24}
          onClick={() => router.back()}
          className={styles['headerSide']}
        />
        <p>Tạo hồ sơ</p>
        <div className={styles['headerSide']}></div>
      </div>
      {/* <div className={styles['scanBox']}>
        <MPButton type='default' className={styles['scanBtn']}>
          <Image src={scanIcon} alt='scan' />
          Quét mã BHYT/ CCCD
        </MPButton>
        <div className={styles['separate']}>
          <Image src={line} alt='scan' />
          <span>Hoặc nhập</span>
          <Image src={line} alt='scan' />
        </div>
      </div> */}
      <Form
        form={form}
        layout='vertical'
        initialValues={{ nation: 'medpro_1' }}
        onFinish={onFinish}
        onFieldsChange={handleFormChange}
        className={styles['listInfo']}
      >
        <Row gutter={12}>
          {handleDetails(
            openSelect,
            province,
            form,
            age,
            partnerId,
            toggleAge
          ).map((item, index) => {
            return (
              !item.hidden && (
                <Col key={index} span={item.width === '100%' ? 24 : 12}>
                  <div
                    className={cx(
                      styles['inputItem'],
                      item.id === 'birthdate' && styles['inputItemHalfLeft'],
                      item.id === 'sex' && styles['inputItemHalfRight']
                    )}
                  >
                    {item?.enter && item?.enter(item)}
                  </div>
                </Col>
              )
            )
          })}
        </Row>
        <div className={styles['footerBtn']}>
          <MPButton
            className={cx({
              [styles['btnSubmit']]: true,
              [styles['disabledBtn']]: disabledBtn
            })}
            type='primary'
            htmlType='submit'
            full='true'
            disabled={disabledBtn}
          >
            Tạo mới
          </MPButton>
        </div>
      </Form>

      {openPickSex && (
        <SexSelectDrawer
          isOpen={openPickSex}
          toggleDrawer={toggleSexDrawer}
          form={form}
          handleFormChange={handleFormChange}
        />
      )}
      {openPickCity && (
        <MobileSelectDrawer
          isOpen={openPickCity}
          toggleDrawer={toggleCityDrawer}
          form={form}
          handleFormChange={handleFormChange}
          dataSelect={dataSelect}
        />
      )}
      {showNotePopup && (
        <Modal
          title={'Lưu ý'}
          open={showNotePopup}
          footer={null}
          centered
          closable={false}
          // onCancel={() => setShowNotePopup((prev) => !prev)}
          className={styles['notePopup']}
        >
          <p>
            Bạn cần bổ sung thêm thông tin theo yêu cầu của cơ sở y tế. Bạn muốn
            hoàn tất thông tin này hay để sau khi đặt khám thành công?
          </p>
          <div className={styles['btnContainer']}>
            <MPButton
              type='default'
              onClick={() => {
                router.push({
                  pathname: '/cap-nhat-thong-tin',
                  query: {
                    ...router.query,
                    id: idForUpdate
                  }
                })
              }}
            >
              Hoàn tất thông tin
            </MPButton>
            <MPButton
              onClick={() => {
                router.push({
                  pathname: '/chon-lich-kham',
                  query: {
                    ...router.query,
                    step: 'chon-ho-so'
                  }
                })
              }}
              type='primary'
            >
              Để sau
            </MPButton>
          </div>
        </Modal>
      )}
    </div>
  )
}

export default MPCreateProfileAppCard
