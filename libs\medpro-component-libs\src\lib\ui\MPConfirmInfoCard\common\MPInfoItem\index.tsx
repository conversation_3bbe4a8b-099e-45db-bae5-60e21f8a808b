import { get } from 'lodash'
import moment from 'moment'
import React from 'react'
import { getFormatMoney, getTranformPrice } from '../../../../common/func'
import MPButton from '../../../MPButton'
import styles from './../../styles.module.less'
import { RiDeleteBin6Line } from 'react-icons/ri'
import classNames from 'classnames'
interface MPInfoItemProps {
  index: number
  item: any
  isRepayment?: boolean
  handleDeleteBooking: (item: any) => void
  data?:any
}

export const MPInfoItem = ({
  item,
  isRepayment,
  handleDeleteBooking,
  index,
  data
}: MPInfoItemProps) => {
  const time = isRepayment
    ? moment(item.date).format('HH:mm')
    : `${item.time?.startTime} - ${item.time?.endTime}`
  const date = moment(item.date).format('DD/MM/YYYY')
  const advance = get(item, 'service.advanced', 0)
  return (
    <React.Fragment>
      <tr className={styles['serviceDetail']}>
        <td className={styles['stt']}>
          <b>{index + 1}</b>
        </td>
        {data.subject.isHasSubject && (
          <td className={styles['td_sub']}>
            {item.subject?.name && item.subject?.name}
          </td>
        )}

        {data.service.isHasService && (
          <td className={styles['td_sub']}>{item?.service?.name}</td>
        )}
        {data.doctor.isHasDoctor && (
          <td className={styles['td_sub']}>{item.doctor?.name}</td>
        )}
        {data.room.isHasRoom && (
          <td className={styles['td_sub']}>{item.room?.name}</td>
        )}
        <td className={classNames(styles['td_sub'], styles['time'])}>
          {item.date ? (
            <>
              <p>{time}</p>
              <p>{date}</p>
            </>
          ) : (
            'Chờ cập nhật'
          )}
        </td>
        <td
          className={classNames(
            styles['td_sub'],
            !item.service?.displayDetail && styles['money']
          )}
        >
          {getTranformPrice({
            displayDetail: item.service?.displayDetail,
            price:
              getFormatMoney(
                item.service?.price - get(item, 'service.advanced', 0)
              ) + ' đ'
          })}
        </td>
        {!!advance && (
          <td className={classNames(styles['td_sub'], styles['money'])}>
            {getFormatMoney(advance) + ' đ'}
          </td>
        )}

        <td className={styles['action']}>
          {!isRepayment && (
            <MPButton
              type='default'
              className={styles['btnDelete']}
              onClick={() => handleDeleteBooking({ item, index })}
            >
              <RiDeleteBin6Line fill='#000' color='#000' />
            </MPButton>
          )}
        </td>
      </tr>
    </React.Fragment>
  )
}

export default MPInfoItem
