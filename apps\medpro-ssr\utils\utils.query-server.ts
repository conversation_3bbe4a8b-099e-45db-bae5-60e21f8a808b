import { server } from '../config/medproSdk'
import {
  HeadersParams,
  SearchDetailQuery,
  ServiceSearchQuery,
  GetDoctorTelemedQuery
} from 'medpro-sdk-v2'
import { getError } from './utils.error'
import { currentEnv } from '../config/envs'
import { size } from 'lodash'
import { LIST_CATE_NEWS } from '@medpro-libs/libs'
import axios from 'axios'
import { PartnerByFeatureInApp } from './utils.commonType'

export class ServerError extends Error {
  constructor(public data: any) {
    super()
  }
}

export const fetchPartnerInfoServer = async (headers: HeadersParams) => {
  try {
    const { data } = await server.partner.getPartnerInfo(headers)
    return data || []
  } catch (err) {
    console.log(err)
    throw new ServerError(
      getError(err, { message: 'Lỗi get partnerInfo server' })
    )
  }
}

export const fetchPartnerInfoBySlugServer = async (
  slug: string,
  headers?: HeadersParams
) => {
  try {
    const { data } = await server.partner.getPartnerInfoBySlug(
      { slug },
      headers
    )
    return data
  } catch (err) {
    if (err.response?.status !== 404) {
      console.log('err fetchPartnerInfoBySlugServer: ', err)
    }

    throw new ServerError(
      getError(err, { message: 'Lỗi fetchPartnerInfoBySlugServer' })
    )
  }
}

export const fetchExtraConfigServer = async (headers: HeadersParams) => {
  try {
    const { data } = await server.partnerConfig.getExtraConfig(headers)
    return data
  } catch (err) {
    console.log(err)
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchExtraConfigServer' })
    )
  }
}

export const fetchPartnerFeatureServer = async (
  query: any,
  headers: HeadersParams
) => {
  try {
    const { data } = await server.appId.getFeatureOfPartnerInApp(query, headers)
    return data
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi get partnerInfo server' })
    )
  }
}

export const fetchFeatureByPartner = async (headers: HeadersParams) => {
  try {
    const { data } = await server.feature.getFeatureByPartner(headers)
    return data
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi get FeatureByPartner server' })
    )
  }
}

export const fetchPartnerFeatureBySlugServer = async (
  headers: HeadersParams
) => {
  try {
    const { data } = await server.appId.getFeatureInApp(headers)
    return data
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi get fetchPartnerFeatureBySlugServer' })
    )
  }
}

export const getContentPages = async (pathName: any, headers: any) => {
  try {
    const { data } = await server.partner.getHospitalContentPage(
      { key: pathName },
      headers
    )
    return data
  } catch (error) {
    console.info('error_contentpages :>> ', error)
    return []
  }
}

export const getDetailRecruitment = async (params: any, headers: any) => {
  try {
    const { data } = await server.partner.getDetailRcruitment(
      params,
      headers
    )
    console.log('data', data)
    return data
  } catch (error) {
    console.info('error_contentpages :>> ', error)
    return []
  }
}

export const getListPlatform = async (headers: any) => {
  try {
    const { data } = await server.partner.getListPlatform(
      headers
    )
    console.log('data', data)
    return data
  } catch (error) {
    console.info('error_contentpages :>> ', error)
    return []
  }
}

export const getBookingGuide = async (params: any) => {
  try {
    const { data } = await server.partner.bookingGuide({
      appid: params.appId,
      locale: params.locale || 'vi'
    })
    return data
  } catch (error) {
    console.info('error getBookingGuide :>> ', error)
    return []
  }
}

export const getClinicGAReport = async () => {
  try {
    const { data } = await server.partner.getClinicGAReport()
    return data
  } catch (error) {
    console.info('error getClinicGAReport :>> ', getError(error))
    return []
  }
}

export const fetchDataNewsAndEvent = async (
  partnerId?: string,
  pageIndex?: number,
  limit?: number
) => {
  try {
    const url =
      currentEnv.API_CMS +
      `/posts?&categories.slug=tin-tuc&_start=${
        (pageIndex - 1) * limit
      }&_limit=${
        limit || 8
      }&_sort=created_at:desc&partners_contains=${partnerId}`

    const data = await fetch(url)
    return await data.json()
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchDataNewsAndEvent' })
    )
  }
}

export const totalLenghtNewsAndEvent = async (partnerId?: string) => {
  try {
    const url =
      currentEnv.API_CMS +
      `/posts?&categories.slug=tin-tuc&partners_contains=${partnerId}`
    const data = await fetch(url)
    const rs = await data.json()
    return size(rs)
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi get partnerInfo server' })
    )
  }
}

export const getBannerNewsAndEvent = async (limit?: number, appId?: string) => {
  try {
    const pathLimit = limit ? `&_limit=${limit}` : ''
    const partners = appId ? `&partners=${appId}` : ''
    const url =
      currentEnv.API_CMS + `/posts?_sort=created_at:DESC${pathLimit}${partners}`
    const data = await fetch(url)
    const rs = await data.json()
    return rs
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi get partnerInfo server' })
    )
  }
}

export const fetchNewsCategory = async ({
  limit = 12,
  page = 1,
  partnerId,
  cate = ''
}: any) => {
  try {
    const urlc = new URL(currentEnv.API_CMS)
    urlc.pathname = '/posts'
    cate && urlc.searchParams.append('subcategories.slug', cate)
    urlc.searchParams.append('_start', `${(page - 1) * limit}`)
    urlc.searchParams.append('_limit', limit)
    urlc.searchParams.append('_sort', `created_at:DESC`)
    // cate && urlc.searchParams.append('cate', cate)
    partnerId && urlc.searchParams.append('partners', partnerId)

    const data = await fetch(urlc)
    return data.json()
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi get partnerInfo server' })
    )
  }
}
export const fetchCateNewsCount = async ({ cate = '' }: any) => {
  try {
    const urlc = new URL(currentEnv.API_CMS)
    urlc.pathname = '/posts/count'
    cate && urlc.searchParams.append('subcategories.slug', cate)
    // cate && urlc.searchParams.append('cate', cate)
    const data = await fetch(urlc)
    return data.json()
  } catch (err) {
    throw new ServerError(
      getError(err, { message: 'Lỗi get partnerInfo server' })
    )
  }
}
export const fetchNewsCategoryAndGroup = async ({
  limit = 12,
  page = 1,
  partnerId,
  cate = ''
}: any) => {
  const news = await fetchNewsCategory({
    partnerId,
    page,
    limit,
    cate
  })

  return { cate, news }
}

export const getNewsHomePageServer = async ({ appId }: any) => {
  try {
    return await Promise.all([
      fetchNewsCategoryAndGroup({
        partnerId: appId,
        page: 1,
        limit: 1,
        cate: LIST_CATE_NEWS[1]
      }),
      fetchNewsCategoryAndGroup({
        partnerId: appId,
        page: 1,
        limit: 2,
        cate: LIST_CATE_NEWS[2]
      }),
      fetchNewsCategoryAndGroup({
        partnerId: appId,
        page: 1,
        limit: 2,
        cate: LIST_CATE_NEWS[3]
      })
    ])
  } catch (err) {
    console.error('err getNewHomePageServer: ', err)
  }
}

export const fetchDetailNews = async (slug: string) => {
  try {
    const url = currentEnv.API_CMS + `/posts?slug=${slug}`
    const data = await fetch(url)
    return await data.json()
  } catch (err) {
    throw new ServerError(
      getError(err, { message: `Lỗi get chi tiết tin tức server ${slug}` })
    )
  }
}

export const fetchSchemaNews = async (slug: string) => {
  try {
    const url = currentEnv.API_BE + `/schema-news/${slug}`
    const res = await axios.get(url)
    return res.data
  } catch (err) {
    throw new ServerError(
      getError(err, { message: `Lỗi get chi tiết tin tức server ${slug}` })
    )
  }
}

export const fetchIntroduct = async ({ categoryId }: any) => {
  try {
    const url = `https://api-backoffice-beta.medpro.com.vn/cauhoi?categoryId=${categoryId}`
    const data = await fetch(url)
    return await data.json()
  } catch (err) {
    throw new ServerError(
      getError(err, { message: `Lỗi get hướng dẫn server ${categoryId}` })
    )
  }
}
export const fetchIntroductTitle = async () => {
  try {
    const url = 'https://api-backoffice-beta.medpro.com.vn/danhmuc/medpro'
    const data = await fetch(url)
    return await data.json()
  } catch (err) {
    throw new ServerError(
      getError(err, { message: `Lỗi get title hướng dẫn server` })
    )
  }
}
export const fetchInstructions = async () => {
  try {
    const { data } = await server.partner.getInstruction()
    return data
  } catch (error) {
    console.info('error_fetchInstructions :>> ', error)
    return []
  }
}

export const fetchInstructionProcess = async (
  query?: any,
  headers?: HeadersParams
) => {
  try {
    const { data } = await server.partner.getInstructionProcess(query, headers)
    return data
  } catch (error) {
    console.info('error_fetchInstructions :>> ', error)
    return []
  }
}

export const fetchHospitalDescriptionServer = async (slug: string) => {
  try {
    const { data } = await server.partner.getHospitalDescription(slug)
    return data
  } catch (err) {
    console.log('Lỗi fetchHospitalDescriptionServer', err)
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchHospitalDescriptionServer' })
    )
  }
}

export const fetchHospitalsV6Server = async (): Promise<any[]> => {
  try {
    const { data } = await server.partner.getHospitalListByAppIdV6()
    return data
  } catch (err) {
    console.log('Lỗi fetchHospitalsV6Server', err)
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchHospitalsV6Server' })
    )
  }
}

export const fetchPackagesDetail = async (
  query: SearchDetailQuery,
  header?: HeadersParams
) => {
  try {
    const { data, config } = await server.searchService.getPackageDetail(
      query,
      header
    )

    return data || {}
  } catch (err) {
    console.log('Lỗi fetchPackageListServer', err.toJSON ? err.toJSON() : err)
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchPackageListServer' })
    )
  }
}
export const fetchDoctorDetail = async (
  query: SearchDetailQuery,
  header?: HeadersParams
) => {
  try {
    const { data, config } = await server.doctor.doctorDetail(query, header)

    return data
  } catch (err) {
    console.log('Lỗi fetchDoctorDetail', err.toJSON ? err.toJSON() : err)
    throw new ServerError(getError(err, { message: 'Lỗi fetchDoctorDetail' }))
  }
}
export const fetchPackagesServer = async (
  params: ServiceSearchQuery,
  headers?: HeadersParams
) => {
  try {
    const { data } = await server.searchService.getPackages(
      {
        ...params,
        category: 'package'
      },
      headers
    )

    return data
  } catch (err) {
    console.log('Lỗi fetchPackageListServer', err.toJSON ? err.toJSON() : err)
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchPackageListServer' })
    )
  }
}

export const fetchListDoctorServer = async (
  query: ServiceSearchQuery,
  header?: HeadersParams
) => {
  try {
    const { data } = await server.doctor.doctor(query, header)
    return data
  } catch (err) {
    console.log('Lỗi fetchListDoctorServer', err.toJSON ? err.toJSON() : err)
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchListDoctorServer' })
    )
  }
}

export const fetchSearchKeyWords = async () => {
  try {
    const { data } = await server.searchService.searchKeyWords()

    return data || {}
  } catch (err) {
    console.log('Lỗi fetchSearchKeyWords', err.toJSON ? err.toJSON() : err)
    return {
      error: getError(err, { message: 'Lỗi fetchSearchKeyWords' })
    }
  }
}
export const fetchSearch = async (
  query: ServiceSearchQuery,
  header?: HeadersParams
) => {
  try {
    const { data, config } = await server.searchService.search(query, header)
    console.log('config', config)

    return data || {}
  } catch (err) {
    console.log('Lỗi fetchSearchKeyWords', err.toJSON ? err.toJSON() : err)
    return {
      error: getError(err, { message: 'Lỗi fetchSearchKeyWords' })
    }
  }
}
export const fetchDoctorsServer = async (slug: string) => {
  try {
    return []
  } catch (err) {
    console.log('Lỗi fetchPackageListServer', err)
    throw new ServerError(getError(err, { message: 'Lỗi fetchDoctorsServer' }))
  }
}

export const fetchDoctorDescriptionServer = async (slug: string) => {
  try {
    const { data } = await server.doctor.getDoctorDescription(slug)
    return data
  } catch (err) {
    console.log('Lỗi fetchDoctorDescriptionServer', err)
    throw new ServerError(
      getError(err, { message: 'Lỗi fetchDoctorDescriptionServer' })
    )
  }
}

export const fetchDeeplinkServer = async (slug: any) => {
  try {
    const { data } = await server.dynamicLink.getDoctorDynamicLink({ slug })
    return data
  } catch (err) {
    console.log('Lỗi fetchDeeplinkServer', err)
    // throw new ServerError(getError(err, { message: 'Lỗi fetchDeeplinkServer' }))
  }
}

export const fetchDoctorTelemedServer = async (
  query: GetDoctorTelemedQuery,
  header?: HeadersParams
) => {
  try {
    const { data } = await server.partner.getDoctorTelemed(query, header)
    return data
  } catch (err) {
    console.log('Lỗi fetchDoctorTelemedServer', err)
    // throw new ServerError(getError(err, { message: 'Lỗi fetchDeeplinkServer' }))
  }
}

export const fetchPartnerByFeatureInAppServer = async (
  query: PartnerByFeatureInApp,
  header?: HeadersParams
) => {
  try {
    const { data } = await server.appId.getPartnerByFeatureInApp(query, header)
    return data
  } catch (err) {
    console.log('Lỗi fetchPartnerByFeatureInAppServer', err)
    // throw new ServerError(getError(err, { message: 'Lỗi fetchDeeplinkServer' }))
  }
}

export const fetchPartnerInfoBySlugV2Server = async (
  slug: string,
  headers?: HeadersParams
) => {
  try {
    const { data } = await server.partner.getPartnerDetail(slug, headers)
    return data
  } catch (err) {
    if (err.response?.status !== 404) {
      console.log('err fetchPartnerInfoBySlugServer: ', err)
    }

    throw new ServerError(
      getError(err, { message: 'Lỗi fetchPartnerInfoBySlugServer' })
    )
  }
}
