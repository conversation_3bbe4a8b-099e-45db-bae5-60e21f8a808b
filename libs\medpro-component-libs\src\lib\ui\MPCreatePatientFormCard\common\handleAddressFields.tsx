import { Form, Input, Select } from 'antd'
import { get, size } from 'lodash'
import { Valid } from '../../../common/helper/valid'
import styles from './../styles.module.less'

const valid = new Valid()
const { Option } = Select

export const handleAddressFields = (
  data: any,
  country: string,
  isForeign: boolean
) => {
  const ignoreProperties = get(data, 'patient.propertyIgnoreUpdate', [])

  const locationFields = [
    {
      id: 'city_id',
      type: 'text',
      label: 'Tỉnh/Thành',
      placeholder: 'Chọn tỉnh thành',
      require: isForeign ? false : true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              isForeign ? {} : { required: require, validator: valid.province }
            ]}
            className={styles['selectItem']}
          >
            <Select
              size='large'
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                (option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.province) > 0 &&
                data?.province?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: isForeign ? true : false,
      width: ''
    },
    {
      id: 'district_id',
      type: 'text',
      label: 'Quận/Huyện',
      placeholder: 'Chọn quận huyện',
      require: isForeign ? false : true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              isForeign ? {} : { required: require, validator: valid.district }
            ]}
            className={styles['selectItem']}
          >
            <Select
              size='large'
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                (option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.district) > 0 &&
                data?.district?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: isForeign ? true : false,
      width: ''
    },
    {
      id: 'ward_id',
      type: 'text',
      label: 'Phường/Xã',
      placeholder: 'Chọn xã phường',
      require: isForeign ? false : true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            style={{ marginTop: 18 }}
            rules={[
              isForeign ? {} : { required: require, validator: valid.ward }
            ]}
            className={styles['selectItem']}
          >
            <Select
              size='large'
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                (option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.ward) > 0 &&
                data?.ward?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: isForeign ? true : false,
      width: ''
    },
    {
      id: 'address',
      type: 'text',
      label: (
        <div className={styles['boxAddress']}>
          <div>{handleRequireInput('Số nhà/Tên đường/Ấp thôn xóm', true)}</div>

          <p className={styles['sup']}>
            (không bao gồm tỉnh/thành, quận/huyện, phường/xã)
          </p>
        </div>
      ),
      placeholder: 'Nhập số nhà, tên đường, ấp thôn xóm,...',
      require: false,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={
              !isForeign
                ? handleRequireInput(label, require)
                : handleRequireInput('Địa chỉ lưu trú', require)
            }
            name={id}
            rules={[{ validator: valid.address }]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    }
  ]

  switch (country) {
    case 'VN':
    case 'VIE':
      return locationFields.map((field) => ({
        ...field,
        disabled: ignoreProperties.includes(field.id)
      }))
    default: {
      const findProvincesIndex = locationFields.findIndex(
        (field: { id: string }) => field.id === 'city_id'
      )
      const findWardsIndex = locationFields.findIndex(
        (field: { id: string }) => field.id === 'ward_id'
      )
      locationFields.splice(
        findProvincesIndex,
        findWardsIndex - findProvincesIndex + 1
      )

      return locationFields.map((field) => ({
        ...field,
        disabled: ignoreProperties.includes(field.id)
      }))
    }
  }
}
const handleRequireInput = (label: string, require: boolean) => {
  if (require) {
    return (
      <>
        {label} <sup className={styles['requireInput']}>*</sup>
      </>
    )
  }
  return <>{label}</>
}
