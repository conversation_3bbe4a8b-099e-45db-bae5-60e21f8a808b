import type { AxiosResponse } from 'axios';

import { Base } from '../../common/base';
import { Method } from '../../common/constants';
import { basicAuthRequest } from '../../common/utils';
import type { ClientOptions, HeadersParams } from '../../interfaces';
import { USER_DELETE_ACCOUNT, USER_DETAIL_INFO, USER_UPLOAD_AVATAR, USER_UPLOAD_AVATAR_NOT_AUTHEN, USER_CONTACT_US, CREATE_USER_BY_TOKEN } from './constants';

export interface IUser {
    uploadAvatar(data: any, headers?: HeadersParams): Promise<AxiosResponse>;

    submitContactUs(data: any, headers?: HeadersParams): Promise<AxiosResponse>;

    uploadAvatarNotAuthen(data: any, headers?: HeadersParams): Promise<AxiosResponse>;

    getMedproIdUserByToken(headers?: HeadersParams): Promise<AxiosResponse>;

    deleteAccount(headers?: HeadersParams): Promise<AxiosResponse>;

    createUserMomoByWalletId(data: any, headers?: HeadersParams): Promise<AxiosResponse>;
}

export class User extends Base implements IUser {
    constructor(options?: ClientOptions) {
        super(options);
    }

    getMedproIdUserByToken(headers?: HeadersParams): Promise<AxiosResponse> {
        return basicAuthRequest(this.api(USER_DETAIL_INFO), Method.GET, headers);
    }

    uploadAvatar(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
        return basicAuthRequest(
            this.api(USER_UPLOAD_AVATAR),
            Method.POST,
            {
                ...this.options,
                ...headers,
                file: true,
            },
            data,
        );
    }

    submitContactUs(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
        return basicAuthRequest(this.api(USER_CONTACT_US), Method.POST, { ...this.options, ...headers }, data);
    }

    uploadAvatarNotAuthen(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
        return basicAuthRequest(
            this.api(USER_UPLOAD_AVATAR_NOT_AUTHEN),
            Method.POST,
            {
                ...this.options,
                ...headers,
                file: true,
            },
            data,
        );
    }

    deleteAccount(headers?: HeadersParams): Promise<AxiosResponse> {
        return basicAuthRequest(
            this.api(USER_DELETE_ACCOUNT),
            Method.POST,
            {
                ...this.options,
                ...headers,
            },
            {},
        );
    }

    createUserMomoByWalletId(data:any, headers?: HeadersParams): Promise<AxiosResponse> {
        return basicAuthRequest(
            this.api(CREATE_USER_BY_TOKEN),
            Method.POST,
            {
                ...this.options,
                ...headers,
            },
            data,
        );
    }
}
