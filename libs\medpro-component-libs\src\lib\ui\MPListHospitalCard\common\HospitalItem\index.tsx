import { Popover, Rate } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import MPButton from '../../../MPButton'
import styles from './styles.module.less'
import { useWindowDimensions } from 'libs/medpro-component-libs/src/lib/hooks/useWindowDimesion'
import { useState } from 'react'
import DefaultDrawer from '../../../DefaultDrawer'
import { HiCheckBadge } from 'react-icons/hi2'
const imgSponsor = require('../../../MPDetailHealthServicesCard/img/sponsor.svg')
interface Props {
  data: any
  handleBooking: (item: any) => void
  handleViewMore: (item: any) => void
  onSelect: () => void
  itemSelect?: any
  isSelect?: boolean
}

export const HospitalItem = (props: Props) => {
  const { data, onSelect, isSelect } = props
  const router = useRouter()
  const imgDefault = require('./../images/default.svg')
  const rating = Number(
    data?.rating || data?.description?.rating || data?.rating
  )
  const { windowWidth } = useWindowDimensions()
  const [drawerCashBack, setDrawerCashBack] = useState(false)
  const [Visible, setVisible] = useState(false)
  const trigger = windowWidth > 576 ? 'hover' : 'click'
  const onShow = (e: any) => {
    e.stopPropagation()
    if (trigger === 'click') {
      setDrawerCashBack(true)
    }
  }
  const onHidden = (e: any) => {
    e.stopPropagation()
    setDrawerCashBack(false)
  }
  return (
    <div
      className={cx(styles['card'], isSelect && styles['boxHospital'])}
      onClick={() => onSelect()}
    >
      {data?.isCashBack && (
        <div className={styles['tagCashBack']} onClick={onShow}>
          <Popover
            showArrow={true}
            overlayClassName={styles['popoverCashBack']}
            overlayInnerStyle={{ width: 510 }}
            content={
              data?.popup?.content && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: data?.popup?.content
                  }}
                />
              )
            }
            onOpenChange={(visible) => {
              if (windowWidth > 576 && visible) {
                setVisible(true)
              } else {
                setVisible(false)
              }
            }}
            open={Visible}
            placement='bottomLeft'
          >
            Hoàn tiền
          </Popover>
        </div>
      )}
      <div className={styles['DetailInfo']}>
        <div className={styles['cardImage']}>
          <Image
            src={data?.image || data.imageUrl || imgDefault}
            width={90}
            height={90}
            objectFit='contain'
            alt={data.name || data.title}
            layout='responsive'
          />
        </div>
        <div className={styles['cardBody']}>
          <div className={styles['cardContent']}>
            {!router?.query?.type ? (
              <h3 className={styles['title']}>
                {data.name || data.title}
                {(data?.listingPackagePaid ||
                  data?.partner?.listingPackagePaid) && (
                  <HiCheckBadge color='#0097FF' size={20} />
                )}
              </h3>
            ) : (
              <h2 className={styles['title']}>
                {data.name || data.title}
                {(data?.listingPackagePaid ||
                  data?.partner?.listingPackagePaid) && (
                  <HiCheckBadge color='#0097FF' size={20} />
                )}
              </h2>
            )}
            {(data.address || data.hospitalAddress) && (
              <p className={styles['contentItem']}>
                <span>
                  <HiOutlineLocationMarker size={21} color='#858585' />
                </span>
                {data.address || data.hospitalAddress}
              </p>
            )}
            <p className={cx(styles['rating'], !rating && styles['ratingOff'])}>
              ({rating || 'Chưa đánh giá'})
              <Rate
                disabled
                allowHalf
                value={rating}
                style={{ fontSize: 18, marginLeft: 4, color: '#FFB54A' }}
              />
            </p>
            {data?.deliveryMessage && (
              <div className={styles['status']}>{data?.deliveryMessage}</div>
            )}
          </div>
          <div className={styles['desktop']}>
            <div className={styles['btnControl']}>
              {(data?.showPartnerInfo ||
                data?.description?.showPartnerInfo) && (
                <MPButton
                  onClick={(e) => {
                    e.stopPropagation()
                    props.handleViewMore(data)
                  }}
                  className={styles['btnView']}
                >
                  Xem chi tiết
                </MPButton>
              )}
              <MPButton
                onClick={(e) => {
                  e.stopPropagation()
                  props.handleBooking(data)
                }}
                className={cx(
                  styles['btnBooking'],
                  !data?.showPartnerInfo && styles['onlyBtn']
                )}
              >
                Đặt khám ngay
              </MPButton>
            </div>
          </div>
        </div>
      </div>
      <div className={styles['mobile']}>
        <div className={styles['btnControl']}>
          {(data?.showPartnerInfo || data?.description?.showPartnerInfo) && (
            <MPButton
              onClick={(e) => {
                e.stopPropagation()
                props.handleViewMore(data)
              }}
              className={styles['btnView']}
            >
              Xem chi tiết
            </MPButton>
          )}
          <MPButton
            onClick={(e) => {
              e.stopPropagation()
              props.handleBooking(data)
            }}
            className={cx(
              styles['btnBooking'],
              !(data?.showPartnerInfo || data?.description?.showPartnerInfo) &&
                styles['onlyBtn']
            )}
          >
            Đặt khám ngay
          </MPButton>
        </div>
      </div>
      {(data?.sponsored || data?.description?.sponsored) && (
        <div
          className={cx(
            styles['sponsor'],
            data?.isCashBack && styles['sponsor_cashback']
          )}
        >
          <Image
            src={imgSponsor}
            alt='Icon sponsor'
            width={50}
            height={58}
            objectFit='fill'
            layout='fixed'
          />
        </div>
      )}
      {
        <DefaultDrawer
          title={data?.popup?.title}
          className={styles['modalCashBack']}
          open={drawerCashBack}
          onClose={onHidden}
          children={
            <div dangerouslySetInnerHTML={{ __html: data?.popup?.content }} />
          }
          height={'calc(70%)'}
          style={{ zIndex: 999999999 }}
        />
      }
    </div>
  )
}

export default HospitalItem
