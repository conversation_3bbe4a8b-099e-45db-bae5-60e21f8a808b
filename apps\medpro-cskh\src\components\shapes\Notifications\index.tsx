import { MPNotification } from '@medpro-libs/libs'
import { PageRoutes } from '../../../../utils/PageRoutes'
import { useRouter } from 'next/router'
import React from 'react'
import { useDispatch } from 'react-redux'
import actionStore from '../../../../store/actionStore'
import { setMarkViewedNoti } from '../../../../store/notification/slice'

const Notification = ({ notiOfUser }: any) => {
  const router = useRouter()
  const dispatch = useDispatch()
  const onNextLink = (item: any) => {
    if (item.id) {
      dispatch(setMarkViewedNoti({ id: item.id }))
      // console.log('item', item)
      const transactionId = item?.eventData?.transactionId
      if (transactionId) {
        router.push({
          pathname: PageRoutes.booking.detail.path,
          query: { mpTransaction: transactionId }
        })
      }
    }
  }
  return <MPNotification data={notiOfUser} onNextLink={onNextLink} />
}

export default Notification
