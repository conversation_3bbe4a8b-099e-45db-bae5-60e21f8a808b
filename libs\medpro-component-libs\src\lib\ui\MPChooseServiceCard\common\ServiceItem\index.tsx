import { Col, Radio, RadioChangeEvent, Row, Space } from 'antd'
import cx from 'classnames'
import _, { List, size } from 'lodash'
import React, { useState } from 'react'
import { getFormatMoney, getTranformPrice } from '../../../../common/func'
import MPButton from '../../../MPButton'
import styles from './../../styles.module.less'
interface Props {
  service: any
  index: any
  serviceProps: any
  extraConfig: any
  handleSelectService: any
  handleDetailService: any
}

const ServiceItem = ({
  service,
  handleSelectService,
  handleDetailService,
  serviceProps,
  extraConfig,
  index
}: Props) => {
  const [addon, setAddon] = useState([]) as any[]
  const [haveBHYTUMC, setHaveBHYTUMC] = useState<number | undefined>(undefined)

  const radio: List<any> | null | undefined = []

  const fillterSVAddOn = _.filter(
    radio,
    (v) => v.idSV.includes(service?.detail.name) && v.idSV.includes('true')
  )

  const priceOfAddOn = fillterSVAddOn.reduce((init, { price }) => {
    return init + price
  }, 0)

  const handleChangeBHYT = (e: RadioChangeEvent, service: any) => {
    handleSelectService({
      service: {
        ...service,
        detail: {
          ...service.detail,
          popupType: 0
        }
      },
      extraData: {
        id: service?.detail.id,
        selectedBHYT: 'bhyt',
        typeInsurance: e.target.value,
        optionBHYT: e.target.value
      }
    })
  }

  const handleChangeAddon = (e: RadioChangeEvent, service: any, v?: any) => {
    const dataAddon = [...addon, { addon: v, checkAddon: e.target.value }]
    setAddon(dataAddon)
    if (size(dataAddon) === size(service?.detail.addonServices)) {
      handleSelectService({
        service: service,
        addonServices: dataAddon
      })
    }
  }
  return (
    <React.Fragment>
      {service?.detail && (
        <tr className={styles['serviceDetail']}>
          <td className={styles['stt']}>
            <b>{index + 1}</b>
          </td>
          <td className={styles['name']}>
            {
              <>
                <b>{service?.detail.name}</b>
                <div className={styles['subService']}>
                  {service?.detail.displaySchedule && (
                    <div className={styles['node_description']}>
                      Lịch khám: {service?.detail.displaySchedule}
                    </div>
                  )}
                  {service?.detail.bookingNote && (
                    <div className={styles['node_description']}>
                      ({service?.detail.bookingNote})
                    </div>
                  )}
                </div>
              </>
            }
          </td>
          <td className={styles['price']}>
            <p className={styles['salePrice']}>
              {getTranformPrice({
                displayDetail: service?.detail.displayDetail,
                price:
                  getFormatMoney(service?.detail.price + priceOfAddOn) + ' đ'
              })}
            </p>
            <p className={styles['originalPrice']}>
              {service?.detail?.originalPrice}
            </p>
          </td>
          <td className={styles['action']}>
            <div className={styles['groupButton']}>
              {Boolean(service?.detail?.popupType) && (
                <MPButton
                  type='default'
                  className={styles['chooseDetailButton']}
                  onClick={() => {
                    handleDetailService({
                      service: {
                        ...service,
                        detail: {
                          ...service.detail,
                          isPopupOk: true
                        }
                      }
                    })
                  }}
                >
                  Chi tiết
                </MPButton>
              )}

              <MPButton
                type='default'
                className={styles['chooseServiceButton']}
                onClick={() => {
                  handleSelectService({
                    service: {
                      ...service,
                      detail: {
                        ...service.detail,
                        isPopupOk: true,
                        popupType: 2
                      }
                    },
                    addonServices: addon,
                    extraData: {
                      id: service?.detail.id,
                      selectedBHYT: ''
                    }
                  })
                }}
              >
                Đặt khám ngay
              </MPButton>
            </div>
          </td>
        </tr>
      )}

      {serviceProps.checkAddonService &&
        serviceProps.idService === service?.detail.id && (
          <tr className={styles['childrenCheck']}>
            <td />
            <td colSpan={4}>
              <div>
                {service?.detail.addonServices.map((v: any, idx: number) => {
                  return (
                    <Row key={idx}>
                      <Col span={24} md={14}>
                        <b>+ {v.name}</b>
                      </Col>
                      <Col span={24} md={10} className={styles['inputRadio']}>
                        <form>
                          <Radio.Group
                            name='check_addon'
                            onChange={(e: any) =>
                              handleChangeAddon(e, service, v)
                            }
                          >
                            <Radio id='false' value={false}>
                              {' '}
                              <span>Không</span>
                            </Radio>
                            <Radio id='true' value={true}>
                              {' '}
                              <span>Có</span>
                            </Radio>
                          </Radio.Group>
                        </form>
                      </Col>
                    </Row>
                  )
                })}
                <p
                  className={
                    serviceProps.error?.id === serviceProps.idService
                      ? styles['error']
                      : styles['hiddenErr']
                  }
                >
                  <em>{serviceProps.error?.message}</em>
                </p>
              </div>
            </td>
          </tr>
        )}
      {serviceProps.checkBHYT && serviceProps.idService === service?.detail.id && (
        <tr className={cx(styles['checkBHYT'], styles['childrenCheck'])}>
          <td className={styles['stt']}></td>
          <td className={styles['name']}>
            <b>Bạn có đăng ký khám BHYT?</b>
          </td>
          <td colSpan={2} className={styles['inputRadio']}>
            <Radio.Group
              value={haveBHYTUMC}
              onChange={(e) => {
                e.preventDefault()
                if (
                  size(extraConfig?.BHYTOptions) > 0 &&
                  e.target.value === 1 &&
                  extraConfig.partnerId === 'umc'
                ) {
                  setHaveBHYTUMC(e.target.value)
                } else {
                  handleChangeBHYT(e, service)
                }
              }}
            >
              <Radio value={0}>
                <span>Không</span>
              </Radio>
              <Radio value={1}>
                <span>Có</span>
              </Radio>
            </Radio.Group>
          </td>
        </tr>
      )}
      {Boolean(haveBHYTUMC) && (
        <tr
          className={cx(styles['optionBHYT'], styles['animation_fadeInDown'])}
        >
          <td />
          <td colSpan={4}>
            <Radio.Group
              value={
                serviceProps.optionBHYT === 0 && haveBHYTUMC === 1
                  ? undefined
                  : serviceProps.optionBHYT
              }
              onChange={(e) => {
                e.preventDefault()
                handleChangeBHYT(e, service)
              }}
            >
              <Space direction='vertical'>
                {extraConfig?.BHYTOptions.map((item: any) => {
                  return (
                    <Radio value={item.value} key={item.id}>
                      <span>{item.title}</span>
                    </Radio>
                  )
                })}
              </Space>
            </Radio.Group>
          </td>
        </tr>
      )}
    </React.Fragment>
  )
}

export default ServiceItem
