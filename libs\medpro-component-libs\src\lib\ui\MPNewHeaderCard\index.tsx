/* eslint-disable jsx-a11y/accessible-emoji */
/* eslint-disable jsx-a11y/anchor-is-valid */
import { Badge, Dropdown, Menu, Skeleton } from 'antd'
import cx from 'classnames'
import { motion, useScroll } from 'framer-motion'
import { range, size } from 'lodash'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { AiFillCaretDown, AiOutlineCaretDown } from 'react-icons/ai'
import {
  FaFacebookF,
  FaTiktok,
  FaUser,
  FaUserAlt,
  FaYoutube
} from 'react-icons/fa'
import { MdOutlineSmartphone } from 'react-icons/md'
import MPButton from '../MPButton'
import HP from './common/images/hp.svg'
import { ListNoti } from '../MPHeaderCard/common/listNoti'
import MPIcon from '../MPIcon'
// import MPNewSearchCard from '../MPNewSearchCard'
import { MobileHeader } from './MobileHeader'
import { Information } from './common/information'
import styles from './styles.module.less'
import { PAGE_NONE_MINI_SEARCH } from '../../common/constant'
import MPSearch from '../../shapes/MPSearchTyping'

export interface NewHeaderProps {
  data: any[]
  user: any
  handleLogin: () => void
  handleLogout: () => void
  onClickNotificationItem: (value: any) => void
  onSearchDebounce: (value: any) => Promise<void>
  handleBookingSearch: ({ type, item }: any) => Promise<void>
  // downloadApp: any
  noti: any
  appInfo: any
  searchData: any
  [key: string]: any
  featureSelected: any
  cashBack: any
  behavior?: string
  countBooking?: number
  CSYT_DYNAMIC?: boolean
}

const MPNewHeaderCard = (props: NewHeaderProps) => {
  const LogoVN = require('./common/images/VN.svg')
  const LogoEN = require('./common/images/EN.png')
  const LogoKH = require('./common/images/KH.png')
  const iconZalo = require('./common/images/iconZalo.svg')

  const behaviorAnKhang = props.behavior === 'AnKhang'

  const [hidden, setHidden] = useState(false)
  const [hiddenCustom, setHiddenCustom] = useState(false)
  const [userInfoDropDown, setUserInfoDropDown] = useState(false)
  const [visibleInfo, setVisibleInfo] = useState(false)
  const headerRef = useRef<HTMLDivElement | null>(null)
  const [notiDropDown, setNotiDropDown] = useState(false)
  const [LayoutDynamic, setLayoutDynamic] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const router = useRouter()
  const { scrollY } = useScroll()
  const { logo } = props

  const handleScroll = () => {
    if (scrollY.get() > scrollY.getPrevious()) {
      setHidden(true)
    } else if (scrollY.get() < 90) {
      setHidden(false)
    }
  }

  const handleScroll_CSYT_DYNAMIC = () => {
    if (scrollY.get() > scrollY.getPrevious() && scrollY.get() > 1000) {
      setHiddenCustom(true)
      setHidden(true)
    } else if (scrollY.get() < 140) {
      setHiddenCustom(false)
      setHidden(false)
    }
  }

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    setLayoutDynamic(!!props?.CSYT_DYNAMIC)
    if (isMobile) {
      // Trên mobile: bỏ hiệu ứng scroll để tránh giật giao diện hoặc xử lý riêng nếu muốn
      setHidden(false)
      setHiddenCustom(false)
      return undefined
    }
    // Trên desktop: giữ logic cũ
    return scrollY.onChange(() =>
      props?.CSYT_DYNAMIC ? handleScroll_CSYT_DYNAMIC() : handleScroll()
    )
  }, [scrollY, props?.CSYT_DYNAMIC, isMobile])

  const variantsCustom = {
    visible: { opacity: 1, y: 0 },
    initial: { opacity: 0, y: -75, height: 0, padding: 0 },
    hidden: { opacity: 0, y: -25, height: 0, padding: 0 }
  }

  const variants = {
    visible: { opacity: 1, y: 0 },
    initial: { opacity: 0, y: -75, height: 0, padding: 0 },
    hidden: { opacity: 0, y: -25, height: 0, padding: 0 }
  }

  const hanldeChangeUserInfoDropDown = (visible: boolean) => {
    setUserInfoDropDown(visible)
  }
  const toggleModalInfo = () => {
    setVisibleInfo(!visibleInfo)
  }
  const hanldeChangeNotiDropDown = (visible: boolean) => {
    setNotiDropDown(visible)
  }
  const ListLanguage = [
    {
      id: 'VN',
      logo: LogoVN,
      name: 'Tiếng Việt',
      link: '#'
    },
    {
      id: 'EN',
      logo: LogoEN,
      name: 'English',
      link: 'https://medic.medpro.vn/medpro_en'
    },
    {
      id: 'KH',
      logo: LogoKH,
      name: 'Khmer',
      link: 'https://medic.medpro.vn/cambodia'
    }
  ]
  const menu = (
    <Menu className={styles['MenuLanguage']}>
      {ListLanguage.map((item: any) => {
        return (
          <Menu.Item
            key={item.id}
            id={item.id}
            className={cx(
              styles['ItemLanguage'],
              item.id === 'VN' && styles['isSelect']
            )}
          >
            <Link href={item.link}>
              <a
                className={styles.optionLanguage}
                target={item.id !== 'VN' ? '_blank' : '_self'}
                rel={item.id !== 'VN' ? 'nofollow' : ''}
              >
                <Image
                  src={item.logo}
                  alt={item.name}
                  className={styles['imageLanguage']}
                  layout='fixed'
                />
                {item.name}
              </a>
            </Link>
          </Menu.Item>
        )
      })}
    </Menu>
  )

  const renderHeader = () => {
    return (
      <>
        <div className={styles['logoHeader']}>
          <Link href={'/'}>
            <a>
              <Image
                src={logo}
                title='Logo Medpro'
                alt='Logo Medpro'
                layout='responsive'
                width={150}
                height={65}
                priority
              />
            </a>
          </Link>
        </div>
        <div className={styles['menu']}>
          {!behaviorAnKhang && (
            <motion.div
              animate={hidden ? 'hidden' : 'visible'}
              variants={variantsCustom}
              className={styles['header']}
            >
              <div className={styles['network']}>
                <div>
                  <Link
                    href={'https://www.tiktok.com/@medprovn/'}
                    className={styles['social']}
                  >
                    <a target='_blank' rel='nofollow'>
                      <div className={styles['icon']}>
                        <FaTiktok aria-label='Icon TikTok' size={16} />
                      </div>
                      Tiktok
                    </a>
                  </Link>
                </div>
                <div>
                  <Link
                    href={'https://www.facebook.com/www.medpro.vn'}
                    className={styles['social']}
                  >
                    <a target='_blank' rel='nofollow'>
                      <div className={styles['icon']}>
                        <FaFacebookF aria-label='Icon FaceBook' size={16} />
                      </div>
                      Facebook
                    </a>
                  </Link>
                </div>
                <div>
                  <Link
                    href={'https://zalo.me/4018184502979486994'}
                    className={styles['social']}
                  >
                    <a target='_blank' rel='nofollow'>
                      <div className={styles['icon']}>
                        <Image
                          className={styles['iconZalo']}
                          src={iconZalo}
                          alt='Icon Zalo'
                          width={18}
                          height={18}
                        />
                      </div>
                      Zalo
                    </a>
                  </Link>
                </div>
                <div>
                  <Link
                    href={'https://www.youtube.com/@medpro-datkhamnhanh'}
                    className={styles['social']}
                  >
                    <a target='_blank' rel='nofollow'>
                      <div className={styles['icon']}>
                        <FaYoutube aria-label='Icon Youtube' size={16} />
                      </div>
                      Youtube
                    </a>
                  </Link>
                </div>
              </div>
              <div className={styles.buttonWrapper}>
                {props.userToken && (
                  <div
                    id='dropNoti'
                    className={
                      styles[
                        props.noti.unreadCount > 0
                          ? 'activeNotification'
                          : 'dropNotification'
                      ]
                    }
                  >
                    <Dropdown
                      trigger={['click']}
                      overlay={
                        <ListNoti
                          list={props.noti.list}
                          notiDropDown={notiDropDown}
                          onClickNotificationItem={
                            props.onClickNotificationItem
                          }
                          hanldeChangeNotiDropDown={hanldeChangeNotiDropDown}
                        />
                      }
                      open={notiDropDown}
                      onOpenChange={hanldeChangeNotiDropDown}
                      getPopupContainer={() =>
                        document.getElementById('dropNoti') as HTMLElement
                      }
                      placement='bottom'
                    >
                      <div onClick={() => props.noti.getNotiOfUser()}>
                        <Badge
                          count={props.noti.unreadCount}
                          className={cx({
                            [styles.badgeNoti]: true,
                            [styles.badgeNotiHasNoti]:
                              props.noti.unreadCount > 0
                          })}
                        >
                          <MPIcon
                            aria-label='Icon Bell'
                            name='Bell'
                            fill={'#00b5f1'}
                          />
                        </Badge>
                      </div>
                    </Dropdown>
                  </div>
                )}
                <div className={styles['download']}>
                  <MPButton
                    onClick={() =>
                      router.push('/#download', undefined, {
                        shallow: true
                      })
                    }
                    className={styles['btnDownload']}
                    type='primary'
                  >
                    <MdOutlineSmartphone size={17} aria-label='Icon Phone' />
                    Tải ứng dụng
                  </MPButton>
                </div>
                <div>
                  {props.author.loading ? (
                    <Skeleton.Button
                      shape={'round'}
                      active={true}
                      style={{ width: 130 }}
                    />
                  ) : !props.userToken ? (
                    <MPButton
                      onClick={() => props.handleLogin()}
                      className={styles['btnUser']}
                      type='default'
                    >
                      <div className={styles['bg']}>
                        <div className={styles['text']}>
                          <FaUserAlt size={15} aria-label='Icon User' />
                          Tài khoản
                        </div>
                      </div>
                    </MPButton>
                  ) : (
                    <div id='dropInfo'>
                      <Dropdown
                        trigger={['click']}
                        getPopupContainer={() =>
                          document.getElementById('dropInfo') as HTMLElement
                        }
                        open={userInfoDropDown}
                        onOpenChange={hanldeChangeUserInfoDropDown}
                        overlay={
                          <Information
                            handleLogOut={props.handleLogout}
                            data={props.author.data?.fullname}
                            // partnerId={props.partnerId}
                            userInfoDropDown={userInfoDropDown}
                            toggleModalInfo={toggleModalInfo}
                            countBooking={props.countBooking}
                            hanldeChangeUserInfoDropDown={
                              hanldeChangeUserInfoDropDown
                            }
                          />
                        }
                        placement='bottom'
                      >
                        <MPButton
                          className={styles['btnUser']}
                          id='user-info'
                          type='default'
                        >
                          <div className={styles['bg']}>
                            <div className={styles['text']}>
                              <div className={styles['icon']}>
                                <FaUser size={15} color='#00b5f1' />
                              </div>
                              {props.author.data?.fullname}
                            </div>
                          </div>
                        </MPButton>
                      </Dropdown>
                    </div>
                  )}
                </div>
                <Dropdown overlay={menu} placement='bottomRight'>
                  <MPButton className={styles['btnLanguage']}>
                    <Image
                      src={LogoVN}
                      alt='VN'
                      className={styles['imageLanguage']}
                      layout='fixed'
                    />
                    <AiOutlineCaretDown />
                  </MPButton>
                </Dropdown>
              </div>
            </motion.div>
          )}
          <div
            className={cx(
              styles['body'],
              styles['behavior']
              // hiddenCustom && styles['hidden']
            )}
          >
            {PAGE_NONE_MINI_SEARCH.includes(
              router.pathname.split('/').pop() || '/'
            ) ? (
              <div className={styles['support']}>
                <div className={styles['iconCall']}>
                  <Image
                    src={HP}
                    layout='responsive'
                    width={40}
                    height={41}
                    alt='hotline'
                  />
                </div>
                <a href={'tel:19002115'} className={styles['titleSupport']}>
                  Hỗ trợ đặt khám
                  <p>1900 2115</p>
                </a>
              </div>
            ) : (
              <MPSearch
                miniSearch
                onSearchDebounce={props.onSearchDebounce}
                searchData={props.searchData}
                searching={props.searching}
                handleBookingSearch={props.handleBookingSearch}
              />
            )}
            <div className={styles['navbar']}>
              <ul>
                {props.data?.map((item: any) => (
                  <li key={item?.link}>
                    <div className={styles['item']}>
                      <Link href={item?.link}>{item.label}</Link>
                      {item?.children && size(item?.children) > 0 && (
                        <article className={styles['icon']}>
                          <AiFillCaretDown
                            size={15}
                            aria-label='Icon Caret Down'
                          />
                        </article>
                      )}
                      {item?.children && size(item?.children) > 0 && (
                        <div>
                          <ul>
                            {item?.children?.map((el: any, i: number) => {
                              return (
                                <Link href={el?.customUrl || el.link} key={i}>
                                  <a target={el?.customUrl && '_blank'}>
                                    <li key={i}>{el.label}</li>
                                  </a>
                                </Link>
                              )
                            })}
                          </ul>
                        </div>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <div className={styles.newHeader} ref={headerRef}>
        {LayoutDynamic ? (
          <motion.div
            animate={hiddenCustom ? 'hidden' : 'visible'}
            variants={variants}
            className={styles['newHeaderWrapper']}
          >
            {renderHeader()}
          </motion.div>
        ) : (
          <div className={styles['newHeaderWrapper']}>{renderHeader()}</div>
        )}
        {props.cashBack?.status && (
          <div className={styles['cashBackWrapper']}>
            <p className={styles['cashBackContent']}>
              {range(20).map((item: any, index: number) => (
                <span style={{ marginRight: 12 }} key={index}>
                  {props.cashBack?.adsContent}
                </span>
              ))}
            </p>
          </div>
        )}
      </div>
      <div className={styles['newMobileHeader']}>
        <MobileHeader
          menuHeader={props.data}
          author={props.author}
          totalNoti={props.noti.unreadCount}
          handleLogin={props.handleLogin}
          handleLogOut={props.handleLogout}
          logoWhite={logo}
          // downloadApp={props.downloadApp}
          userToken={props.userToken}
          onSearchDebounce={props.onSearchDebounce}
          handleBookingSearch={props.handleBookingSearch}
          searchData={props.searchData}
          searching={props.searching}
          appInfo={props.appInfo}
          featureSelected={props.featureSelected}
          cashBack={props.cashBack}
        />
      </div>
    </>
  )
}

export default MPNewHeaderCard
