import { <PERSON>Button, MPCompleteProfileApp } from '@medpro-libs/libs'
import * as React from 'react'
import { showErrorNotification } from '../../../../utils/utils.error'
import { openNotification } from '../../../../utils/utils.notification'
import moment from 'moment'
import {
  fetchCountries,
  fetchDistricts,
  fetchProvinces,
  fetchRelative,
  fetchWards,
  setBreadcrumb,
  setDistricts,
  setWards
} from '../../../../store/total/slice'
import { size } from 'lodash'
import { useDispatch } from 'react-redux'
import { useEffect, useMemo, useState } from 'react'
import { useAppSelector } from '../../../../store/hooks'
import { useRouter } from 'next/router'
import { patientActions } from '../../../../store/patient/patientSlice'
import { selectUpdatePatientForm } from '../../../../store/patient/patientSelector'
import { useFetchNation, useFetchProfession } from '../../../../hooks/query'
import client from '../../../../config/medproSdk'
import { Modal } from 'antd'
import styles from './styles.module.less'
import Link from 'next/link'
import _ from 'lodash'

export interface Props {}

export default function CompleteProfileApp(props: Props) {
  const router = useRouter()
  const bookingId = router.query.bookingId as string
  const patientId = router.query.id as string
  const partnerId = router.query.partnerId as string
  const transactionId = router.query.transactionId as string
  const prevPage = router.query?.prevPage as string
  const editProfile = router.query.editProfile as string
  const dispatch = useDispatch()
  const [showPopup, setShowPopup] = useState(false)
  const [phoneLocale, setPhoneLocale] = useState([])

  const countries = useAppSelector((s) => s.total.countries)
  const province = useAppSelector((s) => s.total.provinces)
  const district = useAppSelector((s) => s.total.districts)
  const ward = useAppSelector((s) => s.total.wards)
  const relative = useAppSelector((s) => s.total.relative)
  const patientUpdate = useAppSelector(selectUpdatePatientForm)

  const { data: profession, isLoading: loadingProfession } =
    useFetchProfession()
  const { data: nation, isLoading: loadingNation } = useFetchNation()

  useEffect(() => {
    if (!patientId) {
      router.push('/tao-moi-ho-so')
    } else {
      dispatch(patientActions.fetchUpdatePatient(patientId))
    }
  }, [patientId])

  useEffect(() => {
    cleanDistrictsAndWards()
    if (size(province) === 0) {
      dispatch(fetchProvinces({ country_code: '203' }))
    }

    if (size(countries) === 0) {
      dispatch(fetchCountries())
    }
    if (size(relative) === 0) {
      dispatch(fetchRelative())
    }
    fetchPhoneLocale()
    dispatch(setBreadcrumb([]))
  }, [])

  useEffect(() => {
    if (patientUpdate) {
      if (patientUpdate.city_id) {
        dispatch(fetchDistricts({ city_id: patientUpdate.city_id }))
      }

      if (patientUpdate.district_id) {
        dispatch(fetchWards({ district_id: patientUpdate.district_id }))
      }
    }
  }, [patientUpdate])

  function cleanDistrictsAndWards() {
    dispatch(setDistricts([]))
    dispatch(setWards([]))
  }

  const fetchPhoneLocale = async () => {
    try {
      const { data } = await client.patient.getPhoneLocale()
      if (_.isArray(data)) {
        setPhoneLocale(data)
      }
    } catch (err) {
      console.log(err)
    }
  }

  const onChangeAddress = (type: string, id: string) => {
    console.log(type, id)
    switch (type) {
      case 'district':
        dispatch(fetchDistricts({ city_id: id }))
        break
      case 'ward':
        dispatch(fetchWards({ district_id: id }))
        break
      default:
        break
    }
  }

  const onCompleteProfile = async (values: any) => {
    try {
      await client.patient.updatePatient({
        ...values,
        id: patientId,
        bookingId
      })
      if (transactionId) {
        setShowPopup(true)
      } else {
        openNotification('success', {
          message: 'Cập nhật thông tin thành công.'
        })

        if (prevPage) {
          router.push(prevPage)
        } else {
          router.push({
            pathname: '/chon-lich-kham',
            query: {
              ...router.query,
              step: 'chon-ho-so'
            }
          })
        }
      }
    } catch (err) {
      showErrorNotification(err)
    }
  }

  const dataNewPatient = useMemo(() => {
    let phonePrefix: string
    let phoneNumber: string
    let relativePhoneNumber: string
    const prefix = phoneLocale.filter(
      (item) => item.iso === patientUpdate?.mobileLocaleIso
    )[0]?.label

    const prefixRelative = phoneLocale.filter(
      (item) => item.iso === patientUpdate?.relation?.relativeMobileLocaleIso
    )[0]?.label

    const iso =
      phoneLocale.filter(
        (item) => item.iso === patientUpdate?.mobileLocaleIso
      )[0]?.iso || 'vi-VN'

    const relativeMobileLocaleIso =
      phoneLocale.filter(
        (item) => item.iso === patientUpdate?.relation?.relativeMobileLocaleIso
      )[0]?.iso || 'vi-VN'

    if (prefix) {
      phonePrefix = prefix
    } else {
      phonePrefix = '+84'
    }

    if (patientUpdate?.mobile?.startsWith(prefix)) {
      phoneNumber = patientUpdate?.mobile?.replace(prefix, '')
    } else {
      phoneNumber = patientUpdate?.mobile?.substring(1)
    }

    //relative
    if (patientUpdate?.relation?.relative_mobile?.startsWith(prefixRelative)) {
      relativePhoneNumber = patientUpdate?.relation?.relative_mobile?.replace(
        prefixRelative,
        ''
      )
    } else {
      relativePhoneNumber =
        patientUpdate?.relation?.relative_mobile?.substring(1)
    }

    return {
      profession,
      country: [],
      nation,
      province,
      district,
      ward,
      patient: {
        ...patientUpdate,
        prefix: phonePrefix,
        iso,
        phoneNumber: phoneNumber,
        relative_email: patientUpdate?.relation?.relative_email,
        relative_mobile: relativePhoneNumber,
        relative_name: patientUpdate?.relation?.relative_name,
        relative_type_id: patientUpdate?.relation?.relative_type_id,
        relativeMobileLocaleIso,
        cmnd: patientUpdate?.cmnd || '',
        insuranceCode: patientUpdate?.insuranceCode || '',
        email: patientUpdate?.email || ''
      },
      countries,
      relative: relative?.map((item) => {
        return {
          ...item,
          value: item.id,
          title: item.name
        }
      })
    }
  }, [patientUpdate, countries, phoneLocale])
  return (
    <>
      <MPCompleteProfileApp
        onSubmit={onCompleteProfile}
        data={dataNewPatient}
        onChangeAddress={onChangeAddress}
        phoneLocale={phoneLocale}
        district={district}
        ward={ward}
        partnerId={partnerId}
      />
      {showPopup && (
        <Modal
          title={'Thông báo'}
          open={showPopup}
          footer={null}
          centered
          onCancel={() => setShowPopup((prev) => !prev)}
          className={styles['notePopup']}
        >
          <p>
            Bạn đã cập nhật hồ sơ thành công. Xin vui lòng đến đúng giờ để quy
            trình khám được tiến hành một cách thuận lợi
          </p>
          <div className={styles['btnContainer']}>
            <MPButton
              type='default'
              onClick={() => {
                router.push({
                  pathname: 'chi-tiet-phieu-kham-benh',
                  query: {
                    transactionId: transactionId
                  }
                })
              }}
            >
              Xem lại phiếu khám
            </MPButton>

            <Link href={`/`}>
              <MPButton type='primary'>Về trang chủ</MPButton>
            </Link>
          </div>
        </Modal>
      )}
    </>
  )
}
