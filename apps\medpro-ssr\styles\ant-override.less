.ant-notification-notice {
  width: fit-content !important;
  max-width: 500px !important;
  padding: 12px 8px !important;
}

.ant-notification-notice-message-single-line-auto-margin {
  width: auto !important;
}

.custom-notification-success {
  min-width: max-content !important;
  background-color: #0eb429 !important;
  border-radius: 6px !important;
  .ant-notification-notice-message {
    font-weight: 400;
    font-size: 16px;
    padding: 0 !important;
  }
  .ant-notification-notice-description {
    display: none !important;
  }
}

.ant-notification-notice-content {
  .ant-notification-notice-with-icon {
    .ant-notification-notice-icon {
      display: none;
    }
  }
  .ant-notification-notice-message {
    color: white;
    padding: 5px;
    margin: 0 !important;
    padding-right: 5px !important;
  }
}
.ant-notification-notice-close {
  color: transparent !important;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
}
.ant-notification-notice-error {
  background-color: #e85546 !important;
}
.ant-notification-notice-info {
  background-color: #1da1f2 !important;
}
.ant-notification-notice-warning {
  background-color: #ff851b !important;
}
.ant-notification-notice-success {
  background-color: #1da1f2 !important;
}

.ant-notification-notice {
  &.primary {
    background-color: #ffffff !important;
    max-width: 500px !important;

    .ant-notification-notice-message {
      color: #00b5f1;
      padding-left: 15px;
      padding-right: 15px;
      font-weight: 500;
    }

    .ant-notification-notice-description {
      color: #003553;
      padding-left: 15px;
      padding-right: 15px;
      font-size: 15px;
    }
  }
}
