.container {
  max-width: 360px;
}
.printBillPage {
  background: #f6f6f6;
  // overflow-x: hidden;
  .attention {
    max-width: 360px;
    margin-bottom: 12px;
    background-color: #ffebec;
    border-radius: 12px;
    padding: 8px;
    gap: 6px;
    display: flex;
    .icon {
      min-width: 16px;
    }
    .content {
      font-size: 14px;
      font-weight: 400;
      line-height: 17px;
      color: #f5222d;
      margin-bottom: 0;
    }
  }
  ul {
    margin-block-start: 0;
    margin-block-end: 0;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 0;
  }
  .scanQRCode {
    width: 350px;
    margin-top: 50px;
    margin-left: auto;
    margin-right: auto;
  }
  .modalContentCode {
    text-align: center;
    margin-top: 20px;
    padding-bottom: 50px;
    font-size: 13px;
  }
  .printBill {
    max-width: 360px;
    margin: auto;
    padding: 3px 17px;
    background: #ffffff;
    border-radius: 16px;
    .printBillTitle {
      text-align: center;
      padding-top: 20px;
      line-height: 1.3rem;
      // border-top: 2px dashed #f0f2f5;
      color: #3e3e3e;
      position: relative;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: -15px;

        width: 28px;
        height: 28px;
        background-color: #f6f6f6;
        border-radius: 50%;
      }
      &::after {
        right: -34px;
      }
      &::before {
        left: -34px;
      }
      > article {
        font-size: 20px;
        text-transform: uppercase;
        margin-bottom: 10px;
      }
    }
    .printBillCode {
      margin: 20px 0;
    }
    .cancelMessage {
      font-size: 0.9rem;
      font-style: italic;
      color: red;
      font-weight: 300;
      line-height: 1.2rem;
      margin-top: 0.5rem;
      margin-bottom: 30px;
    }

    .greenNote {
      background-color: #3bb54a;
      font-size: 13px;
      display: inline-block;
      padding: 10px 25px;
      border-radius: 20px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      color: white;
    }
    .redNote {
      background-color: red;
    }
    .greyNote {
      background-color: #c6cace;
    }
    .printBillFooter {
      padding-top: 20px;
      font-size: 17px;
      border-top: 2px dashed #f0f2f5;
      position: relative;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: -15px;
        width: 28px;
        height: 28px;
        background-color: #f6f6f6;
        border-radius: 50%;
      }
      &::after {
        right: -34px;
      }
      &::before {
        left: -34px;
      }
    }
  }
  .sendBookingBill {
    width: 350px;
    margin: auto;
    margin-top: 1rem;
    button {
      width: 100%;
    }
    .btnShowModal {
      background: black;
    }
  }
}

.errorBookingBill {
  text-align: center;
  margin-top: 10vh;
  color: red;
  font-size: 1.2rem;
}
.cancelMessage {
  font-style: italic;
  color: #df0000;
  margin-bottom: 1rem;
}

.btn_to_print {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  button {
    width: 360px;
    min-height: 50px;
    padding: 8px 20px;
    border: none;
    border-radius: 12px;
    background-color: #1da1f2;
    color: white;
    font-weight: 500;
  }
}
.box {
  position: sticky;
  bottom: 60px;
  width: 100% !important;
  margin-bottom: 14px;
  @media (min-width: 786px) {
    position: relative;
    bottom: 0;
  }
  :global {
    .ant-space-item {
      width: 100%;
    }
  }
  button {
    width: 100%;
    height: 50px;
    text-transform: none;
    display: flex;
    gap: 8px;
    align-items: center;
    border-radius: 10px;
    border: 1px solid #00b5f1;
    outline: none;
    box-shadow: none;

    span {
      font-weight: 500;
      font-size: 16px;
      line-height: 23.75px;
      text-align: center;
    }
  }
  .save {
    color: #00b5f1;
    svg {
      fill: #00b5f1;
    }
  }
  .share {
    background-color: #00b5f1;
  }
}
