import React from 'react'
import MPSearchTypingComponent from '../../component/MPSearchTypingComponent'
import MPSearchTypingCard from '../../ui/MPSearchTypingCard'

export const MPSearchTyping = ({
  handleBookingSearch,
  miniSearch = false,
  onSearchDebounce,
  searchData,
  searching,
  mobileSearch = false,
  setShowSearch
}: IF_MPSearch) => {
  return (
    <MPSearchTypingComponent
      onSearchDebounce={onSearchDebounce}
      renderItem={(
        elementRef,
        search,
        showResult,
        searchHistory,
        isSearchQuery,
        onSearchSubmit,
        onSearchDelete,
        onKeyPress,
        onChooseHistory,
        onTyping,
        onOpenPopover,
        setShowResult
      ) => {
        return (
          <MPSearchTypingCard
            miniSearch={miniSearch}
            mobileSearch={mobileSearch}
            elementRef={elementRef}
            search={search}
            showResult={showResult}
            searchHistory={searchHistory}
            isSearchQuery={isSearchQuery}
            searchData={searchData}
            searching={searching}
            handleBookingSearch={handleBookingSearch}
            handleSearchSubmit={onSearchSubmit}
            handleSearchDelete={onSearchDelete}
            handlekeypress={onKeyPress}
            handleChooseHistory={onChooseHistory}
            onTyping={onTyping}
            onOpenPopover={onOpenPopover}
            setShowResult={setShowResult}
            setShowSearch={setShowSearch}
          />
        )
      }}
    />
  )
}

export default MPSearchTyping
interface IF_MPSearch {
  miniSearch?: boolean
  mobileSearch?: boolean
  setShowSearch?: any
  onSearchDebounce: (event: any) => Promise<void>
  handleBookingSearch: ({ type, item }: any) => Promise<void>
  searchData: any
  searching: boolean
  behavior?: string
}
