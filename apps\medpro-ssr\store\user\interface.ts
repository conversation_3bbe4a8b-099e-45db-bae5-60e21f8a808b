export interface UserState {
  userInfo?: UserInfo
  accessToken: string
  loading: boolean
  userMomo?: {
    loading: boolean
    data: UserMomoData | null
    error: string | null
  }
}

export interface UserInfo {
  fullname?: string
  username?: string
  id?: string
  isCS?: boolean
  email: string
  fullName?: string
  historyBookingCount?: number
  number?: string
  patientCount?: number
  token?: string
  userId?: string
  userMongoId?: string
  _id?: string
}

export interface UserMomoData {
  id?: string
  username?: string
  fullname?: string
  phone?: string
  email?: string
  token?: string
  // Add other fields as needed based on API response
}
