// h3 {
//   color: #0352cc;
//   margin: 0;
// }
.paymentInfo {
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px,
    rgba(60, 64, 67, 0.15) 0px 1px 3px 1px;
  padding: 8px 12px;
  margin-bottom: 12px;
  border-radius: 8px;
  border: 1px solid rgba(0, 224, 255, 1);
}
.paymentTitle {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  h3 {
    color: #11a2f3 !important;
  }
  .paymentTitleIcon {
    margin-right: 5px;
    font-size: 1.2rem;
  }
}
.cardView {
  margin-top: 15px;
  padding-top: 0;
  // height: 100%;
  // border-top: 1px solid #b2b2b2;

  .listBill {
    list-style: none;
    padding: 0;
    border-bottom: 1px solid #dfe3eb;

    .itemSubject {
      border-bottom: 1px solid rgba(240, 240, 240, 1);
    }
    .itemDoctor {
      border-bottom: 1px solid rgba(240, 240, 240, 1);
    }
    .itemService {
      border-bottom: 1px solid rgba(240, 240, 240, 1);
    }
    .itemService,
    .itemMoney,
    .itemSubject,
    .itemDoctor {
      display: flex;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      p {
        color: var(--primary-body-text, #003553);
        font-feature-settings: 'clig' off, 'liga' off;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        padding-bottom: 10px;
        padding-top: 10px;
      }
      .itemKeys {
        margin: 0;
        color: rgba(0, 53, 83, 1);
        display: flex;
        align-items: center;
        gap: 8px;
        .itemIcon {
          margin-right: 5px;
          font-size: 0.875rem;
        }
      }

      .itemValues {
        display: flex;
        justify-content: flex-end;
        margin: 0;
        max-width: 50%;
        text-align: right;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        color: rgba(0, 53, 83, 1);
      }
    }

    .itemMoney {
      border-bottom: none;

      .itemValues {
        color: rgba(17, 162, 243, 1);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }

  .listPayment {
    list-style: none;
    padding: 0;
    padding-top: 10px;
    margin-bottom: 24px;
    .inputReferral {
      display: flex;
      flex-direction: column;
      margin-top: 20px;
      font-size: 20px;
      font-weight: 500;
      color: var(--primary-body-text, #003553);
      .label {
        font-weight: 500;
        font-size: 16px;
        line-height: 100%;
        letter-spacing: 0%;
        color: #24313d;
      }
      :global {
        .ant-input {
          margin-top: 8px;
          height: 43px;
          border-radius: 8px;
        }
      }
    }
    li {
      display: flex;
      justify-content: flex-end;
      font-size: 0.875rem;

      .itemTotal:last-child {
        padding-bottom: 0;
      }

      .itemSum,
      .itemFee,
      .itemTotal {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20px;
        gap: 4px;
        p {
          display: flex;
          align-items: center;
          margin: 0;
          color: var(--primary-body-text, #003553);
          text-align: left;
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
        span {
          color: #0352cc;
          font-weight: bolder;
          margin-left: 20px;
          min-width: 120px;
          text-align: right;
        }
      }
      .itemSum,
      .itemFee {
        p {
          font-size: 15px;
          font-weight: 500;
          @media (max-width: 393px) {
            font-size: 15px;
          }
          @media (max-width: 375px) {
            font-size: 14px;
          }
          @media (max-width: 360px) {
            font-size: 13px;
          }
        }
        svg {
          min-width: 16px;
          &:hover {
            cursor: pointer;
          }
        }
        span {
          color: #003553;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
      .itemTotal {
        p {
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
        span {
          color: rgba(17, 162, 243, 1);
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: normal;
        }
      }

      .br {
        border-top: 1px dashed #0352cc;
        width: 100%;
      }

      .itemTotal {
        font-size: 0.945rem;
        font-weight: 500;
      }
    }
    .paymentTotalItem {
      display: flex;
      align-items: flex-start;
      margin-top: 10px;
      p {
        word-wrap: break-word;
        flex: 1;
        min-width: 0;
        color: rgba(18, 38, 63, 0.6);
        margin-left: 5px;
        font-size: 0.875rem;
      }
    }
  }
}
.note_fee {
  display: flex;
  flex-direction: column;
}
.note_discount_fee_umc {
  margin-top: -8px;
  color: #f5222d;
  font-weight: 400;
  font-size: 14px;
  line-height: 16.41px;
  letter-spacing: 0px;
}
