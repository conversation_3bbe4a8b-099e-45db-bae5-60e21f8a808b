import { useRouter } from 'next/router'
import FooterMobileMenu from '../MPFooterCard/common/FooterMobileMenu'
import styles from './styles.module.less'
export interface NewFooterProps {
  data: any
  isBottomTab: boolean
  bottomTabMenu: any
  unReadNoti: any
  downloadApp: any
  hiddenFooter: any
  countBooking?: any
}
const MPNewFooterCard = (props: NewFooterProps) => {
  const router = useRouter()

  return (
    <div className={styles['foorter']}>
      {!props.hiddenFooter
        ? props.isBottomTab &&
          ![
            'chon-lich-kham',
            'tao-moi-ho-so',
            'cap-nhat-thong-tin',
            'tim-ho-so-app',
            'xac-nhan-thong-tin-benh-nhan-app'
          ].includes(router.pathname.split('/')[1]) && (
            <>
              <div className={styles['footer']}></div>
              <FooterMobileMenu
                footerMenu={props.bottomTabMenu}
                noReadNoti={props.unReadNoti}
                countBooking={props.countBooking}
              />
            </>
          )
        : ''}
    </div>
  )
}

export default MPNewFooterCard
