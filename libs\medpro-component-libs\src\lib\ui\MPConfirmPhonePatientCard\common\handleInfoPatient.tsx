export default function handleInfoPatient({ item }: any) {
  const list = [
    {
      visible: 1,
      icon: 'HovaTen',
      label: item?.fullname + (item?.isCreateNew ? ' (<PERSON><PERSON> Sơ Mới)' : ''),
      value: '',
      extra: item?.patientCode
    },
    {
      visible: 1,
      icon: '<PERSON>a<PERSON><PERSON>',
      label: 'Địa chỉ',
      value: item?.fullAddress || 'Chưa cập nhật'
    },
    {
      visible: 1,
      icon: 'NgaySinh',
      label: '<PERSON><PERSON><PERSON> sinh',
      value:
        item?.birthdate.replaceAll('-', '/') ||
        item?.birthyear ||
        'Chưa cập nhật'
    },
    {
      visible: 1,
      icon: 'DienThoai',
      label: 'Số điện thoại',
      value: item?.mobile || 'Chưa cập nhật'
    },
    {
      visible: 0,
      icon: 'GioiTinh',
      label: 'Giới tính',
      value: item?.sex ? 'Nam' : 'Nữ'
    },
    {
      visible: 0,
      icon: 'Email',
      label: 'Địa chỉ email',
      value: item?.email || 'Chưa cập nhật'
    },
    {
      visible: 0,
      icon: '<PERSON><PERSON><PERSON>',
      label: '<PERSON>ân tộc',
      value: item?.nation?.name || 'Chưa cập nhật'
    }
  ] as Item[]

  return list
}

export interface Item {
  [x: string]: any

  visible: boolean | 0 | 1
  icon: string
  label: string
  value: string
  extra?: string
}
