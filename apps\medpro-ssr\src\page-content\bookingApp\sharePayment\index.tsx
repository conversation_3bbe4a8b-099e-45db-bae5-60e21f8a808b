import {
  <PERSON><PERSON><PERSON><PERSON>,
  get<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  getReplaceUTF8,
  getTranform<PERSON><PERSON>,
  Valid
} from '@medpro-libs/libs'
import {
  Button,
  Collapse,
  Modal,
  Form,
  Input,
  Checkbox,
  notification
} from 'antd'
import React, { useEffect, useState } from 'react'
import { IoIosInformationCircleOutline, IoMdClose } from 'react-icons/io'
import styles from './styles.module.less'
import {
  FaArrowLeft,
  FaHandHoldingHeart,
  FaPhoneAlt,
  FaStethoscope,
  FaUser,
  FaUserMd,
  FaWallet
} from 'react-icons/fa'
import { BsCalendar2DateFill } from 'react-icons/bs'
import { TiLocation } from 'react-icons/ti'
import moment from 'moment'
import { get, isArray, size } from 'lodash'
import cx from 'classnames'
import { MdKeyboardArrowRight, MdOutlineNavigateNext } from 'react-icons/md'
import { useRouter } from 'next/router'
import imgURL from '../../../../public/images/payment.gif'
import { useDispatch } from 'react-redux'
import { IoCheckmarkCircle, IoCloseSharp } from 'react-icons/io5'
import { compose } from '@reduxjs/toolkit'
import withBreadCrumb from '../../../HOCs/withBreadCrumb'
import { connect } from 'react-redux'
import { BiLeftArrowAlt } from 'react-icons/bi'
import {
  AiOutlineDown,
  AiOutlineExclamationCircle,
  AiOutlineUp
} from 'react-icons/ai'
import format from 'libs/medpro-component-libs/src/lib/common/format'
import {
  selectCanReserveBooking,
  selectPaymentMethods,
  selectSelectedPaymentMethod,
  selectSelectedPaymentType,
  selectSharePaymentFeeInfo,
  selectTreeId
} from '../../../../store/booking/selector'
import {
  selectAppId,
  selectExtraConfig
} from '../../../../store/hospital/selector'
import {
  getSharePayment,
  resetPayment,
  setSelectedPaymentMethod,
  setSelectedPaymentType
} from '../../../../store/booking/slice'
import { hospitalActions } from '../../../../store/hospital/hospitalSlice'
import {
  handleRedirectPayment,
  reserveSharePayment
} from '../../../components/pages/Booking/func'
import { PaymentAppSkeleton } from '../PaymentBookingApp/PaymentAppSkeleton'
import DefautDrawer from '../DefautDrawer'
import { RootState } from '../../../../store'
import { useAppSelector } from '../../../../store/hooks'
import Image from 'next/image'
import { showErrorNotification } from '../../../../utils/utils.error'
import { HiCheckBadge } from 'react-icons/hi2'
const valid = new Valid()
const { Panel } = Collapse

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }

interface DrawerContentItem {
  data: any
  extraContent?: any
  title: string
  open: boolean
}

const SharePaymentApp = () => {
  const router = useRouter()
  const dispatch = useDispatch()
  const paymentMethods = useAppSelector(selectPaymentMethods)
  const selectedPaymentType = useAppSelector(selectSelectedPaymentType)
  const selectedPaymentMethod = useAppSelector(selectSelectedPaymentMethod)
  const {
    loading,
    id: sharePaymentId,
    booking: bookingData,
    error
  } = useAppSelector((s) => s.booking.sharePayment)
  const treeId = useAppSelector(selectTreeId)
  const SharePaymentFeeInfo = useAppSelector(selectSharePaymentFeeInfo)
  const [openVisa, setOpenVisa] = useState<DrawerContentItem>({
    data: {},
    extraContent: {},
    title: 'Chọn phương thức thanh toán',
    open: false
  })
  const [activeContent, setActiveContent] = useState<any>(false)
  const [openCardPay, setOpenCardPay] = useState(false)
  const [modalNote, setModalNote] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [isReserving, setIsReserving] = useState(false)
  const [dataSearch, setDataSearch] = useState<any>([])
  const [medproCareSupport, setMedproCareSupport] = useState<{
    status: boolean
    content?: string
    description_following?: string
  }>({ status: false, content: '', description_following: '' })
  const [openDetailMethod, setOpenDetailMethod] = useState<any>({
    open: false,
    data: {}
  })

  const [updateVisa, setUpdateVisa] = useState({
    status: false,
    initValue: {}
  })
  const [isCheckedVisa, setIsCheckedVisa] = useState(false)
  const [ipAddress, setIPAddress] = useState('')
  const [enteredVisa, setVisaEntered] = useState({
    status: false,
    info: {
      fullName: '',
      userName: '',
      email: ''
    }
  })
  const partnerInfo = bookingData?.[0].partner
  const partnerId = bookingData?.[0]?.partnerId
  const extraConfig = useAppSelector(selectExtraConfig)
  const refundNote = get(extraConfig, 'refundNote', '')
  const appId = useAppSelector(selectAppId)
  const PaymentInfo: any[] = SharePaymentFeeInfo
  const displayDetail = PaymentInfo?.[0]?.service?.displayDetail
  const [form] = Form.useForm()
  const [formVisa] = Form.useForm()
  const patientInfo = bookingData?.[0].patient
  const fullName =
    patientInfo?.fullname || `${patientInfo?.surname} ${patientInfo?.name}`
  const medproCare = PaymentInfo[0]?.service?.care247
  const priceService = PaymentInfo.reduce((p, c) => {
    p += c?.service?.price
    return p
  }, 0)
  const tempGrandTotal =
    priceService + (medproCare?.addonServices[0]?.price || 0)

  let sumGrandTotal = 0

  if (medproCare?.partner2bill) {
    sumGrandTotal = selectedPaymentType
      ? selectedPaymentType?.grandTotal
      : priceService
  } else {
    sumGrandTotal = selectedPaymentType
      ? selectedPaymentType?.grandTotal
      : tempGrandTotal
  }
  const infoLine2 = PaymentInfo[0]?.service?.infoLine2
  const codePaymentType = selectedPaymentType?.code
  const advanced = PaymentInfo?.[0]?.service?.advanced || 0

  useEffect(() => {
    dispatch(resetPayment())
    if (!extraConfig) {
      dispatch(hospitalActions.getExtraConfig())
    }
  }, [])

  useEffect(() => {
    if (router.query.bookingId) {
      dispatch(
        getSharePayment({
          bookingId: router.query.bookingId as string,
          shareToPay: true
        })
      )
    }
  }, [router.query.bookingId])

  const handleOpenCardPay = () => {
    if (size(paymentMethods) > 1) {
      setOpenCardPay(true)
    }
  }
  const handleNoteOpen = () => {
    setModalNote(true)
  }

  const onReserveSharePayment = async () => {
    let paramsVisa
    if (selectedPaymentType?.checkEmail) {
      paramsVisa = {
        customerIpAddress: ipAddress,
        browserScreenHeight: window.innerHeight,
        browserScreenWidth: window.innerWidth,
        fullName: enteredVisa.info.fullName,
        userName: enteredVisa.info.userName,
        email: enteredVisa.info.email
      }
    }
    try {
      await reserveSharePayment({
        handleRedirectPayment: (data: any) =>
          handleRedirectPayment({ data, router, dispatch, appId: appId }),
        selectedPaymentMethod,
        selectedPaymentType,
        bookingId: sharePaymentId,
        partnerId: partnerId,
        ...paramsVisa
      })
    } catch (error) {
      showErrorNotification(error)
    }
  }
  const actionScroll = () => {
    const payment = document.getElementById('payment') as HTMLElement
    payment.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
      inline: 'nearest'
    })
  }
  const selectPaymentMethod = async ({ item }: any) => {
    const { paymentType, paymentMethod } = item
    await dispatch(setSelectedPaymentMethod(paymentMethod))
    await dispatch(setSelectedPaymentType(paymentType))
    if (paymentType) {
      actionScroll()
    }
  }
  const onSelectPaymentMethod = (item) => {
    if (size(item.paymentTypes) > 1) {
      setDataSearch(item)
      setOpenVisa({
        data: item,
        title: item.name,
        open: true
      })
      return
    }
    setOpenCardPay(false)
    setActiveContent(false)
    selectPaymentMethod({
      item: {
        paymentType: item.paymentTypes[0],
        paymentMethod: item
      }
    })
  }
  const onSelectPaymentType = (item) => {
    if (item?.checkEmail && !enteredVisa.status) {
      formVisa.setFieldsValue({
        ...item.userInfo,
        confirm: false
      })
      toggleVisa()
    } else {
      setDataSearch([])
      setOpenVisa({
        data: {},
        title: 'Chọn phương thức thanh toán',
        open: false
      })
      setOpenCardPay(false)
      selectPaymentMethod({
        item: {
          paymentType: item,
          paymentMethod: openVisa.data
        }
      })
    }
  }
  const toggleClose = () => {
    setModalNote(false)
  }
  const onClose = () => {
    setOpenCardPay(false)
  }

  const renderLabelPayment = () => {
    switch (true) {
      case treeId !== 'CSKH' && partnerId === 'bvsingapore':
        return 'Phí tư vấn trực tuyến với bệnh viện SGH'
      case codePaymentType === 'fundiin':
        return 'Phí tiện ích + Phí thanh toán hộ qua Fundiin'
      default:
        return 'Phí nền tảng + Phí TGTT'
    }
  }
  if (error) {
    console.log('error: ', error)
    router.push('/')
    return <></>
  }

  const hangdleFilter = ({ search, data, fieldSearch }: any) => {
    if (size(data) < 0) {
      const list = handleFilterByKeyword(search, data, fieldSearch)
      setDataSearch(list)
    } else {
      setDataSearch(data)
    }
    let list = []
    list = handleFilterByKeyword(search, data?.paymentTypes, fieldSearch)
    setOpenVisa((prev) => {
      return { ...prev, data: { ...data, paymentTypes: list } }
    })
  }
  const handleFilterByKeyword = (value: any, data: any, key: any) => {
    return data.filter((item: any) => {
      const regex = new RegExp(getReplaceUTF8(value), 'i')
      return getReplaceUTF8(get(item, key)).match(regex)
    })
  }

  const handleRequireInput = (label: string, require: boolean) => {
    if (require) {
      return (
        <>
          {label}
          <sup className={styles['requireInput']}>*</sup>
        </>
      )
    }
    return <span>{label}</span>
  }

  const toggleVisa = () => {
    if (!enteredVisa.status) {
      setUpdateVisa((preState) => ({
        ...preState,
        status: !updateVisa.status
      }))
    }
  }

  const handleFinish = async () => {
    const { fullName, userName, email } = formVisa.getFieldsValue()
    await getIPAddress()
    setVisaEntered({
      status: true,
      info: {
        fullName,
        userName,
        email
      }
    })
    notification.success({ message: 'Cập nhật thông tin thành công !!!' })
    toggleVisa()
  }

  const getIPAddress = () => {
    fetch('https://api.ipify.org?format=json')
      .then((response) => response.json())
      .then((data) => {
        setIPAddress(data.ip)
      })
      .catch((error) => {
        console.error('Error fetching IP address:', error)
      })
  }

  const handleCheckboxChange = (e) => {
    setIsCheckedVisa(e.target.checked)
  }

  const handleCancelVisa = () => {
    setIsCheckedVisa(false)
    toggleVisa()
  }

  return (
    <>
      <div className={styles['headerTitle']}>
        <BiLeftArrowAlt size={24} onClick={() => router.push('/')} />
        <div className={styles['title']}>Thanh toán hộ</div>
      </div>
      {loading ? (
        <PaymentAppSkeleton />
      ) : (
        <>
          <div className={styles['ConfirmPaymentBookingApp']}>
            <div className={styles['hospitalTitle']}>
              <h1>
                {partnerInfo?.name}
                {partnerInfo?.listingPackagePaid && (
                  <HiCheckBadge color='#0097FF' size={14} />
                )}
              </h1>
              <p>{partnerInfo?.address}</p>
            </div>
            <div className={styles['patientInfo']}>
              <h3>Thông tin bệnh nhân</h3>
              <Collapse
                ghost
                className={styles['collapse']}
                expandIconPosition='end'
              >
                <Panel
                  header={
                    <span className={styles['collapseHeader']}>
                      <FaUser size={18} color='#11A2F3' />
                      {fullName}
                    </span>
                  }
                  key='1'
                >
                  <p>
                    <FaPhoneAlt size={18} color='#11A2F3' />
                    {format.concerPhone(patientInfo?.mobile)}
                  </p>
                  <p>
                    <BsCalendar2DateFill size={18} color='#11A2F3' />
                    {patientInfo?.birthdate || patientInfo?.birthyear}
                  </p>
                  <p style={{ alignItems: 'flex-start' }}>
                    <TiLocation
                      size={20}
                      color='#11A2F3'
                      style={{ scale: '1.2' }}
                    />
                    {patientInfo?.fullAddress}
                  </p>
                </Panel>
              </Collapse>
            </div>
            <div className={styles['informationBooking']}>
              <h3>Thông tin đặt khám</h3>
              <div className={styles['bookingInfo']}>
                {PaymentInfo?.map((item: any, index) => {
                  return (
                    <div key={index} className={styles['bookingInfoItem']}>
                      {item?.doctor?.name && (
                        <p>
                          <FaUserMd size={18} color='#11A2F3' />
                          {`${item?.doctor?.role} ${item?.doctor?.name}` ||
                            'Đang cập nhật'}
                        </p>
                      )}
                      {item?.subject?.name && (
                        <p>
                          <FaStethoscope size={18} color='#11A2F3' />
                          {`${item?.subject?.name || 'Đang cập nhật'}`}
                        </p>
                      )}
                      {item?.service?.name && (
                        <p style={{ alignItems: 'flex-start' }}>
                          <FaHandHoldingHeart size={18} color='#11A2F3' />
                          {`${item?.service?.name || 'Đang cập nhật'}`}
                        </p>
                      )}
                      <p>
                        <BsCalendar2DateFill size={18} color='#11A2F3' />
                        {item.dateTime.date ? (
                          <>
                            {moment(item.dateTime.date).format('DD/MM/YYYY')} (
                            {moment(item.dateTime.date).format('HH:mm')})
                          </>
                        ) : (
                          'Chờ cập nhật'
                        )}
                      </p>
                      {size(PaymentInfo) > 0 && (
                        <p>
                          <FaWallet size={18} color='#11A2F3' />
                          <span className={styles['priceBooking']}>
                            {item.service?.displayDetail &&
                            item.service?.displayDetail !== ''
                              ? item.service?.displayDetail
                              : getFormatMoney(item.service?.price) + ' đ'}
                          </span>
                        </p>
                      )}
                      {!!get(
                        item,
                        'schedulesSelected[0].service.advanced',
                        0
                      ) && (
                        <div className={styles['specialistItem']}>
                          <div className={styles['itemTitle']}>Tạm ứng </div>
                          <div className={styles['content']}>
                            {getFormatMoney(
                              get(
                                item,
                                'schedulesSelected[0].service.advanced',
                                0
                              )
                            ) + ' đ'}
                          </div>
                        </div>
                      )}
                    </div>
                  )
                })}
                {infoLine2 && partnerId === 'bvsingapore' && (
                  <div className={styles['bookingInfoItem']}>
                    <p>
                      <FaStethoscope size={18} color='#11A2F3' />
                      {infoLine2.roomName}
                    </p>
                    <p>
                      <FaHandHoldingHeart size={18} color='#11A2F3' />
                      {infoLine2.serviceName}
                    </p>
                    <p>
                      <FaWallet size={18} color='#11A2F3' />
                      {getFormatMoney(infoLine2?.price)}
                    </p>
                    <p>
                      <BsCalendar2DateFill size={18} color='#11A2F3' />
                      {moment(PaymentInfo?.[0]?.date).format('DD/MM/YYYY')}
                      {PaymentInfo[0]?.time?.startTime && (
                        <span>
                          ({PaymentInfo[0]?.time?.startTime} -{' '}
                          {PaymentInfo[0]?.time?.endTime})
                        </span>
                      )}
                    </p>
                  </div>
                )}
              </div>
            </div>
            {size(medproCare?.addonServices) > 0 && (
              <div className={styles['informationBooking']}>
                <div
                  dangerouslySetInnerHTML={{ __html: medproCare.htmlTitle }}
                />
                <div className={cx(styles['bookingInfo'])}>
                  {medproCare?.addonServices?.map((item: any) => {
                    return (
                      <div key={item?.id} className={styles['medproCareItem']}>
                        <p className={styles['careItemName']}>{item.name}</p>
                        <div className={styles.Detail}>
                          <p className={styles['careItemPrice']}>
                            Giá:{' '}
                            <span className={styles['price']}>
                              {getFormatMoney(item?.price)}
                              {item.currency} / {item.duration}
                            </span>
                            {!!item?.originalPrice &&
                              item?.originalPrice !== item?.price && (
                                <span className={styles['originalPrice']}>
                                  {getFormatMoney(item?.originalPrice)}
                                  {item.currency}
                                </span>
                              )}
                          </p>
                          <div
                            className={styles['careItemDetail']}
                            onClick={(e) => {
                              e.preventDefault()
                              setMedproCareSupport({
                                status: true,
                                content: item?.description
                              })
                            }}
                          >
                            Chi tiết
                          </div>
                        </div>
                      </div>
                    )
                  })}
                  <p className={styles['noteCare247']}>
                    *Dịch vụ này được cung cấp bởi Công ty Care247, không phải
                    CSYT cung cấp, không bắt buộc.
                  </p>
                </div>
              </div>
            )}
            {size(selectedPaymentType) > 0 && (
              <>
                <div
                  className={styles['paymentMethod']}
                  onClick={() =>
                    size(paymentMethods) > 1 && handleOpenCardPay()
                  }
                >
                  <h3>Phương thức thanh toán </h3>
                  <MPButton
                    className={styles['paymentMethodItem']}
                    disabled={size(paymentMethods) === 1}
                    onClick={handleOpenCardPay}
                  >
                    <div className={styles['contentMethod']}>
                      <FaWallet size={20} color='#11A2F3' />
                      <div> {selectedPaymentType?.name}</div>
                    </div>

                    <MdKeyboardArrowRight size={18} fill='#000' />
                  </MPButton>
                </div>
                <div className={styles['customTotalPrice']}>
                  <div className={styles['customExamination']}>
                    <p>
                      Tiền khám
                      <IoIosInformationCircleOutline
                        width={12}
                        height={12}
                        color='#11a2f3'
                        onClick={handleNoteOpen}
                      />
                    </p>
                    <div className={styles['text']}>
                      {getTranformPrice({
                        displayDetail: displayDetail,
                        price:
                          getFormatMoney(
                            selectedPaymentType.subTotal - advanced
                          ) + ' đ'
                      })}
                    </div>
                  </div>
                  {!!advanced && (
                    <div className={styles['customExamination']}>
                      <p>Tạm ứng </p>
                      <div className={styles['text']}>
                        {getFormatMoney(advanced) + ' đ'}
                      </div>
                    </div>
                  )}
                  {medproCare && !medproCare?.partner2bill && (
                    <div className={styles['customExamination']}>
                      <p>
                        Dịch vụ đặt thêm
                        {medproCare?.medproCareNote && (
                          <IoIosInformationCircleOutline
                            width={12}
                            height={12}
                            color='#11a2f3'
                            onClick={handleNoteOpen}
                          />
                        )}
                      </p>
                      <div className={styles['text']}>
                        {getFormatMoney(medproCare?.addonServices[0]?.price)} đ
                      </div>
                    </div>
                  )}
                  <div className={styles['customFee']}>
                    <div className={styles['group_fee']}>
                      <p>
                        {renderLabelPayment()}
                        <IoIosInformationCircleOutline
                          width={12}
                          height={12}
                          color='#11a2f3'
                          onClick={handleNoteOpen}
                        />
                      </p>
                      <div className={styles['text']}>
                        {`${selectedPaymentType?.totalFee?.toLocaleString(
                          'vi-VN'
                        )} đ`}
                      </div>
                    </div>
                    {extraConfig?.discountUMCGroup.includes(
                      (router.query.partnerId as string) || partnerId
                    ) && (
                      <sup className={styles['note_discount_fee_umc']}>
                        Giảm 2.000đ, ưu đãi từ Medpro & UMC
                      </sup>
                    )}
                  </div>
                  <div className={styles['customTotal']}>
                    <h3>Tổng tiền</h3>
                    <div className={styles['text']}>
                      {getFormatMoney(sumGrandTotal)} đ
                    </div>
                  </div>
                </div>
                {selectedPaymentMethod?.agreement && (
                  <div className={styles.acceptPaymentLabel}>
                    <IoCheckmarkCircle width={24} height={24} color='#52C41A' />
                    <div className={styles.checkboxText}>
                      {selectedPaymentMethod?.agreement}
                    </div>
                  </div>
                )}
                <div id='payment' />
              </>
            )}
            {refundNote && (
              <div
                className={styles['attentionConfirmPayment']}
                style={{ backgroundColor: refundNote.box.backgroundColor }}
              >
                <AiOutlineExclamationCircle
                  color={refundNote.text.color}
                  className={styles['icon']}
                />
                <p
                  className={styles['content']}
                  style={{ color: refundNote.text.color }}
                >
                  {refundNote.title}
                </p>
              </div>
            )}
          </div>
        </>
      )}
      <div className={styles['customButtonNext']}>
        {size(selectedPaymentType) > 0 ? (
          <MPButton
            htmlType='submit'
            className={styles['mpButton']}
            onClick={() => setModalVisible(true)}
          >
            Xác nhận
          </MPButton>
        ) : (
          <>
            <div className={styles['customButtonNextInfo']}>
              {size(medproCare?.addonServices) > 0 && (
                <>
                  <div className={styles['infoPayment']}>
                    <span>Tiền khám</span>
                    <span className={styles['textMedproCare']}>
                      {getFormatMoney(priceService) + ' đ'}
                    </span>
                  </div>
                  {!medproCare?.partner2bill && (
                    <div className={styles['infoPayment']}>
                      <span>Dịch vụ đặt thêm</span>
                      <span className={styles['textMedproCare']}>
                        {getFormatMoney(medproCare?.addonServices[0]?.price) +
                          ' đ'}
                      </span>
                    </div>
                  )}
                </>
              )}
              <div className={styles['infoPayment']}>
                <h3>Thanh toán tạm tính</h3>
                <div className={styles['text']}>
                  {displayDetail
                    ? displayDetail
                    : `${getFormatMoney(sumGrandTotal) + ' đ'}`}
                </div>
              </div>
            </div>
            <div className={styles['groupButton']}>
              <MPButton
                htmlType='submit'
                className={styles['mpButton']}
                onClick={handleOpenCardPay}
              >
                Tiến hành thanh toán
              </MPButton>
            </div>
          </>
        )}
      </div>
      {
        // Hiển thị module thông tin Card Nội địa và Card Quốc tế
        openCardPay && (
          <DefautDrawer
            onClose={() => {
              openVisa.open
                ? setOpenVisa({
                    data: {},
                    title: 'Chọn phương thức thanh toán',
                    open: false
                  })
                : onClose()
              setActiveContent(false)
            }}
            open={openCardPay}
            closeIcon={
              openVisa.open ? (
                <FaArrowLeft />
              ) : (
                <IoCloseSharp size={22} style={{ marginTop: '2px' }} />
              )
            }
            extra={
              openVisa.open &&
              size(openVisa.data?.paymentTypes) > 10 && (
                <MPDrawerBooking
                  hospital={''}
                  province={[]}
                  handleSearch={(e) => {
                    if (!e) {
                      setOpenVisa({
                        data: dataSearch,
                        title: 'Chọn phương thức thanh toán',
                        open: true
                      })
                      return
                    }
                    hangdleFilter({
                      search: e,
                      data: openVisa.data,
                      fieldSearch: 'name'
                    })
                  }}
                  // onSearch={(e) => extraFn.onSearchDebounce(e)}
                  title=''
                  placeholder={'Tìm kiếm ngân hàng'}
                  subTitle=' '
                  isHiddenFilter={true}
                  isHidden={false}
                  form={form}
                />
              )
            }
            title={
              openVisa.open ? 'Chọn loại thẻ' : 'Chọn phương thức thanh toán'
            }
            height={'calc(100% - 150px)'}
            className={styles['drawer']}
          >
            <div
              className={cx(
                styles['ConfirmSelectPayMethod'],
                openVisa.open && styles['faceOut']
              )}
            >
              <div className={styles['btnWalletCard']}>
                {paymentMethods.map((item, index) => {
                  const isFundiin = item.methodId === 'fundiin'
                  return (
                    <Button
                      key={index}
                      className={cx(
                        styles['btn-pay-method'],
                        activeContent && isFundiin && styles['active-method']
                      )}
                      onClick={(e) => {
                        e.stopPropagation()
                        if (item.methodId === 'fundiin') {
                          setOpenVisa({
                            data: item?.paymentTypes,
                            title: item?.name,
                            extraContent: {
                              description: item?.description,
                              contentPopup: item?.contentPopup,
                              labelPopup: item?.labelPopup
                            },
                            open: true
                          })
                        } else {
                          onSelectPaymentMethod(item)
                        }
                      }}
                    >
                      <div className={styles['MethodPayment']}>
                        <div className={styles['header-method']}>
                          <span className={styles['iconMethodPay']}>
                            <Image
                              src={item?.imageURL || imgURL.src}
                              alt={item?.name}
                              width={20}
                              height={20}
                              layout='fixed'
                              objectFit='cover'
                            />
                          </span>
                          <div className={styles['name-method']}>
                            <label>
                              {item.name}
                              {/* {item.methodId === 'fundiin' && (
                                <AiOutlineExclamationCircle
                                  size={18}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setOpenVisa({
                                      data: item?.paymentTypes,
                                      title: item?.name,
                                      extraContent: {
                                        description: item?.description,
                                        contentPopup: item?.contentPopup,
                                        labelPopup: item?.labelPopup
                                      },
                                      open: true
                                    })
                                  }}
                                />
                              )} */}
                            </label>
                            {item.methodId !== 'fundiin' && (
                              <p>{item?.description}</p>
                            )}
                          </div>
                        </div>
                        <div className={styles['arrowMethod']}>
                          {size(item.paymentTypes) > 1 && (
                            <MdOutlineNavigateNext
                              size={20}
                              className={styles['arrow']}
                            />
                          )}
                        </div>
                      </div>
                    </Button>
                  )
                })}
              </div>
            </div>
            {/* )} */}
            {openVisa.open && (
              <div className={styles['btnPay-CardDrawer']}>
                {isArray(openVisa.data?.paymentTypes) ? (
                  openVisa.data?.paymentTypes.map((item, index) => {
                    return (
                      <MPButton
                        className={styles['btn-pay-method']}
                        onClick={(e) => {
                          e.stopPropagation()
                          onSelectPaymentType(item)
                        }}
                        key={index}
                      >
                        {item?.paymentIcon?.path && (
                          <span className={styles['icon']}>
                            <img
                              src={item?.paymentIcon?.path || imgURL.src}
                              alt={item?.name}
                            />
                          </span>
                        )}

                        <span>{item?.name?.trim()}</span>
                      </MPButton>
                    )
                  })
                ) : (
                  <>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: openVisa.extraContent?.description
                      }}
                    />
                    <>
                      <p
                        className={styles['labelPopup']}
                        onClick={() => {
                          setOpenDetailMethod({
                            open: true,
                            data: openVisa.extraContent
                          })
                        }}
                      >
                        {openVisa.extraContent?.labelPopup}
                      </p>
                      <MPButton
                        onClick={(e) => {
                          e.stopPropagation()
                          onSelectPaymentType(openVisa.data?.[0])
                        }}
                        className={cx(
                          styles['mpButton'],
                          styles['button-fundiin']
                        )}
                      >
                        Tiếp tục
                      </MPButton>
                    </>
                  </>
                )}
              </div>
            )}
          </DefautDrawer>
        )
      }
      {/* ===== Info MedproCare ======*/}
      {medproCareSupport.status && (
        <DefautDrawer
          title='Chi tiết dịch vụ'
          open={medproCareSupport.status}
          onClose={() =>
            setMedproCareSupport({
              status: false
            })
          }
          className={styles['drawer']}
          height='fitContent'
        >
          <div className={styles['description']}>
            <p className={styles['description_following']}>
              {medproCareSupport.description_following}
            </p>
            <div
              dangerouslySetInnerHTML={{
                __html: medproCareSupport?.content
              }}
            />
          </div>
          {/* <div className={styles['hotline']}>
            <div className={styles['icon']}>
              <FaPhoneAlt size={16} color=' #11A2F3' />
            </div>
            <p>
              <span className={styles['title']}>Gọi 1900 2115</span>
              <span className={styles['subTitle']}>
                để được hỗ trợ trực tiếp
              </span>
            </p>
          </div> */}
        </DefautDrawer>
      )}
      {/* ===== Popup Ghi chú ======*/}
      {modalNote && (
        <div style={{ borderRadius: '16px' }}>
          <Modal
            centered
            open={modalNote}
            className={styles['customModalNoteModule']}
            bodyStyle={{ padding: '12px' }}
            destroyOnClose={false}
            closable={false}
            footer={null}
            onCancel={toggleClose}
          >
            <div className={styles['customModalNote']}>
              <div className={styles['customModalHeader']}>
                <span>Ghi chú</span>
                <IoMdClose
                  className={styles['iconCloseModule']}
                  onClick={toggleClose}
                />
              </div>
              <div className={styles['customModalBody']}>
                {selectedPaymentType?.subTotalNote && (
                  <>
                    <p>Tiền khám</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedPaymentType?.subTotalNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
                {medproCare?.medproCareNote && (
                  <>
                    <p>Dịch vụ đặt thêm</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: medproCare?.medproCareNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
                {selectedPaymentType?.totalFeeNote && (
                  <>
                    <p>{renderLabelPayment()}</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedPaymentType?.totalFeeNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
              </div>
            </div>
          </Modal>
        </div>
      )}
      {/* ===== Popup chi tiết thanh toán ======*/}
      {openDetailMethod.open && (
        <Modal
          title={''}
          open={openDetailMethod.open}
          centered
          className={styles['modalConfirmPayment']}
          okText='Đóng'
          onOk={() => {
            setOpenDetailMethod((preState) => ({
              ...preState,
              open: false
            }))
          }}
          cancelButtonProps={{ hidden: true }}
        >
          <div>
            <div className={styles['customModalHeader']}>
              <span>{openDetailMethod?.data?.labelPopup}</span>
            </div>
            <div
              className={styles['descriptionDetail']}
              dangerouslySetInnerHTML={{
                __html: openDetailMethod?.data?.contentPopup
              }}
            />
          </div>
        </Modal>
      )}
      {modalVisible && (
        <Modal
          title={null}
          open={modalVisible}
          centered
          onOk={async (e) => {
            try {
              setIsReserving(true)
              await onReserveSharePayment()
            } finally {
              setIsReserving(false)
              window.dataLayer.push({
                event: 'Xác Nhận Phương Thức Thanh Toán',
                Action: 'Click',
                Category: 'Button-Action',
                Label: 'Đồng ý',
                Event: 'Xác Nhận Phương Thức Thanh Toán',
                PartnerId: partnerId,
                UserId: ''
              })
            }
          }}
          okText={isReserving ? 'Đang xử lý...' : 'Đồng ý'}
          okButtonProps={{ loading: isReserving }}
          cancelButtonProps={{ style: { display: 'none' } }}
          className={styles['modalConfirmPayment']}
        >
          <div>
            <div className={styles['customModalHeader']}>
              <span>Thông báo</span>
              <IoMdClose
                className={styles['iconCloseModule']}
                onClick={() => setModalVisible(false)}
              />
            </div>
            <div className={styles['title']}>
              {/* {selectedPaymentType?.paymentIcon?.path && (
                  <img src={selectedPaymentType?.paymentIcon?.path} alt='' />
                )} */}
              Bạn đang thực hiện <b> {selectedPaymentType?.name}</b> với số tiền{' '}
              <b> {getFormatMoney(selectedPaymentType?.grandTotal)} đ</b>{' '}
            </div>

            {/* <div className={styles['description']}>
                Bạn sẽ nhận được phiếu khám bệnh ngay khi{' '}
                <b>thanh toán thành công.</b> Trường hợp không nhận được phiếu
                khám bệnh, vui lòng liên hệ <b>19002115.</b>
              </div> */}
          </div>
        </Modal>
      )}
      {updateVisa.status && (
        <DefautDrawer
          title={'Thông báo'}
          open={updateVisa.status}
          onClose={() => {
            console.log('onClose :>>')
          }}
          height={'calc(100% - 150px)'}
          className={styles['drawer']}
          closeIcon={false}
        >
          <div>
            <p className={styles.description}>
              Theo quy định của Tổ chức thẻ Quốc tế, vui lòng điền đầy đủ thông
              tin người thanh toán
            </p>
            <Form
              form={formVisa}
              layout='vertical'
              onFinish={handleFinish}
              className={styles['form']}
            >
              <Form.Item
                label={handleRequireInput('Họ & tên', true)}
                name='fullName'
                rules={[{ validator: valid.required }]}
              >
                <Input placeholder='Nhập họ và tên ghi trên thẻ Visa' />
              </Form.Item>
              <Form.Item
                label={handleRequireInput('Số điện thoại', true)}
                name='userName'
                rules={[{ validator: valid.mobile }]}
              >
                <Input placeholder='Nhập số điện thoại' />
              </Form.Item>
              <Form.Item
                label={handleRequireInput('Email', true)}
                name='email'
                rules={[{ validator: valid.email }]}
              >
                <Input placeholder='Nhập email' />
              </Form.Item>
              <Form.Item name='confirm'>
                <Checkbox onChange={handleCheckboxChange}>
                  <p className={styles.contentConfirm}>
                    Tôi xác nhận thông tin trên là chính xác và hoàn toàn chịu
                    trách nhiệm về thông tin này.
                  </p>
                </Checkbox>
              </Form.Item>
              <div className={styles.ButtonControl}>
                <Button
                  type='ghost'
                  className={styles['cancelBtn']}
                  onClick={handleCancelVisa}
                >
                  Trở lại
                </Button>
                <Button
                  htmlType='submit'
                  className={styles['confirmBtn']}
                  disabled={!isCheckedVisa}
                  type='primary'
                >
                  Xác nhận
                </Button>
              </div>
            </Form>
          </div>
        </DefautDrawer>
      )}
    </>
  )
}
export default compose(
  withBreadCrumb([{ title: 'Thanh toán hộ' }]),
  connect((state: RootState) => {
    return {
      selectedPaymentMethod: selectSelectedPaymentMethod(state),
      selectedPaymentType: selectSelectedPaymentType(state),
      treeId: selectTreeId(state),
      canReserveBooking: selectCanReserveBooking(state)
    }
  })
)(SharePaymentApp)
