.createPatientCard {
  width: 100%;
  max-width: 980px;
  margin: auto;
  margin-top: 32px;
  padding-bottom: 38px;
  .title {
    text-align: center;
    width: 100%;
    font-size: 25px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-bottom: 28px;
    color: var(--primary-body-text, #003553);
  }
  .desc {
    padding: 16px;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #11a2f3;
    margin-bottom: 24px;
    border-radius: 8px;
    border: 1px solid #c1d5e9;
    background: #d4e9ff;
  }
  .noteRequired {
    color: #ff3b30;
    margin-bottom: 24px;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}

.formContact {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;

  .submit {
    background: #1da1f2;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 4 15px;
    min-width: 152px;
    min-height: 32px;
    margin: 10px;
    font-weight: 500;
    transition: all 0.3s;
    &:disabled {
      background-color: #cccccc;
    }
    &:hover {
      background-color: #007bff;
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
    }
  }

  .listContact {
    width: 100%;
    height: 100%;
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;

    li {
      width: 50%;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      &:last-child {
        width: 100%;
      }

      .enter {
        width: 100%;
        label {
          font-weight: 500;
          sup {
            font-size: 1rem;
            color: red;
          }
        }

        // input,
        // select {
        //   height: 45px;
        //   .enterCustom();
        // }
        // textarea {
        //   padding: 10px !important;
        //   .enterCustom();
        // }

        input,
        select,
        textarea {
          &:hover,
          &:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
            outline: 0;
          }
        }
      }
    }
  }
}
.formInputGroup {
  margin-bottom: 0 !important;
  @media only screen and (max-width: 768px) {
    margin-bottom: 24px !important;
  }
  .inputGroup {
    display: flex !important;
    justify-content: space-between;
  }
  .selectItem {
    width: 30%;
    margin-bottom: 0;
  }
  .requireInput {
    font-size: 100%;
    top: -0.2em;
    left: 3px;
    color: red;
  }
}
.selectItem {
  input {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  :global {
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 10px;
      background: #fff;
      min-height: 50px;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none !important;
    }
    .ant-select-selection-placeholder {
      margin-top: 4px;
    }
    .ant-select-selection-item {
      margin-top: 4px;
    }
  }
}
.selectItem,
.formInputGroup,
.formInputItem {
  label {
    color: var(--primary-body-text, #003553) !important;
    font-size: 20px !important;
    font-style: normal !important;
    font-weight: 500 !important;
    line-height: normal !important;
  }
  .requireInput {
    font-size: 100%;
    top: -0.2em;
    left: 3px;
    color: red;
  }
  :global {
    .ant-form-item-explain-error {
      font-size: 0.7rem;
    }
  }
}
.formInputItem {
  @media only screen and (max-width: 768px) {
    :global {
      .ant-form-item-label {
        padding: 0 0 2px;
      }
    }
  }
  input {
    display: flex;
    height: 50px;
    padding: 15px 16px 16px 16px;
    align-items: center;
    border-radius: 10px;
    &::placeholder {
      color: var(--grey-field, #b1b1b1);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }

  div > span {
    padding-top: 7px;
    padding-bottom: 7px;
    border-radius: 5px;
  }
  :global {
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none !important;
    }
    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      height: auto !important;
      padding-top: 3px !important;
      padding-bottom: 3px !important;
    }
    .ant-select-single:not(.ant-select-customize-input)
      .ant-select-selector
      .ant-select-selection-search-input {
      height: auto !important;
    }
  }
}
.modalAddress {
  :global {
    .ant-modal-content {
      height: fit-content;
      border-radius: 16px;
    }
  }
  .title {
    font-weight: 500;
    font-size: 24px;
    line-height: 28px;
    text-align: center;
    color: #11a2f3;
  }
  .desc {
    margin: 12px 0 12px 0;
    font-weight: 400;
    font-size: 20px;
    line-height: 23px;
    text-align: center;
    color: #24313d;
  }
  .input {
    height: fit-content;
    border-radius: 8px;
    padding: 12px;
    gap: 4px;
    border: 1px solid #cbd2d9;
    > p {
      margin: 0;
    }
    .add,
    .address {
      font-weight: 400;
    }
    .add {
      font-size: 14px;
      line-height: 18px;
      color: #52575c;
    }
    .address {
      font-size: 16px;
      line-height: 23px;
      color: #24313d;
    }
  }
  .btnWrapper {
    margin-top: 16px;
    margin: 16px 0 0 0;
    display: flex;
    justify-content: center;

    .btnCancel,
    .btnConfirm {
      width: 100%;
      height: 50px;
      border-radius: 12px;
      gap: 10px;
      padding: 12px 10px;
      font-family: Roboto;
      font-weight: 600;
      font-size: 16px;
      line-height: 18.75px;
      letter-spacing: 0%;
    }

    .btnCancel {
      background: #ffffff;
      color: #11a2f3;
      border: 1px solid #11a2f3;
    }

    .btnConfirm {
      background: #11a2f3;
      color: #ffffff;
    }
  }
}
.line {
  width: 100%;
  height: 2px;
  background: linear-gradient(40deg, #00b5f1 0%, #00e0ff 100%);
  margin-bottom: 36px;
  margin-top: 24px;
}
.Divider {
  margin-top: 0 !important;
  margin-bottom: 20px !important;
  &::after {
    top: 0 !important;
    border-color: #cbd2d9 !important;
  }
  @media (max-width: 576px) {
    padding-right: 16px !important;
    padding-left: 16px !important;
  }
  :global {
    .ant-divider-inner-text {
      font-weight: 700;
      font-size: 24px;
      line-height: 23.44px;
      color: #003553;
      padding-right: 12px !important;
    }
  }
}
.lineAddress {
  width: 100%;
  height: 2px;
  background: #cbd2d9;
  margin-bottom: 24px;
  margin-top: 12px;
}
.btnWrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  gap: 1rem;
  .btnReset,
  .btnSubmit {
    border-radius: 8px;
    box-shadow: 4px 8px 30px 0px rgba(177, 196, 218, 0.35);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 10px 0;
    width: 121px;
    border: none;
  }
  .btnReset {
    background: #ffb54a;
    &:hover {
      background: #e68a0a;
    }
  }
  .btnSubmit {
    background: var(
      --primary-gradient-title,
      linear-gradient(40deg, #00b5f1 0%, #00e0ff 100%)
    );
    &:hover {
      background: linear-gradient(
        46deg,
        rgba(7, 170, 224, 1) 0%,
        rgba(0, 198, 225, 1) 100%
      );
    }
  }
}
.boxAddress {
  display: flex;
  flex-direction: column;
  .sup {
    font-weight: 400;
    font-style: italic;
    font-size: 16px;
    margin-bottom: 0;
    color: #f5222d;
  }
}
