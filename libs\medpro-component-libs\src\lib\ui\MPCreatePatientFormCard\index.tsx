/* eslint-disable react-hooks/rules-of-hooks */
import { Col, Divider, Form, Modal, Row, notification } from 'antd'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { PatientFormData } from '../../component/MPCreatePatientFormComponent/types'
import MPButton from '../MPButton'
import { handleAddressFields } from './common/handleAddressFields'
import { handleDetails } from './common/handleDetails'
import { handleRelative } from './common/handleRelative'
import styles from './styles.module.less'
import { matchFullAddress } from '../../common/func'

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }
interface Props {
  data: {
    title: string
    description: string
    patient: PatientFormData
    profession: any[]
    nation: any[]
    relative: any[]
    province: any[]
    district: any[]
    ward: any[]
  }
  handleSubmit: any
  handleChangeAddress: any
  submitting?: boolean
  isCSKHApp: boolean
  userCountry: string
  patientYearOldAccepted?: number
  cleanDistrictsAndWards?: () => void
  partnerId?: string
  userId?: string
}

export const MPCreatePatientFormCard = ({
  data,
  handleSubmit,
  handleChangeAddress,
  submitting,
  isCSKHApp,
  patientYearOldAccepted = 16,
  userCountry,
  cleanDistrictsAndWards,
  partnerId,
  userId
}: Props) => {
  // const disabled = Object.keys(errors)?.length >= 1
  const patient = data.patient
  const date = new Date()

  const [form] = Form.useForm()
  // #region State
  const [confirmAddress, setConfirmAddress] = useState(false)
  const [birthDay, setBirthDay] = useState({
    day: patient?.day,
    month: patient?.month,
    year: patient?.year
  })

  useEffect(() => {
    if (patient) {
      setBirthDay({
        day: patient.day,
        month: patient.month,
        year: patient.year
      })

      form.setFieldsValue(patient)

      if (patient?.dantoc_id === 'medpro_82') {
        setIsForeign(true)
      }
      if (patient?.relation) {
        //Lỗi select không hiện place holder
        if (!patient?.relation?.relative_type_id) {
          form.setFieldValue(['relation', 'relative_type_id'], undefined)
        }
      }
    }
  }, [form, patient])

  const [showRelative, setShowRelative] = useState(false)
  const [age, setAge] = useState(-1)
  const [country, setCountry] = useState(userCountry)
  const [isForeign, setIsForeign] = useState(false)

  useEffect(() => {
    const { year, month, day } = birthDay
    let age
    if (month && day) {
      const dateMM = moment({
        day,
        month: month - 1,
        year
      })
      age = moment().diff(dateMM, 'years')
    } else {
      age = moment().year() - year
    }
    setShowRelative(age < patientYearOldAccepted)
    setAge(age)
    if (0 <= age && age < patientYearOldAccepted) {
      form.setFieldValue('profession_id', 'medpro_952')
    } else if (form.getFieldValue('profession_id') === 'medpro_952') {
      form.resetFields(['profession_id'])
    }
  }, [birthDay, form, patientYearOldAccepted])

  function resetForm() {
    form.resetFields()

    // scroll to first field when reset form
    const firstField = [Object.keys(form.getFieldsValue())[0]]
    form.scrollToField(firstField, {
      behavior: (actions) =>
        actions.forEach(({ el, top, left }) => {
          // 100 is height of element
          const adjustedTop = top - 100
          el.scrollTop = adjustedTop
          el.scrollLeft = left
        })
    })
    setBirthDay({ day: 0, month: 0, year: 0 })

    // Syntax này là optional chaining => google
    cleanDistrictsAndWards?.()
  }

  const onValuesChange = (changes: any) => {
    console.log('changes', changes)
    const { day, month, year, city_id, district_id, quocgia_id } = changes
    if (year) {
      setBirthDay((p) => ({ ...p, year }))
    }
    if (month) {
      setBirthDay((p) => ({ ...p, month }))
    }
    if (day) {
      setBirthDay((p) => ({ ...p, day }))
    }

    //TODO case cskh medpro cam
    if (quocgia_id) {
      setCountry(quocgia_id)
      switch (quocgia_id) {
        case 'VN':
        case 'VIE':
          form.setFieldValue('dantoc_id', 'medpro_1')
          setIsForeign(false)
          break
        default:
          form.setFieldValue('dantoc_id', 'medpro_82')
          setIsForeign(true)
          break
      }
    }

    if (city_id) {
      handleChangeAddress('district', city_id)
      form.resetFields(['district_id', 'ward_id'])
    }
    if (district_id) {
      handleChangeAddress('ward', district_id)
      form.resetFields(['ward_id'])
    }
  }

  const handleInitFormValues = () => {
    switch (userCountry) {
      case 'CAM':
        return {
          quocgia_id: 'CAM',
          dantoc_id: 'medpro_82'
        }
      default:
        return {
          quocgia_id: 'VIE',
          dantoc_id: 'medpro_1'
        }
    }
  }

  const handleCheckDay = (values: any) => {
    // Kiểm tra năm
    if (Number(values?.year) <= Number(date.getFullYear()))
      if (Number(values?.year) < Number(date.getFullYear())) return true
      // Nếu năm bằng nhau thì kiểm tra tháng
      else if (Number(values?.month) <= Number(date.getMonth()) + 1)
        if (Number(values?.month) < Number(date.getMonth()) + 1) return true
        // Nếu tháng bằng nhau thì kiểm tra ngày
        else if (Number(values?.day) < Number(date.getDate())) return true
        else return false
      else return false
    else return false
  }
  const onFinish = (values: any) => {
    if (handleCheckDay(values)) {
      handleSubmit(values)
    } else {
      notification.error({
        message: 'Ngày sinh không được lớn hơn ngày hiện tại!'
      })
    }
    // setFormValues(values)
    // setConfirmAddress(true)
  }
  const handleConfirmAddress = () => {
    form.submit()
  }

  const toggleConfirmAddress = () => {
    setConfirmAddress((preState) => !preState)
  }

  const onResetFieldDay = () => {
    form.resetFields(['day'])
  }

  const onSendEventTagManager = (e: any) => {
    form
      .validateFields()
      .then(() => {
        if (!patient?.id) {
          window.dataLayer.push({
            event: 'Tạo mới hồ sơ chưa từng khám',
            Action: 'Click',
            Category: 'Button-Action',
            Event: 'Tạo mới hồ sơ chưa từng khám',
            Label: e?.currentTarget?.textContent,
            PartnerId: partnerId,
            UserId: userId
          })
        }
        toggleConfirmAddress()
      })
      .catch((errorInfo) => {
        console.log('Validation failed:', errorInfo)
        const firstErrorField = errorInfo.errorFields[0].name
        console.log('firstErrorField', firstErrorField)
        form.scrollToField(firstErrorField, {
          behavior: (actions) =>
            actions.forEach(({ el, top, left }) => {
              // 100 is height of element
              const adjustedTop = top - 100
              el.scrollTop = adjustedTop
              el.scrollLeft = left
            })
        })
      })
  }

  const DividerCustomer = (title: string) => {
    return (
      <Divider
        orientation='left'
        plain
        orientationMargin={0}
        className={styles['Divider']}
      >
        {title}
      </Divider>
    )
  }

  return (
    <div className={styles['createPatientCard']}>
      <div className={styles['desc']}>{data.description}</div>
      <div className={styles['noteRequired']}>(*) Thông tin bắt buộc nhập</div>
      <Form
        form={form}
        layout='vertical'
        initialValues={handleInitFormValues()}
        onValuesChange={onValuesChange}
        onFinish={onFinish}
        className={styles['listContact']}
        onReset={resetForm}
      >
        <Row gutter={10}>
          {DividerCustomer('Thông tin chung')}
          {handleDetails(
            data,
            age,
            birthDay,
            isCSKHApp,
            country,
            onResetFieldDay,
            patientYearOldAccepted,
            partnerId
          ).map((item, index) => {
            return (
              <Col
                key={index}
                span={24}
                sm={24}
                md={item.width === 'fuild' ? 24 : 12}
              >
                <div className={styles['inputItem']}>
                  {item?.enter && item?.enter(item)}
                </div>
              </Col>
            )
          })}
          {/* Nhập địa chỉ thường trú (ghi trên CCCD) (Luồng website riêng) */}
          {DividerCustomer(
            isForeign ? 'Thông tin lưu trú' : 'Địa chỉ theo CCCD'
          )}
          {handleAddressFields(data, country, isForeign).map((item, index) => {
            return (
              <Col
                key={index}
                span={24}
                sm={24}
                md={item.width === 'fuild' ? 24 : 12}
                hidden={item.hidden}
              >
                <div className={styles['inputItem']}>
                  {item?.enter && item?.enter(item)}
                </div>
              </Col>
            )
          })}
          {showRelative && (
            <>
              {DividerCustomer('Thông tin thân nhân')}
              {handleRelative(data).map((item, index) => {
                return (
                  <Col
                    key={index}
                    span={24}
                    sm={24}
                    md={item.width === 'fuild' ? 24 : 12}
                  >
                    <div className={styles['inputItem']}>
                      {item?.enter && item?.enter(item)}
                    </div>
                  </Col>
                )
              })}
            </>
          )}
        </Row>
        <div className={styles['btnWrapper']}>
          {!patient?.id && (
            <MPButton
              className={styles['btnReset']}
              type='primary'
              htmlType='reset'
            >
              Nhập lại
            </MPButton>
          )}
          <MPButton
            className={styles['btnSubmit']}
            type='primary'
            loading={submitting}
            onClick={onSendEventTagManager}
          >
            {patient?.id ? 'Cập nhật' : 'Tạo mới'}
          </MPButton>
        </div>
      </Form>
      {/* Modal thông báo xác nhận địa chỉ (Luồng website riêng)  */}
      {confirmAddress && (
        <Modal
          open={confirmAddress}
          footer={null}
          closable={false}
          centered
          className={styles['modalAddress']}
        >
          <>
            <div className={styles['title']}>Thông báo</div>
            <p className={styles['desc']}>
              Vui lòng kiểm tra và xác nhận địa chỉ nhập đúng với địa chỉ trên
              CCCD
            </p>
          </>
          <div className={styles['input']}>
            <p className={styles['add']}>Địa chỉ:</p>
            <p className={styles['address']}>
              {matchFullAddress(form.getFieldsValue(), data)}
            </p>
          </div>

          <div className={styles['btnWrapper']}>
            <MPButton
              className={styles['btnCancel']}
              type='primary'
              onClick={toggleConfirmAddress}
            >
              Chỉnh sửa lại
            </MPButton>
            <MPButton
              className={styles['btnConfirm']}
              type='primary'
              onClick={handleConfirmAddress}
            >
              Đồng ý
            </MPButton>
          </div>
        </Modal>
      )}
    </div>
  )
}

export default MPCreatePatientFormCard
