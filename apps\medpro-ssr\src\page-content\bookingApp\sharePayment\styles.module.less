.textFont() {
  //styleName: h2: Regular;
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  text-align: left;
  color: #24313d;
}
.mpButton {
  // margin-top: 16px;
  width: 100%;
  height: 50px !important;
  padding: 12px;
  border-radius: 12px;
  gap: 8px;
  background: #11a2f3;
  color: #fff;
  font-size: 16px;
}
.drawer {
  :global {
    .ant-drawer-body {
      padding: 0 16px;
    }
  }
  .description {
    .description_following {
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 400;
      line-height: 19px;
      color: #24313d;
    }
  }
}
.headerTitle {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #11a2f3;
  color: #ffffff;
  padding: 12px 16px 7px 18px;
  height: 50px;
  .title {
    font-size: 18px;
    font-weight: 600;
    line-height: 18px;
    text-align: center;
    color: #ffffff;
  }
  svg {
    position: absolute;
    top: 14px;
    left: 16px;
  }
}
// ====================================================================================
.customTotalPrice {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 12px;

  .customExamination {
    display: flex;
    justify-content: space-between;
    align-items: center;

    p {
      .textFont();
      display: flex;
      align-items: center;
      gap: 5px;
      font-weight: 400;
      line-height: 16px;
      margin-bottom: 0;
      max-width: 200px;
    }
    .text {
      .textFont();
      text-align: end;
      font-weight: 400;
      line-height: 16px;
      max-width: 200px;
    }
  }

  .customFee {
    .note_discount_fee_umc {
      top: 0;
      color: #f5222d;
      font-weight: 400;
      font-size: 14px;
      line-height: 16.41px;
      letter-spacing: 0px;
    }
    .group_fee {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #24313d;
      p {
        .textFont();
        display: flex;
        align-items: center;
        gap: 5px;
        font-weight: 400;
        line-height: 16px;
        margin-bottom: 0;
        max-width: 200px;
      }
      .text {
        .textFont();
        text-align: end;
        font-weight: 400;
        line-height: 16px;
        max-width: 200px;
      }
    }
  }

  .customTotal {
    display: flex;
    align-items: center;
    justify-content: space-between;
    h3 {
      margin-bottom: 0;
      line-height: 16px;
    }
    .text {
      .textFont();
      line-height: 16px;
      font-weight: 600;
      color: #11a2f3;
    }
  }
}
.acceptPaymentLabel {
  padding: 12px 0;
  display: flex;
  gap: 4px;
  svg {
    width: 18px;
    height: 18px;
  }
  .checkboxText {
    width: 95%;
    font-size: 14px;
    font-weight: 400;
    line-height: 16.94px;
    color: #7b8794;
  }
}

.ConfirmPaymentBookingApp {
  padding: 12px 16px;
  background: #f6f6f6;
  .hospitalTitle {
    column-gap: 8px;
    align-items: center;
    background: #ffffff;
    padding: 8px;
    border: 1px solid #11a2f3;
    border-radius: 8px;
    svg {
      margin-top: 1px;
      vertical-align: top;
    }
    img {
      width: 35px;
      height: 35px;
    }
    h1 {
      font-size: 16px;
      font-weight: 500;
      line-height: 19px;
      letter-spacing: 0em;
      text-align: left;
      color: #24313d;
      margin-bottom: 4px;
    }
    p {
      font-size: 13px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0em;
      text-align: left;
      color: #627792;
      margin-bottom: 0;
    }
  }
  .patientInfo {
    margin-top: 12px;
    h3 {
      font-size: 16px;
    }
    .collapse {
      .textFont();
      column-gap: 8px;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      p {
        display: flex;
        align-items: center;
        gap: 8px;
        svg {
          min-width: 18px;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
      .collapseHeader {
        .textFont();
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
  .informationBooking {
    margin-top: 12px;
    .label {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 0;
      color: #24313d;
    }
    .label_sup {
      font-size: 14px;
      font-weight: 400;
      line-height: 16.94px;
      color: #566572;
    }
    .bookingInfo {
      column-gap: 8px;
      padding: 12px;
      align-items: center;
      background: #ffffff;
      border-radius: 12px;
      .bookingInfoItem {
        border-bottom: 1px dashed #bebebe;
        padding-bottom: 16px;
        margin-bottom: 16px;
        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
          margin-bottom: 0;
        }
        svg {
          width: 18px;
          min-width: 18px;
        }
        p {
          .textFont();
          display: flex;
          gap: 8px;
          align-items: center;
          margin-bottom: 12px;
          &:last-child {
            margin-bottom: 0;
          }
          .iconSvg {
            min-width: 18px !important;
            width: 18px;
          }
        }
        .priceBooking {
          font-size: 20px;
          font-weight: 700;
          line-height: 24.2px;
        }
      }
    }
    .noteCare247 {
      margin-left: 4px;
      font-weight: 500;
      font-size: 14px;
      line-height: 16.94px;
      margin-top: 8px;
      margin-bottom: 0;
      color: #f5222d !important;
    }
    .medproCareItem {
      gap: 0.5rem;
      line-height: 18px;
      :global {
        .ant-radio-wrapper > span {
          &:nth-of-type(2) {
            width: 100%;
          }
        }
      }
      p {
        font-size: 14px;
        font-weight: 400;
        line-height: 19px;
        text-align: left;
        color: #24313d;
        margin-bottom: 2px;
      }
      .Detail {
        display: flex;
        justify-content: space-between;
      }
      .careItemPrice {
        display: flex;
        gap: 8px;
        .price {
          color: #24313d;
          font-weight: 500;
        }
        .originalPrice {
          color: #b2b0b0;
          text-decoration: line-through;
        }
      }
      .careItemDetail {
        // position: absolute;
        // bottom: 0;
        // right: 0;
        font-size: 12px;
        line-height: 14px;
        color: #24313d;
        background-color: #f5f7fa;
        padding: 4px 8px;
        border-radius: 4px;
      }
      ol {
        padding-left: 1rem;
        margin-bottom: 0;
      }
    }
  }
  .attentionConfirmPayment {
    margin-top: 12px;
    background-color: #ffefef;
    border-radius: 12px;
    padding: 10px;
    gap: 6px;
    display: flex;
    .icon {
      min-width: 16px;
    }
    .content {
      font-size: 14px;
      font-weight: 400;
      line-height: 17px;
      color: #f5222d;
      margin-bottom: 0;
    }
  }
}
.customButtonNext {
  padding: 12px 16px;
  background: #ffffff;

  .customButtonNextInfo {
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    span {
      line-height: 19.36px;
    }
    .infoPayment {
      display: flex;
      align-items: center;
      justify-content: space-between;
      h3 {
        margin-bottom: 0;
        font-size: 16px;
        font-weight: 600;
      }
      .text {
        width: 50%;
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        color: #11a2f3;
        text-align: end;
      }
      .textMedproCare {
        width: 50%;
        font-size: 16px;
        font-weight: 400;
        // line-height: 16px;
        text-align: end;
      }
    }
  }
  .mpButton {
    width: 100%;
    height: 50px !important;
    padding: 12px;
    border-radius: 12px;
    gap: 8px;
    background: #11a2f3;
    color: #fff;
    font-size: 16px;
  }
}

// ====================================================================================

.ConfirmSelectPayMethod {
  padding: 0;
  animation: left 0.5s ease forwards;
  .btnPay-Card {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 12px;
    .btn-pay-method {
      .textFont();
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 16px, 12px, 16px, 12px;
      border-radius: 12px;
      border: 1px solid #cbd2d9;
      white-space: normal;
    }
  }
  .btnWalletCard {
    display: flex;
    flex-direction: column;
    gap: 12px;
    .btn-pay-method {
      .textFont();
      p {
        margin-bottom: 0;
      }
      svg {
        min-width: 16px;
        min-height: 16px;
      }
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: fit-content;
      min-height: 50px;
      padding: 16px, 12px, 16px, 12px;
      border-radius: 12px;
      border: 1px solid #cbd2d9;
      white-space: normal;
      .MethodPayment {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }
      .header-method {
        display: flex !important;
        justify-content: space-between !important;
        gap: 8px;
        align-items: center;
        position: relative;
        .name-method {
          label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 400;
            line-height: 19.36px;
            color: #24313d;
          }
          p {
            font-size: 14px;
            font-weight: 400;
            line-height: 16.94px;
            text-align: left;
            color: #627792;
          }
        }
      }
      .arrowMethod {
        position: relative;
        // .arrow {
        //   position: absolute;
        //   top: -7px;
        //   left: 10px;
        // }
      }
      .active-method {
        border-color: #11a2f3 !important;
        .contentPopup {
          margin-top: 12px;
        }
        .labelPopup {
          text-decoration: underline;
          color: #1da1f2;
          font-size: 14px;
        }
      }
    }
  }
}
@keyframes right {
  from {
    transform: translateX(100vw);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes leftIn {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100vw);
    opacity: 0;
  }
}
@keyframes left {
  from {
    transform: translateX(-100vw);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.faceOut {
  animation: leftIn 0.5s ease forwards;
  position: absolute;
}
// Open Module Visa & ATM
.btnPay-CardDrawer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  animation: right 0.5s ease forwards;
  .labelPopup {
    text-decoration: underline;
    color: #1da1f2;
    font-size: 14px;
    margin-bottom: 0;
  }
  .btn-pay-method {
    .textFont();
    display: flex;
    align-items: center;
    justify-content: left;
    gap: 8px;
    width: 100%;
    height: 50px;
    padding: 16px, 12px, 16px, 12px;
    border-radius: 12px;
    border: 1px solid #cbd2d9;
    white-space: normal;
    .icon {
      img {
        object-fit: contain;
        // transform: scale(1.5);
        width: 60px !important;
        height: 60px !important;
        max-width: 60px !important;
        max-height: 60px !important;
      }
    }
  }
}
// =====Popup Thông Báo ======
.customModalLocal {
  border-radius: 16px !important;
  :global {
    .ant-modal-content {
      border-radius: 16px;
      // padding: 12px;
    }
  }
}
.customModalNotification {
  .customModalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      font-family: Inter;
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      color: #11a2f3;
      margin-left: 38%;
      @media (max-width: 375px) {
        margin-left: 36%;
      }
    }
    .iconCloseModule {
      width: 26px;
      height: 26px;
      justify-content: flex-end;
    }
  }
  .customModalBody {
    .textFont();
    margin: 12px 0;
  }
  .customModalFooter {
    .mpButton {
      .mpButton();
      margin-top: 0;
    }
  }
}
// ======= Popup Ghi chú =======
.customModalNoteModule {
  border-radius: 16px !important;
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }
  }
}
.customModalNote {
  .customModalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    span {
      font-family: Inter;
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      color: #11a2f3;
      margin-left: 38%;
      @media (max-width: 375px) {
        margin-left: 36%;
      }
    }
    .iconCloseModule {
      width: 26px;
      height: 26px;
      justify-content: flex-end;
    }
  }
  .customModalBody {
    p {
      font-size: 16px;
      margin-bottom: 4px;
      font-weight: 600;
      line-height: 19.36px;
    }
    .customModalBody_Content {
      font-size: 16px;
      font-weight: 400;
      line-height: 18.75px;
      margin-bottom: 12px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
.paymentMethod {
  margin-top: 12px;
  .paymentMethodItem {
    width: 100%;
    height: 48px;
    border-radius: 12px;
    border: 1px solid #11a2f3;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px #bdc2cb33;
    text-align: left;
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
    svg {
      fill: #11a2f3 !important;
    }
    .contentMethod {
      display: flex;
      align-items: center;
      overflow: hidden;
      gap: 8px;
    }
    .iconMethod {
      img {
        object-fit: contain;
        // transform: scale(1.5);
        width: 50px;
        height: 30px !important;
        max-width: 50px;
        max-height: 30px !important;
      }
    }
  }
}
.modalConfirmPayment {
  .customModalHeader {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    position: relative;
    span {
      font-family: Inter;
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      color: #11a2f3;
      display: flex;
    }
    .iconCloseModule {
      position: absolute;
      width: 26px;
      height: 26px;
      right: 0;
    }
  }
  :global {
    .ant-btn-primary {
      width: 100%;
      height: 50px;
      border-radius: 12px;
    }
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-body {
      padding: 16px 16px 0 16px;
    }
    .ant-modal-close-x {
      display: none;
    }

    .ant-modal-footer {
      border: none;
      padding: 12px 16px;
    }
    .ant-modal-footer .ant-btn + .ant-btn:not(.ant-dropdown-trigger) {
      margin-left: 0 !important;
      background-color: #11a2f3;
    }
  }
  .title {
    font-size: 16px;
    img {
      max-height: 100px;
      object-fit: contain;
      width: 250px;
      margin-bottom: 15px;
    }
  }
  .description {
    padding: 15px;
    background: #cce5ff;
    border-radius: 5px;
    color: #004085;
    margin-top: 20px;
    font-size: 1rem;
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.optionChooseCard {
  animation: fadeIn 0.5s ease forwards;
  bottom: 0 !important;
  :global {
    .ant-drawer-mask {
      display: none;
    }
    .ant-drawer-content-wrapper {
      transition: none !important;
    }
  }
}
.specialistItem {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 8px;
  position: relative;
  width: 100%;
  margin-bottom: 12px;
  &:last-child {
    margin-bottom: 0px;
    justify-content: right;
  }
  .button {
    display: flex;
    width: 78px;
    height: 40px;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    background: #fee;
    color: var(--error, #ff3b30);
    border: 1px solid transparent;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    span {
      margin-left: -4px;
    }
    &:hover {
      color: var(--error, #ff3b30);
      border: 1px solid #ff3b30;
      background: #fee;
    }
  }
}
.modalInfoSchedule {
  :global {
    .ant-modal-content {
      border-radius: 12px;
    }
    .ant-modal-close-x {
      display: none !important;
    }
    .ant-modal-header {
      background-color: #1da1f2 !important;
      text-align: center;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
    }
    .ant-modal-body {
      padding: 12px 16px;
    }
    .ant-btn {
      border-radius: 12px;
      width: fit-content;
    }
  }
  .listInfoSchedule {
    list-style: none !important;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .item {
      display: flex;
      .key {
        min-width: 150px;
        display: flex;
        align-items: center;
        .icon {
          margin: 0 0.5rem;
          display: flex;
          align-items: center;
        }
      }
      .value {
        color: #1da1f2;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }
}
.groupButton {
  .btnSubmit {
    border-radius: 12px;
    width: 100%;
    height: 50px;
    font-size: 16px;
    background-color: #fff;
    border: 1px solid #11a2f3;
    color: #11a2f3;
  }
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 16px;
}

.paymentSkeleton {
  .hospitalTitle {
    .skeletonItemTitle {
      transform: translateY(6px);
    }
  }
  .patientInfo {
    .bookingInfo {
      column-gap: 8px;
      padding: 16px 16px 1px 16px;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      p {
        .textFont();
        display: flex;
        gap: 5px;
        align-items: center;
        svg {
          min-width: 18px !important;
          width: 18px;
        }
      }
    }
  }
  .customButtonNext {
    margin-top: 1rem;
    text-align: center !important;
    :global {
      .ant-skeleton-element {
        width: 100%;
      }
      .ant-skeleton-button {
        width: 100%;
      }
    }
  }
}

.hotline {
  display: flex;
  gap: 8px;
  justify-content: center;
  background: #f5f7fa;
  padding: 12px 0;
  border-radius: 8px;
  margin-bottom: 50px;
  .icon {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #11a2f3;
    border-radius: 50%;
    width: 34px;
    height: 34px;
  }
  p {
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
  }
  .title {
    font-size: 20px;
    font-weight: 500;
    line-height: 24.2px;
    color: #11a2f3;
  }
  .subTitle {
    font-size: 14px;
    font-weight: 400;
    line-height: 16.94px;
    color: #24313d;
  }
}
.form {
  input {
    height: 43px;
    border-radius: 8px;
    border-color: #cbd2d9;
  }
  :global {
    .ant-form-item-label label {
      font-size: 14px;
      font-weight: 500;
      line-height: 16.94px;
    }
  }
}
.requireInput {
  font-size: 100%;
  top: -0.2em;
  left: 5px;
  color: red;
}
.description {
  text-align: center;
}
.contentConfirm {
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 16.41px;

  color: #003553;
}
.ButtonControl {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  gap: 12px;
  button {
    height: 50px;
    width: 50%;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    line-height: 19.36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .cancelBtn {
    border: 1px solid #11a2f3;
    color: #11a2f3;
  }
  .confirmBtn {
    // background-color: #11a2f3;
    // color: white;
  }
}
