import Image from 'next/image'
import styles from './styles.module.less'
import subject_icon from './img/subject.svg'
import dollar_icon from './img/dollar.svg'
import hospital_icon from './img/hospital.svg'
import { getFormatMoney } from '../../../common/func'
import { AiFillStar } from 'react-icons/ai'
import { FaUser } from 'react-icons/fa'
import { Button, Popover } from 'antd'
import { useWindowDimensions } from '../../../hooks/useWindowDimesion'
import { useState } from 'react'
import DefaultDrawer from '../../DefaultDrawer'
export interface MPNewBookingPackageProps {
  isMobile: boolean
  dataDoctorTelemedProps: any
  handleBooking: (type: string, item: any) => void
  handleDetail: (pathname: string, query: any) => void
  handleTrackingEvent?: (data: { Section: string; Label: string }) => void
}
const CardDetailDoctorTelemed = (props: MPNewBookingPackageProps) => {
  const { dataDoctorTelemedProps } = props
  const { windowWidth } = useWindowDimensions()
  const [drawerCashBack, setDrawerCashBack] = useState(false)
  const [Visible, setVisible] = useState(false)
  const trigger = windowWidth > 576 ? 'hover' : 'click'
  const onShow = (e: any) => {
    e.stopPropagation()
    if (trigger === 'click') {
      setDrawerCashBack(true)
    }
  }
  const onHidden = (e: any) => {
    e.stopPropagation()
    setDrawerCashBack(false)
  }
  return (
    <div
      className={styles['DetailDoctorTelemed']}
      onClick={(e) => {
        e.preventDefault()
        props.handleDetail(
          `/bac-si/${dataDoctorTelemedProps.description?.slug}`,
          {}
        )
      }}
    >
      {dataDoctorTelemedProps?.hospitals?.[0]?.isCashBack && (
        <div className={styles['tagCashBack']} onClick={onShow}>
          <Popover
            showArrow={true}
            overlayClassName={styles['popoverCashBack']}
            overlayInnerStyle={{ width: 510 }}
            content={
              dataDoctorTelemedProps?.hospitals?.[0]?.popup?.content && (
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      dataDoctorTelemedProps?.hospitals?.[0]?.popup?.content
                  }}
                />
              )
            }
            onOpenChange={(visible) => {
              if (windowWidth > 576 && visible) {
                setVisible(true)
              } else {
                setVisible(false)
              }
            }}
            open={Visible}
            placement='bottomLeft'
          >
            Hoàn tiền
          </Popover>
        </div>
      )}
      <div className={styles['DetailDoctorTelemedCard']}>
        <div className={styles['DetailDoctorTelemedCardImage']}>
          <Image
            src={dataDoctorTelemedProps?.imageUrl || ''}
            layout='fixed'
            width={277}
            height={166}
            objectFit='cover'
            priority
            alt={dataDoctorTelemedProps?.title}
          />
        </div>
      </div>
      <div className={styles['DetailDoctorTelemedCardContent']}>
        <div className={styles['CardContentBodyEvaluate']}>
          <p className={styles['item']}>
            <label>Đánh giá:</label>
            <span>
              {dataDoctorTelemedProps?.description?.rating?.rate || 4.5}
            </span>
            <AiFillStar color='#FFB54A' size={16} />
          </p>
          <p className={styles['item']}>
            <label>Lượt khám:</label>
            <span>
              {dataDoctorTelemedProps?.description?.rating?.count || 100}
            </span>
            <FaUser color='#FFB54A' size={15} />
          </p>
        </div>
        <div className={styles['CardContentHeader']}>
          <h2 className={styles.role}>{dataDoctorTelemedProps?.role}</h2>
          <h2 className={styles.name}>{dataDoctorTelemedProps?.title}</h2>
        </div>
        <div className={styles['CardContentBody']}>
          <div className={styles['CardContentBodyText']}>
            <Image
              src={subject_icon}
              width={14.62}
              height={14.62}
              priority
              layout='fixed'
            />
            <p>{dataDoctorTelemedProps?.subjects?.[0]?.name}</p>
          </div>
          <div className={styles['CardContentBodyText']}>
            <Image
              src={dollar_icon}
              width={14.62}
              height={14.62}
              layout='fixed'
              priority
            />
            <p>{getFormatMoney(dataDoctorTelemedProps?.price)}</p>
          </div>
          <div className={styles['CardContentBodyText']}>
            <Image
              src={hospital_icon}
              width={16}
              height={16}
              layout='fixed'
              priority
            />
            <p>
              {dataDoctorTelemedProps?.description?.refHospital ||
                dataDoctorTelemedProps?.hospitals?.[0]?.name}
            </p>
          </div>
        </div>
      </div>
      <div className={styles['DoctorTelemedFoorterCard']}>
        <Button
          className={styles['DoctorTelemedButton']}
          onClick={(e) => {
            e.stopPropagation()
            props.handleTrackingEvent?.({
              Section: 'Doctor',
              Label: 'Tư vấn ngay'
            })
            props.handleBooking('doctor', dataDoctorTelemedProps)
          }}
        >
          Tư vấn ngay
        </Button>
      </div>
      <DefaultDrawer
        title={
          dataDoctorTelemedProps?.hospitals?.[0]?.popup?.title ||
          'Thông tin hoàn tiền'
        }
        className={styles['modalCashBack']}
        open={drawerCashBack}
        onClose={onHidden}
        children={
          <div
            dangerouslySetInnerHTML={{
              __html: dataDoctorTelemedProps?.hospitals?.[0]?.popup?.content
            }}
          />
        }
        height={'calc(70%)'}
        style={{ zIndex: 999999999 }}
      />
    </div>
  )
}
export default CardDetailDoctorTelemed
