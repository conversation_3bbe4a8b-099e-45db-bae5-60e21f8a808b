// @import '../../variables';

@media screen {
  .printBill_Print {
    visibility: hidden;
    height: 0;
  }
}
.printBill_Print {
  max-width: 100%;
  margin: auto;
  padding: 3px 17px;
  // background: #ffffff;
  border-radius: 16px;
  .printBillTitle {
    text-align: center;
    padding-top: 10px;
  }
  .printBillCode {
    margin: 20px 0;
  }
  .cancelMessage {
    font-size: 0.9rem;
    font-style: italic;
    color: red;
    font-weight: 300;
    line-height: 1.2rem;
    margin-top: 0.5rem;
    margin-bottom: 30px;
  }

  .greenNote {
    background-color: #3bb54a;
    font-size: 13px;
    display: inline-block;
    padding: 10px 25px;
    border-radius: 20px;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: white;
  }
  .redNote {
    background-color: red;
  }
  .greyNote {
    background-color: #c6cace;
  }

  .printBillFooter {
    padding-top: 20px;
    font-size: 17px;

    position: relative;
    // &::before,
    // &::after {
    //   content: '';
    //   position: absolute;
    //   top: -15px;
    //   width: 28px;
    //   height: 28px;
    //   background-color: #e8f2f7;
    //   border-radius: 50%;
    // }
    // &::after {
    //   right: -34px;
    // }
    // &::before {
    //   left: -34px;
    // }
  }
}

.noteBillContent {
  text-align: left;
  .attention {
    color: #df0000;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  .note {
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 16.94px;
    li {
      list-style: none;
    }
  }
}
.copyRight {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  > span {
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    color: black;
  }
}
.introMedpro {
  text-align: center;
  position: relative;
  padding-bottom: 19px;
  margin-top: 4px;
  font-size: 16px;
  line-height: 18px;
  font-weight: 400;
  &::before,
  &::after {
    content: '';
    position: absolute;
    bottom: -15px;
    width: 28px;
    height: 28px;
    background-color: #f6f6f6;
    border-radius: 50%;
  }
  &::after {
    right: -34px;
  }
  &::before {
    left: -34px;
  }
  &:last-child::before {
    background-color: #ffffff !important;
    width: 0;
    height: 0;
  }
  &:last-child::after {
    background-color: #ffffff !important;
    width: 0;
    height: 0;
  }
}

.modalContentBill {
  padding: 20px 0 0;
  position: relative;

  .customerLabelHeader {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 0 16px;
  }
  .BoxCustomerLabelHeader {
    margin: 0 8px;
    border: 1px solid black;
    padding: 8px;
    border-radius: 8px;
    margin-bottom: 12px;
    li {
      margin: 0;
    }
  }
  .customerLabelBody {
    margin: 0 16px;
  }
  .doctorLabel222 {
    padding: 0 8px;
  }
  .line {
    position: relative;
    display: flex;
    margin-top: 20px;
    padding-top: 20px;
    justify-content: left;
    font-size: 13px;
    font-weight: 700;
  }
  .label {
    // max-width: 72%;
    min-width: 110px;
    width: 35%;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
  }

  .greenText {
    color: #24313d;
  }

  .modalContentStt {
    color: #33b5e5;
    font-weight: 700;
    text-align: center;

    .modalContentSttNumber {
      margin: 30px 0;
      font-size: 50px;
      line-height: 1.5rem;
    }
  }
  .linkResult {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
    gap: 3px;
    a {
      color: #1da1f2;
      font-size: 13px;
      text-decoration-color: #1da1f2;
      text-decoration: underline;
      &:hover {
        color: #1da1f2;
      }
    }
  }
  ul {
    width: 100%;

    & > * ~ * {
      margin-top: 10px;
    }

    li {
      margin-bottom: 10px;
      width: 100%;
      display: flex;
      // justify-content: space-between;
      font-size: 13px;
      text-align: left;

      > article,
      b {
        font-weight: 700;
      }
    }
  }
}
.printBillCode {
  // margin: 20px 0;
  .notPaymentYetCountDown {
    margin-bottom: 16px;
    text-align: center;
    span {
      font-size: 16px;
      color: #24313d;
      line-height: 19px;
      font-weight: 400;
    }
    div {
      margin-top: 4px;
      font-size: 20px;
      color: #f5222d;
      font-weight: 500;
      line-height: 24px;
    }
  }
  .bar_code {
    min-width: 45%;
    .isNewUserTxt {
      line-height: normal;
    }
  }
  .center {
    justify-content: center !important;
  }
  .segmentCode {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .segmentInfo {
      border-radius: 8px;
      padding: 12px;
      min-width: 145px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      p {
        margin-bottom: 4px;
        font-size: 12px;
        font-weight: 400;
        line-height: 14.52px;
      }
      b {
        font-size: 20px;
        font-weight: 700;
        line-height: 19.36px;
      }
      .awaitMessage {
        b {
          color: #00b5f1;
        }
      }
      .boxSegmentInfo {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .segmentInfoTime {
        // margin-bottom: 8px;
      }
      .segmentInfoSTT {
      }
    }
  }
}
