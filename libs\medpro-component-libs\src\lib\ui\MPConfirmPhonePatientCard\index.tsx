import { size } from 'lodash'
import React from 'react'
import MPIcon from '../MPIcon'
import handleInfoPatient from './common/handleInfoPatient'
import styles from './styles.module.less'
import { MPPageTitle } from '@medpro-libs/libs'

interface Props {
  data: any[]
  handleSubmitPhonePatient: any
}

const MPConfirmPhonePatientCard = ({
  data,
  handleSubmitPhonePatient
}: Props) => {
  return (
    <div className={styles['confirmPhonePatient']}>
      <MPPageTitle title='Hồ sơ bệnh nhân' />
      <div className={styles['content']}>
        <ul className={styles['listPatient']}>
          {size(data) < 1 ? (
            <div className={styles['error']}>
              Không tìm thấy hồ sơ bệnh nhân!
            </div>
          ) : (
            data?.map((item: any, index) => (
              <li
                onClick={() => handleSubmitPhonePatient(item)}
                className={styles['item']}
                key={index}
              >
                {item?.isCreateNew && (
                  <div className={styles['itemTitle']}>
                    Hồ sơ vừa được tạo:{' '}
                  </div>
                )}
                <div className={styles['card']}>
                  {handleInfoPatient({ item }).map(
                    (element: any, i: number) => {
                      return element?.visible ? (
                        <div className={styles['element']} key={i}>
                          <span className={styles['label']}>
                            <MPIcon
                              name={element.icon as any}
                              size={18}
                              fill='#7B8794'
                            />
                            <span>{element?.label}</span>
                          </span>
                          <span className={styles['value']}>
                            {element?.value}
                            <span className={styles['extra']}>
                              {element?.extra}
                            </span>
                          </span>
                        </div>
                      ) : null
                    }
                  )}
                </div>
                {item?.isCreateNew && (
                  <div className={styles['itemRecommendTitle']}>
                    Hồ sơ gợi ý:
                  </div>
                )}
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  )
}

export default MPConfirmPhonePatientCard
