import { MPCreateProfileAppCompoment } from '../../component/MPCreateProfileAppCompoment'
import MPCreateProfileAppCard from '../../ui/MPCreateProfileAppCard'

interface Props {
  province: any[]
  onSubmit: (values: any) => void
  showPopup: boolean
  idForUpdate: string
  partnerId?: string
}
export const MPCreateProfileApp = ({
  province,
  onSubmit,
  showPopup,
  idForUpdate,
  partnerId
}: Props) => {
  return (
    <MPCreateProfileAppCompoment
      onSubmit={onSubmit}
      render={(handleSubmit: any) => (
        <MPCreateProfileAppCard
          province={province}
          handleSubmit={handleSubmit}
          showPopup={showPopup}
          idForUpdate={idForUpdate}
          partnerId={partnerId}
        />
      )}
    />
  )
}
