{"name": "medpro-libs", "version": "0.0.12", "license": "MIT", "scripts": {"start:id": "nx serve medpro-id", "start": "node dist/apps/medpro-ssr/server/main", "start:ssr": "nx serve medpro-ssr", "start:bo": "nx serve medpro-bo", "start:cskh": "nx serve medpro-cskh", "start:mono": "nx serve umc-mono", "build": "nx build medpro-component-libs", "test": "nx test", "prettier:fix": "prettier --write \"libs/**/(**.tsx|*.ts|*.less|*.css)\""}, "private": true, "dependencies": {"@casl/ability": "^6.3.3", "@fingerprintjs/fingerprintjs": "^3.3.6", "@firebase/analytics": "0.7.9", "@nrwl/next": "^14.7.13", "@reduxjs/toolkit": "^1.8.5", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "antd": "^4.23.2", "axios": "0.19.2", "babel-loader": "8.2.5", "classnames": "^2.3.2", "cookies-next": "^2.1.1", "core-js": "^3.6.5", "express": "^4.18.1", "fingerprintjs2": "^2.1.4", "firebase": "9.8.1", "framer-motion": "^7.3.6", "history": "^5.3.0", "html5-qrcode": "^2.3.8", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "^12", "next-compose-plugins": "^2.2.1", "next-fonts": "^1.5.1", "next-redux-wrapper": "^8.0.0", "next-seo": "5.15.0", "next-with-less": "^2.0.5", "qrcode.react": "^3.1.0", "querystring": "^0.2.1", "react": "18.2.0", "react-barcode": "1.4.2", "react-dom": "18.2.0", "react-gtm-module": "^2.0.11", "react-hook-form": "7.41.0", "react-icons": "^4.6.0", "react-jsbarcode": "^0.2.4", "react-json-editor-ajrm": "^2.5.13", "react-lazyload": "^3.2.0", "react-no-ssr": "1.1.0", "react-otp-input": "^2.4.0", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.2", "react-quill": "^2.0.0", "react-redux": "^8.0.4", "react-router-dom": "^6.4.0", "react-to-print": "^2.14.10", "redux-devtools-extension": "2.13.9", "redux-persist": "^6.0.0", "redux-saga": "^1.2.1", "regenerator-runtime": "0.13.7", "sharp": "^0.32.4", "slick-carousel": "^1.8.1", "tslib": "^2.3.0", "tsparticles": "^3.8.1", "uuid": "^9.0.0", "validator": "^13.7.0", "vanilla-jsoneditor": "^0.11.5"}, "devDependencies": {"@nrwl/cli": "14.7.5", "@nrwl/cypress": "14.7.5", "@nrwl/eslint-plugin-nx": "14.7.5", "@nrwl/jest": "14.7.5", "@nrwl/js": "14.6.5", "@nrwl/linter": "14.7.5", "@nrwl/nx-cloud": "latest", "@nrwl/react": "14.7.5", "@nrwl/web": "14.7.5", "@nrwl/workspace": "14.7.5", "@swc/core": "^1.2.173", "@swc/jest": "0.2.20", "@testing-library/react": "13.3.0", "@types/express": "^4.17.14", "@types/jest": "28.1.1", "@types/lodash": "4.14.184", "@types/node": "16.11.7", "@types/qrcode.react": "^1.0.2", "@types/react": "18.0.18", "@types/react-dom": "18.0.6", "@types/react-json-editor-ajrm": "^2.5.3", "@types/react-lazyload": "^3.2.0", "@types/react-no-ssr": "1.1.3", "@types/react-router-dom": "5.3.3", "@types/react-slick": "0.23.10", "@types/uuid": "^9.0.1", "@types/validator": "^13.7.2", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "babel-jest": "28.1.1", "cypress": "^10.7.0", "eslint": "~8.15.0", "eslint-config-next": "12.3.1", "eslint-config-prettier": "8.1.0", "eslint-plugin-cypress": "^2.10.3", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-react": "7.31.1", "eslint-plugin-react-hooks": "^4.6.0", "jest": "28.1.1", "jest-environment-jsdom": "28.1.1", "next": "^12", "next-compose-plugins": "2.2.1", "next-with-less": "^2.0.5", "nx": "14.7.5", "prettier": "^2.6.2", "react-slick": "^0.29.0", "react-test-renderer": "18.2.0", "ts-jest": "28.0.5", "ts-node": "10.9.1", "typescript": "~4.8.2"}}