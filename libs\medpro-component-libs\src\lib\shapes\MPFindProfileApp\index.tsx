import { MPFindProfileAppCompoment } from '../../component/MPFindProfileAppCompoment'
import MPFindProfileAppCard from '../../ui/MPFindProfileAppCard'

interface Props {
  onSubmit: (values: any) => void
  onSubmitFindByInfo: (values: any) => void
  province: any[]
  data: any[]
  onSelectPatient: (item: any) => void
  onConfirmPhoneModal: (phone: string) => void
  searching: boolean
  findExtra: boolean
  extraConfig: any
  partnerInfo: any
  selectedPatient: any
}
export const MPFindProfileApp = ({
  onSubmit,
  onSubmitFindByInfo,
  province,
  data,
  onSelectPatient,
  onConfirmPhoneModal,
  searching,
  findExtra,
  extraConfig,
  partnerInfo,
  selectedPatient
}: Props) => {
  return (
    <MPFindProfileAppCompoment
      onSubmit={onSubmit}
      onSubmitFindByInfo={onSubmitFindByInfo}
      onSelectPatient={onSelectPatient}
      onConfirmPhoneModal={onConfirmPhoneModal}
      render={(
        handleSubmit: any,
        handleSubmitFindByInfo: any,
        handleSelectPatient: any,
        handleConfirmPhoneModal: any
      ) => (
        <MPFindProfileAppCard
          handleSubmit={handleSubmit}
          handleSubmitFindByInfo={handleSubmitFindByInfo}
          province={province}
          data={data}
          handleSelectPatient={handleSelectPatient}
          handleConfirmPhoneModal={handleConfirmPhoneModal}
          searching={searching}
          findExtra={findExtra}
          extraConfig={extraConfig}
          partnerInfo={partnerInfo}
          selectedPatient={selectedPatient}
        />
      )}
    />
  )
}
