import { MPGuide } from '@medpro-libs/libs'
import { find } from 'lodash'
import { GetServerSidePropsContext } from 'next'
import { useRouter } from 'next/router'
import SEOHead from '../../../../src/components/SEOHead'
import { PageProps } from '../../../../src/type'
import { useAppSelector } from '../../../../store/hooks'
import { selectAppInfo } from '../../../../store/hospital/selector'
import { getAppInfo } from '../../../../utils/method'
import { handleSEOTitleSubPage, SEOPage } from '../../../../utils/SEOPage'
import { getBookingGuide } from '../../../../utils/utils.query-server'
import withBreadCrumb from '../../../../src/HOCs/withBreadCrumb'

interface GuideProps extends PageProps {
  meta: any
  data: any
}

const Guide = ({ meta, data }: GuideProps) => {
  const partnerInfo = useAppSelector(selectAppInfo)

  return (
    <>
      <div>
        {data ? (
          typeof data === 'string' ? (
            <div
              dangerouslySetInnerHTML={{
                __html: data
              }}
            />
          ) : Array.isArray(data) ? (
            <MPGuide data={data} isWebviewInApp={true} />
          ) : (
            <div
              dangerouslySetInnerHTML={{
                __html: data?.content?.content
              }}
            />
          )
        ) : (
          <div>Không có dữ liệu trả về</div>
        )}
      </div>
    </>
  )
}

export default withBreadCrumb([], { ssr: true })(Guide)

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const locale = (context.query.locale as string) || 'vi'

  const data = await getBookingGuide({ appId: 'momo', locale })

  return {
    props: {
      data,
      isWebView: true
    }
  }
}
