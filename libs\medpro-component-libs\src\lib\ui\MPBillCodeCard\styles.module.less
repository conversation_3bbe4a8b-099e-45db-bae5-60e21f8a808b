/* Font imported in _document.tsx for optimal performance */

.printBillCode {
  // margin: 20px 0;
  .notPaymentYetCountDown {
    margin-bottom: 16px;
    text-align: center;
    span {
      font-size: 16px;
      color: #24313d;
      line-height: 19px;
      font-weight: 400;
    }
    div {
      margin-top: 4px;
      font-size: 20px;
      color: #f5222d;
      font-weight: 500;
      line-height: 24px;
    }
  }
  .bar_code {
    width: 45%;

    .isNewUserTxt {
      line-height: normal;
    }
  }
  .center {
    justify-content: center !important;
  }
  .segmentCode {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .segmentInfo {
      border: 1px solid #11a2f3;
      border-radius: 8px;
      padding: 12px;
      min-width: 145px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      p {
        margin-bottom: 4px;
        font-size: 12px;
        font-weight: 400;
        line-height: 14.52px;
      }
      b {
        font-size: 16px;
        font-weight: 700;
        line-height: 19.36px;
      }
      .awaitMessage {
        b {
          color: #00b5f1;
        }
      }
      .boxSegmentInfo {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .segmentInfoTime {
        // margin-bottom: 8px;
      }
      .segmentInfoSTT {
      }
    }
  }
}
.cancelMessage {
  font-size: 0.9rem;
  font-style: italic;
  color: red;
  font-weight: 300;
  line-height: 1.2rem;
  margin-top: 0.5rem;
  margin-bottom: 30px;
}
.barcode {
  font-family: 'Libre Barcode 128 Text', cursive;
  font-size: 40px;
}
.billDescription {
  margin: 20px;
}

.segmentTotalFeeMessage {
  margin-top: 1rem;
  text-align: center;

  .totalPaymentMessage {
    color: orangered;
    font-weight: 600;
  }

  .totalMessageExtra {
    color: orangered;
    font-size: 0.75rem;
  }
}

.greenNote {
  background-color: #0eb429;
  font-size: 13px;
  display: inline-block;
  padding: 10px 25px;
  border-radius: 20px;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  color: white;
}
.redNote {
  background-color: red;
}
.greyNote {
  background-color: #c6cace;
}
.cancelMessage {
  font-style: italic;
  color: #df0000;
  margin-bottom: 1rem;
}
.notPaymentYetDescription {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;

  span {
    margin-top: 16px;
    font-weight: 500;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    flex-direction: column;
    p {
      font-size: 16px;
      font-weight: 400;
      line-height: 19px;
      margin-bottom: 12px;
    }
    .YetDescriptionText {
      width: fit-content;
      // max-height: 35px;
      border-radius: 8px;
      border: 0.5px solid #f5222d;
      background: #fff3f4;
      font-size: 16px;
      font-weight: 400;
      line-height: 19px;
      color: #f5222d;
      padding: 8px 16px;
    }
  }
}
.isCSKHApp {
  border-bottom: 2px dashed #f5f5f5;
  &::before,
  &::after {
    content: '';
    position: absolute;
    bottom: -15px;
    width: 28px;
    height: 28px;
    background-color: #f5f5f5;
    border-radius: 50%;
  }
  &::after {
    right: -34px;
  }
  &::before {
    left: -34px;
  }
}
.notPaymentYetMessage {
  text-align: start;
}
// .notPaymentYetMessage {
//   background: #fff8eb;
//   border: 1px solid #ffcc80;
//   box-sizing: border-box;
//   border-radius: 8px;
//   display: flex;
//   flex-direction: row;
//   align-items: flex-start;
//   padding: 8px;
//   margin-bottom: 15px;
//   text-align: left;
//   svg {
//     fill: white;
//     font-size: 20px;
//     padding: 3px;
//     border-radius: 50%;
//     background-color: #ffcc80;
//     margin-top: 2px;
//     margin-right: 5px;
//   }
//   div {
//     flex: 1;
//     font-size: 0.895rem !important;
//   }
// }

.notPaymentYetButton {
  display: flex;
  justify-content: space-between;
  column-gap: 8px;
  width: 100%;
  margin: 12px 0;
  .rePaymentBtn,
  .sharePaymentBtn {
    color: #fff;
    text-transform: none;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    font-weight: 400;
    margin: 0;
    padding: 8px 15px;
    border-radius: 10px;
    border: 1px solid #11a2f3;
    outline: none;
    box-shadow: none;
    min-width: calc(50% - 4px);
    label {
      font-family: Roboto;
      font-size: 14px;
      font-weight: 400;
      line-height: 14px;
      text-align: left;
    }
    &:hover {
      box-shadow: rgba(0, 0, 0, 0.19) 0px 5px 15px,
        rgba(0, 0, 0, 0.23) 0px 6px 6px;
      transform: translateY(-0.2rem);
      cursor: pointer;
    }

    @media screen and (max-width: 1024px) {
      div {
        font-size: 0.775rem;
      }
    }
  }
  .onlyButton {
    min-width: 100% !important;
  }
  .rePaymentBtn {
    background-color: #11a2f3 !important;
    &:hover {
      color: white;
    }
  }
  .sharePaymentBtn {
    background-color: white !important;
    color: #11a2f3;
  }
}

.notPaymentYetShortMessage {
  background: #f5f7fa;
  box-sizing: border-box;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  justify-content: center;
  margin-bottom: 21px;
  button {
    background: #f5f7fa;
    color: #01b5f1;
    border: none;
    text-align: left;
    gap: 9px;
    align-items: flex-start !important;
    padding: 0;

    .notPaymentYetShortMessageCallText {
      a {
        color: #11a2f3;
        font-weight: 500;
        font-size: 20px;
        line-height: 24px;
        span {
          font-weight: 400 !important;
        }
      }
      p {
        font-size: 14px;
        font-weight: 400;
        line-height: 17px;
        color: #24313d;
        margin-bottom: 0;
      }
    }
  }
}
