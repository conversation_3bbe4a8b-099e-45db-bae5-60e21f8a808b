@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.textFont() {
  //styleName: h2: Regular;
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  text-align: left;
  color: #24313d;
}
.confirmPage {
  padding: 12px 16px;
  background: #f6f6f6;
  :global {
    .ant-form-item {
      margin-bottom: 0;
      margin-top: 8px;
    }
    .ant-form-item-label {
      padding: 0 0 4px;
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      height: max-content;
      margin-bottom: 8px;
    }
    .ant-form-item-control-input-content {
      padding: 10px, 12px, 10px, 12px !important;
      border-radius: 12px !important;
      border: 1px !important;
      justify-content: space-between !important;
      border: 1px solid #e3e7eb !important;
      height: 50px !important;
    }
    .ant-input-affix-wrapper {
      height: 50px !important;
      border-radius: 12px !important;
    }
    .ant-input-affix-wrapper > input.ant-input {
      margin-top: 4px !important;
    }
  }
}
.disabledSelect {
  // background-color: red !important;
  pointer-events: none;
  svg {
    color: #d9d9d9;
  }
}
.nextStep {
  border-color: #52575c !important;
}
.hiddenItem {
  display: none;
}
.itemRenderTitle {
  font-size: 16px;
  font-weight: 500;
  line-height: 19.36px;
  text-align: left;
  margin-bottom: 8px;
  margin-top: 12px;
  color: #24313d;
}
.itemSelectService {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.itemRenderContentSevice {
  // display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 12px;
  border: 1px solid #d9d9d9;
  background: #fff;
  .titleService {
    max-width: 100%;
    width: fit-content;
    border-radius: 8px;
    margin-top: 8px;
    color: #11a2f3;

    padding: 8px 12px;
    background-color: #eff7ff;
    .umcBhyt {
      margin-bottom: 0;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.94px;
      text-align: left;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .itemContent {
    display: flex;
    align-items: center;
    gap: 8px;
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: left;
      color: #24313d;
    }
  }
}
.itemRenderContent {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 12px;
  border: 1px solid #d9d9d9;
  background: #fff;
  .itemContent {
    display: flex;
    align-items: center;
    gap: 8px;
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: left;
      color: #24313d;
    }
  }
}
.values {
  animation: fadeIn 0.5s;
}

.empty {
}
.inputSelect {
  :global {
    .ant-input {
      color: transparent !important;
      text-shadow: 0 0 0 rgba(0, 0, 0, 0.85) !important;
      &:focus {
        outline: none !important;
      }
    }
  }
}
// .dateAndTime {
.customSelectDone {
  border-color: #11a2f3 !important;
}
.itemBhyt {
  border: 1px solid #eaeaea;
  padding: 8px;
  border-radius: 12px;
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 0;
  }
  p {
    margin-bottom: 0;
    font-family: 'Roboto' !important;
    font-size: 16px;
    font-weight: 400;
  }
  &:hover {
    border: 1px solid #00b5f1;
    color: #00b5f1;
  }
}
.dateTimeDivider {
  padding: 10px 20px;

  .divider {
    border-top: 2px solid #00e0ff;
    flex: 1;
  }

  max-width: 100%;
  display: flex;
  justify-content: space-between;
  align-content: center;
  justify-items: center;
  align-items: center;

  button {
    font-weight: 600;
  }
}

.time {
  padding: 0 20px;
}
.noteCalendar {
  margin-bottom: 20px;
}
.gtmMessage {
  margin: 20px 20px 0;
  font-size: 1rem;
  p {
    margin-bottom: 0;
  }
}
.noteUMC1 {
  font-size: 1rem;
  font-size: 15px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
}
.advertiseTelemed {
  @media (max-width: 576px) {
    margin-top: 12px;
  }
  img {
    @media (max-width: 576px) {
      min-width: 364px;
      min-height: 97px;
    }
  }
  .adviseTelemed {
    @media (max-width: 576px) {
      flex-direction: column;
      align-items: flex-start;
      padding: 12px;
      gap: 8px;
    }
    width: 100%;
    background: linear-gradient(92deg, #e1f6ff 2.01%, #fff 99.58%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #00b5f1;
    border-radius: 12px;
    box-shadow: 0px 4px 12px 0px rgba(178, 178, 178, 0.25);

    .adviseText {
      @media (max-width: 576px) {
        width: 100%;
      }
      width: 463px;
      p {
        color: #003553;
        font-style: normal;
        margin-bottom: 0;
        line-height: normal;
      }
      .title {
        @media (max-width: 576px) {
          width: 100%;
          font-size: 20px;
          margin-bottom: 8px;
        }
        width: 420px;
        font-family: 'Montserrat' !important;
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
      }
      .description {
        @media (max-width: 576px) {
          font-size: 16px;
        }
        font-family: 'Roboto' !important;
        font-size: 20px;
        font-weight: 400;
      }
    }
    .button-link {
      @media (max-width: 576px) {
        width: 134px;
        height: 37px;
        font-size: 14px;
      }
      height: 50px;
      border-radius: 105.769px;
      background: linear-gradient(84deg, #00b5f1 33.34%, #00e0ff 113.91%);

      font-family: 'Montserrat' !important;
      color: white;
      font-size: 20px;
      font-style: italic;
      font-weight: 700;
      line-height: normal;
    }
  }
}
.subjectItem {
  border: 1px solid #d7dbe0;
  min-height: 50px;
  border-radius: 12px;
  margin-bottom: 16px;
  padding: 12px 12px 13px 12px;
  .content {
    .title {
      font-size: 16px;
      font-weight: 400;
      line-height: 19px;
      letter-spacing: 0em;
      text-align: left;
      color: #24313d;
      margin-bottom: 4px;
    }
    p {
      margin: 0;
      font-size: 14px;
      font-weight: 400;
      line-height: 17px;
      letter-spacing: 0em;
      text-align: left;
      color: #627792;
    }
  }
}
.serviceItem {
  border: 1px solid #d7dbe0;
  min-height: 50px;
  border-radius: 12px;
  margin-bottom: 16px;

  .service {
    padding: 12px 12px 13px 12px;
  }
  .serviceName {
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
    color: #11a2f3;
    margin-bottom: 4px;
  }
  .serviceDes,
  .serviceCal {
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: 0em;
    text-align: left;
    color: #24313d;
    margin-bottom: 4px;
  }
  .servicePrice {
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
    color: #ffb54a;
  }
  .content {
    .title {
      font-size: 16px;
      font-weight: 400;
      line-height: 19px;
      letter-spacing: 0em;
      text-align: left;
      color: #11a2f3;
      margin-bottom: 4px;
    }
    p {
      margin: 0;
      font-size: 14px;
      font-weight: 400;
      line-height: 17px;
      letter-spacing: 0em;
      text-align: left;
    }
  }
}
.itemSelect {
  border-color: #00b5f1 !important;
}
.groupNote {
  display: flex;
  justify-content: space-between;
  .note {
    display: flex;
    gap: 8px;
    margin-top: 24px;

    p {
      min-width: 24px;
      min-height: 24px;
      margin-bottom: 0;
      border-radius: 4px;

      border: 0.5px solid #d7dbe0;
    }
    span {
      font-size: 13px;
    }
  }
}
.stickyButton {
  position: absolute;
  bottom: 0;
  width: 100%;
  // background-color: red;
  background: #f6f6f6;
  padding: 4px 16px 16px 16px;
}
.mpButton {
  // margin-top: 16px;
  width: 100%;
  height: 50px !important;
  padding: 12px;
  border-radius: 12px;
  gap: 8px;
  background: #11a2f3;
  color: #fff;
  span {
    width: 64px;
    height: 19px;
    font-size: 16px;
  }
}
.subClass {
  margin-top: 12px;
}
.notePickDate {
  font-size: 14px;
  font-weight: 400;
  line-height: 14px;
  letter-spacing: 0em;
  text-align: left;
  color: #11a2f3;
  background: #eff7ff;
  padding: 10px 12px 10px 12px;
  border-radius: 12px;
  // text-align: center;
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
  align-items: flex-start;
  span {
    margin-top: -2px;
  }
  p {
    margin: 0;
  }
}
.titleTime {
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  letter-spacing: 0em;
  text-align: left;
  color: #24313d;
  margin-bottom: 16px;
}
.groupItemTime {
  width: 100%;
  .btnTime {
    //color: #00b5f1;
    //background: #d6f3fd;
    height: 41px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 8px 12px 8px;
    border-radius: 8px;
    border: 0.5px solid #d7dbe0;
    background-color: #f0f9ff;
    font-size: 14px;
    font-weight: 500;
    line-height: 17px;
    letter-spacing: 0px;
    text-align: center;
    color: #11a2f3;

    @media only screen and (max-width: 576px) {
      padding: 12px;
      width: 100%;
    }
    &:hover,
    &:focus {
      background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
      color: #ffffff !important;
      cursor: pointer;
      //box-shadow: 0px 2px 10px 8px #f7f7f7;
      //transform: translateY(-0.2rem);
    }
    &:disabled {
      background-color: #d7dbe0 !important;
      color: #ffffff !important;
      pointer-events: none;
      cursor: not-allowed;
      border-color: #f5f5f5;
    }
  }
}
.formBooking {
  :global {
    .ant-input-affix-wrapper-disabled {
      background-color: #fff;
      &:hover {
        border-color: #11a2f3;
      }
    }
    .ant-input[disabled] {
      color: rgba(0, 0, 0, 0.85) !important;
    }
  }
}
.validRange {
  height: 49px !important;
  text-align: center;
  padding-top: 13px;
  background-color: #e0efff;
  border: 1px solid #ffffff !important;
  color: #11a2f3;
}
.calendar {
  box-shadow: 0px 5px 12px 0px #adb2c833;
  .cellHoliday {
    height: 49px !important;
    text-align: center;
    font-weight: bold;
    padding-top: 13px;
    background-color: rgb(253, 227, 178);
    border: 1px solid #ffffff !important;
    .holiday {
      margin-top: -4px;
    }

    pointer-events: none !important;
  }
  .currentDate {
    border: 1px solid #11a2f3 !important;
  }
  .cellDate {
    // height: 50px;
    height: 49px !important;
    padding: 13px 12.14px 12px 12.86px !important;
    // border-radius: 8px !important;
    color: #7b8794;
    background: #f5f7fa !important;
    border: 1px solid #ffffff !important;
    margin: 0 !important;
    text-align: center;
    position: relative;
    pointer-events: none;
  }
  .holiday {
    color: red;
    font-size: 10px;
    text-align: center;
    font-weight: 500;
    display: flex;
    justify-content: center;
  }

  .dateItem {
    background-color: #11a2f3;
    height: 46px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    align-items: center;
    color: #fff;
  }
  :global {
    .ant-picker-calendar-date {
      height: 49px !important;
      padding: 13px 12.14px 12px 12.86px !important;
      // border-radius: 8px !important;
      // background: #d7dbe0 !important;
      border: 1px solid #ffffff !important;
      margin: 0 !important;
      text-align: center;
    }

    .ant-picker-body {
      padding: 0 !important;
    }
    .ant-picker-cell-disabled {
      background: #d7dbe0 !important;
      color: #fff !important;
    }
    .ant-picker-cell-in-view {
      background-color: #f0f9ff !important;
      color: #11a2f3 !important;
    }
    .ant-picker-cell-today {
      border: 1px solid #11a2f3 !important;
    }
    .ant-picker-content {
      thead {
        text-align: center !important;
        height: 49px !important;
        th {
          padding: 0;
        }
      }
    }
  }
}

.hospitalTitle {
  column-gap: 8px;
  align-items: center;
  background: #ffffff;
  padding: 12px;
  border: 1px solid #11a2f3;
  border-radius: 8px;
  svg {
    margin-top: 1px;
    vertical-align: top;
  }
  img {
    width: 35px;
    height: 35px;
  }
  h1 {
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
    color: #24313d;
    margin-bottom: 4px;
  }
  p {
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
    color: #627792;
    margin-bottom: 0;
  }
}
.modal {
  .modalButton {
    width: 100%;
    height: 50px;
    padding: 12px 10px 12px 10px;
    border-radius: 12px;
    background: #11a2f3;
    color: white;
    font-size: 16px;
    margin-top: 8px;
  }
  :global {
    .ant-modal-content {
      border-radius: 12px;
      position: relative;
    }
    .ant-modal-body {
      padding: 16px;
      max-height: 70vh;
      overflow: auto;
    }
    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: center;
      color: #11a2f3;
    }
    .ant-modal-close-x {
      position: absolute;
      right: 0;
      top: -35px;
      opacity: 0.8;
      border: none;
      background-color: #fff;
      border-radius: 5px;
      font-size: 17px;
      line-height: 0;
      padding: 15px 13px;
      color: #0352cc;
      width: 25px;
      height: 25px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-content: center;
      svg {
        margin-top: -7px;
      }
    }
    .ant-modal-header {
      background-color: #fff !important;
      border-bottom: none;
      // padding-bottom: 0;
      border-radius: 12px;
      padding: 15px 16px 10px 16px !important;
    }
  }
}
.error {
  font-family: 'Roboto', sans-serif !important;
  text-align: center;
  height: 50vh;
  color: #7b8794;
  font-size: 25px;
  font-weight: 500;
  line-height: normal;
  margin-top: 45px;
  p {
    //styleName: h2: Medium;
    text-align: center;
    margin-top: 16px;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    line-height: 19.36px;
  }
}
.viewMove {
  width: 100%;
  margin-bottom: 16px;
  p {
    color: var(--primary-body-text, #003553);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin: 0;
  }
  text-align: center;
  display: none;
  @media (max-width: 576px) {
    display: block;
  }
  span {
    margin-top: -100px;
  }
  img {
    animation-name: bounceAlpha;
    animation-duration: 1.4s;
    animation-delay: 0.2s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }
  &:hover {
    cursor: pointer;
  }
}
.loadingRing {
  display: inline-block;
  position: relative;
  width: 50px;
  height: 50px;
  text-align: center;
  align-items: center;
  margin: 0 auto;
  margin-bottom: 16px;
  div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 44px;
    height: 44px;
    border: 6px solid #00b5f1;
    border-radius: 50%;
    animation: loadingRing 1.6s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #00b5f1 transparent transparent transparent;
  }
  div:nth-child(1) {
    animation-delay: -0.45s;
  }
  div:nth-child(2) {
    animation-delay: -0.3s;
  }
  div:nth-child(3) {
    animation-delay: -0.15s;
  }
}
@keyframes loadingRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.checkBHYTMobile {
  width: 100%;
  display: none !important;
  @media (max-width: 768px) {
    display: flex !important;
  }
  height: 43px;
  border-bottom: none !important;
  text-align: center;
  display: flex;
  justify-content: space-between;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: left;

  .checkBHYTWrapper {
    display: flex;
    justify-content: space-between;
    // padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .name {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 16px;
  }
  .inputRadio {
    font-weight: 600;
    width: 33%;
    justify-content: right;
    align-items: center;
    display: flex;
    :global {
      .ant-radio-group {
        display: flex;
      }
    }
  }

  background: #f0f9ff;
}
.bookingInfo {
  margin-bottom: 8px;
  column-gap: 8px;
  padding: 16px;
  align-items: center;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #eaeaea;
  p {
    .textFont();
    &:last-child {
      margin-bottom: 0;
    }
    display: flex;
    gap: 5px;
    align-items: center;
    .iconSvg {
      min-width: 18px !important;
      width: 18px;
    }
    strong {
      color: #24313d;
      font-weight: 600;
    }
  }
  .specialistItem {
    align-items: flex-start;
    align-self: stretch;
    display: flex;
    flex: 0 0 auto;
    gap: 8px;
    position: relative;
    width: 100%;
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0px;
      justify-content: right;
    }
    .button {
      display: flex;
      width: 78px;
      height: 40px;
      padding: 8px 16px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      border-radius: 8px;
      background: #fee;
      color: var(--error, #ff3b30);
      border: 1px solid transparent;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      span {
        margin-left: -4px;
      }
      &:hover {
        color: var(--error, #ff3b30);
        border: 1px solid #ff3b30;
        background: #fee;
      }
    }
  }
  .detailSubject {
    padding-bottom: 12px;
    border-bottom: 1px dashed #bebebe;
    margin-bottom: 12px;
    &:last-child {
      padding-bottom: 0;
      border-bottom: none;
      margin-bottom: 0;
    }
  }
}
.titleListSubject {
  font-size: 16px;
  font-weight: 500;
  line-height: 19.36px;
  margin-top: 12px;
  margin-bottom: 8px;
}
