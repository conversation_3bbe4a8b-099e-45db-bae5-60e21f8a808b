import { Drawer } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import Link from 'next/link'

import { useRouter } from 'next/router'
import { useState } from 'react'
import {
  AiOutlineBell,
  AiOutlineClose,
  AiOutlineFileText,
  AiOutlineUser
} from 'react-icons/ai'
import { BiUser } from 'react-icons/bi'
import { CiSearch } from 'react-icons/ci'
import { FaFacebookF, FaTiktok, FaYoutube } from 'react-icons/fa'
import { MdOutlineFileDownload } from 'react-icons/md'
import { HIDE_SEARCH_INTO_BOOKING } from '../../../common/constant'
import MPSearchTyping from '../../../shapes/MPSearchTyping'
import MPButton from '../../MPButton'
import MPIcon from '../../MPIcon'
import MPHeaderBarMobile from '../common/MPHeaderBarMobile'
import MPMenuMobileItem from '../common/MPMenuMobileItem'
import CSKH from './../common/images/CSKH.svg'
import Facebook from './../common/images/Facebook.svg'
import Gmail from './../common/images/Gmail.svg'
import Zalo from './../common/images/Zalo.svg'
import styles from './styles.module.less'
import { FiMenu } from 'react-icons/fi'

interface IFHeadMobile {
  menuHeader: any[]
  author: any
  totalNoti: any
  handleLogin: () => void
  handleLogOut: () => void
  onSearchDebounce: (event: any) => Promise<void>
  handleBookingSearch: ({ type, item }: any) => Promise<void>
  searchData: any
  searching: any
  logoWhite: any
  userToken: any
  // downloadApp: any
  appInfo: any
  featureSelected: any
  cashBack: any
}

export const MobileHeader = ({
  menuHeader,
  author,
  handleLogin,
  handleLogOut,
  onSearchDebounce,
  handleBookingSearch,
  searching,
  searchData,
  logoWhite,
  userToken,
  // downloadApp,
  totalNoti,
  appInfo,
  featureSelected,
  cashBack
}: IFHeadMobile) => {
  const [visibleMenu, setVisibleMenu] = useState(false)
  const [showSearch, setShowSearch] = useState(false)
  const iconZalo = require('../common/images/iconZalo.svg')
  const imgDown = require('../common/images/imgDownApp.svg')
  const router = useRouter()
  const behaviorAnKhang = router.query?.behavior === 'AnKhang'
  const toggleMenuMobile = () => {
    setVisibleMenu(!visibleMenu)
  }
  const handleSearch = () => {
    setShowSearch(!showSearch)
  }
  const handleBooking: any = ({ type, item }: any) => {
    setShowSearch(false)
    handleBookingSearch({
      type,
      item
    })
  }
  return (
    <>
      {showSearch && (
        <>
          <div
            className={styles['mobileMask']}
            onClick={(e) => {
              e.stopPropagation()
              setShowSearch(false)
            }}
          ></div>
          <div className={styles['mobileSearch']}>
            <MPSearchTyping
              mobileSearch
              onSearchDebounce={onSearchDebounce}
              searchData={searchData}
              searching={searching}
              handleBookingSearch={handleBooking}
              setShowSearch={setShowSearch}
            />
          </div>
        </>
      )}
      <MPHeaderBarMobile
        logoWhite={logoWhite}
        toggleMenuMobile={toggleMenuMobile}
        visibleMenu={visibleMenu}
        featureSelected={featureSelected}
        cashBack={cashBack}
      >
        {!HIDE_SEARCH_INTO_BOOKING.includes(
          router.pathname.split('/').pop() || '/'
        ) && (
          <MPButton
            onClick={handleSearch}
            type='default'
            className={styles['btnSearch']}
          >
            <CiSearch size={25} />
          </MPButton>
        )}
        <MPButton
          onClick={toggleMenuMobile}
          type='default'
          className={styles['btnShowMenu']}
        >
          <FiMenu size={24} color='#003553' />
        </MPButton>
      </MPHeaderBarMobile>
      <Drawer
        contentWrapperStyle={{ width: '100vw' }}
        className={cx(styles['menuMobile'])}
        open={visibleMenu}
        closeIcon=''
        title={
          <MPHeaderBarMobile
            toggleMenuMobile={toggleMenuMobile}
            visibleMenu={visibleMenu}
            className={styles['headerMenu']}
            logoWhite={logoWhite}
          >
            <MPButton
              onClick={toggleMenuMobile}
              type='default'
              className={styles['btnShowMenu']}
            >
              <AiOutlineClose color='#003553 ' size={24} />
            </MPButton>
          </MPHeaderBarMobile>
        }
      >
        {!behaviorAnKhang && (
          <>
            <div className={styles['topMenu']}>
              <div className={styles['author']}>
                {!userToken ? (
                  <div className={styles['boxControl']}>
                    <MPButton
                      className={cx(styles['btn'], styles['register'])}
                      onClick={handleLogin}
                      type='default'
                    >
                      Đăng ký
                    </MPButton>
                    <MPButton
                      className={cx(styles['btn'], styles['login'])}
                      onClick={handleLogin}
                      type='default'
                    >
                      Đăng nhập
                    </MPButton>
                  </div>
                ) : (
                  <div className={styles['user']}>
                    <MPButton className={styles['userBtn']}>
                      <BiUser color='white' size={24} />
                      <span>{author?.data?.fullname}</span>
                    </MPButton>
                  </div>
                )}
              </div>
            </div>
            {userToken && (
              <div className={styles['userMenu']}>
                <MPMenuMobileItem
                  data={userMenu}
                  totalNoti={totalNoti}
                  toggleClick={toggleMenuMobile}
                />
              </div>
            )}
          </>
        )}
        <div className={styles['mainMenu']}>
          <MPMenuMobileItem data={menuHeader} toggleClick={toggleMenuMobile} />
        </div>
        {!behaviorAnKhang && (
          <div className={styles['supportMenu']}>
            <MPMenuMobileItem
              isSupport={true}
              data={supportMenu(appInfo)}
              toggleClick={toggleMenuMobile}
            />
          </div>
        )}
        <Link href={'https://medpro.vn/getapp'}>
          <a target='_blank' className={styles['downloadApp']}>
            <div className={styles['Image']}>
              <Image
                src={imgDown}
                title='Icon Dowload Medpro'
                alt='Icon Dowload Medpro'
                width={42}
                height={60}
              />
            </div>
            <div className={styles['Content']}>
              <label>
                Tải ứng dụng Medpro tại đây <MdOutlineFileDownload size={18} />
              </label>
              <p>
                Ứng dụng đặt khám nhanh tại hơn 300 bệnh viện hàng đầu Việt Nam
              </p>
            </div>
          </a>
        </Link>
        {userToken && !behaviorAnKhang && (
          <div className={styles['logout']}>
            <MPButton
              className={styles['logoutBtn']}
              onClick={() => {
                handleLogOut()
                setVisibleMenu(false)
              }}
            >
              <MPIcon name='LogOut' fill='#FFFFFF' size={24} />
              <span>Đăng xuất</span>
            </MPButton>
          </div>
        )}
        {!behaviorAnKhang && (
          <div className={styles['network']}>
            <div>
              <Link
                href={'https://www.tiktok.com/@medprovn/'}
                className={styles['social']}
              >
                <a target='_blank'>
                  <div className={styles['icon']}>
                    <FaTiktok size={12} />
                  </div>
                  Tiktok
                </a>
              </Link>
            </div>
            <div>
              <Link
                href={'https://www.facebook.com/www.medpro.vn'}
                className={styles['social']}
              >
                <a target='_blank'>
                  <div className={styles['icon']}>
                    <FaFacebookF size={12} />
                  </div>
                  Facebook
                </a>
              </Link>
            </div>
            <div>
              <Link
                href={'https://zalo.me/4018184502979486994'}
                className={styles['social']}
              >
                <a target='_blank'>
                  <div className={styles['icon']}>
                    <Image
                      className={styles['iconZalo']}
                      src={iconZalo}
                      alt='Icon Zalo'
                      width={14}
                      height={14}
                    />
                  </div>
                  Zalo
                </a>
              </Link>
            </div>
            <div>
              <Link
                href={'https://www.youtube.com/@medpro-datkhamnhanh'}
                className={styles['social']}
              >
                <a target='_blank'>
                  <div className={styles['icon']}>
                    <FaYoutube size={12} />
                  </div>
                  Youtube
                </a>
              </Link>
            </div>
          </div>
        )}
      </Drawer>
    </>
  )
}
export const userMenu = [
  {
    label: 'Hồ sơ bệnh nhân',
    icon: <AiOutlineUser color='#4ECCF5' />,
    link: '/user?key=records',
    query: { key: 'records' }
  },
  {
    label: 'Phiếu khám bệnh',
    icon: <AiOutlineFileText color='#4ECCF5' />,
    link: '/user?key=bills',
    query: { key: 'bills' }
  },
  {
    label: 'Thông báo',
    icon: <AiOutlineBell color='#4ECCF5' />,
    link: '/user?key=notifications',
    query: { key: 'notifications' }
  }
]
export const supportMenu = (appInfo: any) => [
  {
    label: 'Hỗ trợ Zalo',
    icon: <Image src={Zalo} alt='Logo Zalo' layout='responsive' />,
    link: 'https://zalo.me/4018184502979486994'
  },
  {
    label: 'Hỗ trợ Facebook',
    icon: <Image src={Facebook} alt='Logo Facebook' layout='responsive' />,
    link: 'https://m.me/www.medpro.vn'
  },
  {
    label: 'Hỗ trợ đặt khám: 1900-2115',
    icon: <Image src={CSKH} alt='Logo CSKH' layout='responsive' />,
    link: `tel:${appInfo?.hotlineMedpro}`
  },
  {
    label: `Email: ${appInfo?.email}`,
    icon: <Image src={Gmail} alt='Logo Gmail' layout='responsive' />,
    link: `mailto:${appInfo?.email}`
  }
]
