import { <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import { useState } from 'react'
// import { AiFillStar } from 'react-icons/ai'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import { useWindowResize } from '../../../common/func'
import MPButton from '../../MPButton'
import styles from './styles.module.less'
import { capitalizeWords } from '../../MPSuggestCard/common/function'

const { Paragraph } = Typography

export const BannerDoctor = ({ data, handleBooking }: any) => {
  const isMobile = useWindowResize(576)
  const [warningBooking, setWarningBooking] = useState<boolean>()
  const [openBookingTelemed, setOpenBookingTelemed] = useState<any>(false)
  const [ellipsis, setEllipsis] = useState(true)

  return (
    <div className={styles['cardDoctor']}>
      <Row>
        <Col xs={24} sm={24} lg={24} className={styles['leftGroup']}>
          <div className={styles['logoImg']}>
            <Image
              src={data.imageUrl}
              alt={`${data.role} ${data?.title}`}
              layout='fill'
              objectFit='cover'
              objectPosition='top'
              style={{ borderRadius: '8px' }}
            />
          </div>

          <div className={styles['groupInfo']}>
            <h1>
              <strong>
                {data?.role} {data?.title}
              </strong>
            </h1>
            <div className={styles['info_General']}>
              <label>Chuyên khoa</label>
              <span>
                {capitalizeWords(
                  data?.tags.map((item: any) => item.name).join(' - ')
                )}
              </span>
            </div>
            {data?.treatments && (
              <div className={styles['info_General']}>
                <label>Chuyên trị</label>
                <div>
                  <Paragraph
                    ellipsis={
                      ellipsis
                        ? {
                            rows: 2,
                            expandable: true,
                            symbol: <></>
                          }
                        : false
                    }
                  >
                    {data?.treatments}
                  </Paragraph>
                  {isMobile && (
                    <span
                      className={styles['read']}
                      onClick={() => setEllipsis((prev) => !prev)}
                    >
                      {ellipsis ? 'Xem thêm' : 'Thu gọn'}
                    </span>
                  )}
                </div>
              </div>
            )}
            <div className={cx(styles['info_General'])}>
              <label>Giá khám</label>
              <span>{data.price}</span>
            </div>
            <div className={cx(styles['info_General'])}>
              <label>Lịch khám</label>
              <span>{data.days}</span>
            </div>
          </div>
        </Col>
      </Row>
      <div className={styles['frame']}>
        <div className={styles['bottomLeft']}>
          <HiOutlineLocationMarker className={styles['linear-location']} />
          <div className={styles['groupAddress']}>
            <p className={styles['hopital']}>{data.desc2 || 'Đang cập nhật'}</p>
            <p className={styles['address']}>
              {data.hospitalAddress || 'Đang cập nhật'}
            </p>
          </div>
        </div>
        <div className={styles['bottomRight']}>
          <MPButton
            onClick={(e) => {
              e.stopPropagation()
              if (data.doctorDescription?.disabled) {
                setWarningBooking(true)
                return
              }
              if (data?.description?.notiBookingTelemed) {
                setOpenBookingTelemed(true)
                return
              } else {
                handleBooking(data)
              }
            }}
            className={styles['btnBooking']}
          >
            Đặt khám ngay
          </MPButton>
        </div>
      </div>
      {openBookingTelemed && (
        <Modal
          title={'Thông báo'}
          open={openBookingTelemed}
          okText='Đồng ý'
          onOk={()=>handleBooking(data)}
          centered
          onCancel={() => setOpenBookingTelemed(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.description?.notiBookingTelemed
            }}
          />
        </Modal>
      )}
      {warningBooking && (
        <Modal
          title={'Thông báo'}
          open={warningBooking}
          footer={null}
          centered
          onCancel={() => setWarningBooking(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.doctorDescription.message
            }}
          />
        </Modal>
      )}
    </div>
  )
}
