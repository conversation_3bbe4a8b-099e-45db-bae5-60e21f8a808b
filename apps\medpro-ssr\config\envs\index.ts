import includeEnvs from './includeEnvs'
import {
  API_URL,
  BO_API_URL,
  CMS_API_URL,
  MEDPRO_ID_URL,
  nameEnv,
  APP_ID
} from './environment'

const envObj = includeEnvs[nameEnv]
export const currentEnv = {
  ...envObj,
  API_BE: API_URL || envObj.API_BE,
  LOGIN: MEDPRO_ID_URL || envObj.LOGIN,
  API_CMS: CMS_API_URL || envObj.API_CMS,
  BO_API: BO_API_URL || envObj.BO_API,
  APP_ID: APP_ID
}

export const GOOGLE_RECAPCHA_SITEKEY = envObj.GOOGLE_RECAPCHA_SITEKEY
