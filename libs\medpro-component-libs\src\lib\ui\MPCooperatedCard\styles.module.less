.text(@font-size: 0, @font-weight: 0, @line-height: 0, @text-align: left, @color: #003553) {
  font-family: Roboto !important;
  font-size: @font-size;
  font-weight: @font-weight;
  line-height: @line-height;
  text-align: @text-align;
  color: @color;
}

.btnPrev,
.btnNext {
  @media (max-width: 576px) {
    width: 24px !important;
    height: 24px !important;
    svg {
      width: 16px !important;
      height: 16px !important;
    }
  }
  position: absolute;
  top: 50% !important;
  transform: translateY(-50%);
  width: 35px !important;
  height: 35px !important;
  border-radius: 50%;
  background: #fff !important;
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.04),
    0px 2px 6px 0px rgba(0, 0, 0, 0.04), 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(5px);
  display: flex !important;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;

  &:hover {
    background: #ffffff;
  }
}

.btnNext {
  @media (max-width: 1024px) {
    right: -27px !important;
  }
  @media (max-width: 768px) {
    right: -18px !important;
  }
  @media (max-width: 576px) {
    right: -12px !important;
  }

  right: -37.5px !important;
}

.btnPrev {
  @media (max-width: 1024px) {
    left: -27px !important;
  }
  @media (max-width: 768px) {
    left: -18px !important;
  }
  @media (max-width: 576px) {
    left: -12px !important;
    // top: 74px;
  }

  left: -37.5px !important;
}

.Cooperated {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 60px 0;
  @media (max-width: 576px) {
    margin: 40px 0;
  }

  .Cooperated__headline {
    .headline__title {
      .text(28px, 700, 32.81px, center);
      display: flex;
      margin-bottom: 35px;
      text-transform: uppercase;
      font-family: Roboto;

      br {
        display: none;
      }

      @media (max-width: 576px) {
        flex-direction: column;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 500;
        line-height: 23px;
        br {
          display: block;
        }
      }
      @media (max-width: 390px) {
        font-size: 16px;
      }
    }
  }

  .Cooperated__content {
    // display: flex;
    // justify-content: center;
    max-width: 1172px;
    @media (max-width: 576px) {
      max-width: 100%;
    }

    .content__carousel {
      margin-top: 14px;
      // position: relative;
      :global {
        .slick-track {
          display: flex;
          align-items: baseline;

          .slick-slide {
            display: flex;
            justify-content: center;
            //width: 180px !important;
            cursor: pointer;

            @media (max-width: 576px) {
              max-width: 170px !important;
            }
          }
        }

        .slick-list {
          @media (max-width: 576px) {
            height: fit-content;
          }
        }

        .slick-arrow {
          z-index: 99;
          width: 32px;
          height: 32px;
          background-color: #ffffff;
          border-radius: 50%;
          top: 40%;
          box-shadow: 0px 10px 20px 0px #0000001a !important;

          &:before {
            display: none;
          }

          @media (max-width: 576px) {
            width: 24px;
            height: 24px;
            top: 35%;
            svg {
              width: 18px;
              height: 18px;
            }
          }
        }

        .slick-next {
          right: -45px;
          @media (max-width: 576px) {
            right: -10px !important;
          }
        }

        .slick-prev {
          left: -45px;
          @media (max-width: 576px) {
            left: -10px !important;
          }
        }
      }
    }

    .content__carousel__item--div {
      display: flex !important;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      width: 185px;
      height: 100%;
      cursor: pointer;
      @media (max-width: 576px) {
        min-width: 120px !important;
        max-width: 120px !important;
        gap: 8px;
      }
      @media (max-width: 390px) {
        min-width: 100px !important;
        max-width: 100px !important;
      }
    }

    .content__carousel__item--image {
      // border-radius: 50%;
      overflow: hidden;
      @media (max-width: 576px) {
        width: 50px;
        height: 50px;
      }

      span {
        min-height: 64px;
        min-width: 64px;
        max-height: 64px;
        max-width: 64px;
        @media (max-width: 576px) {
          min-height: 50px;
          min-width: 50px;
          max-height: 50px;
          max-width: 50px;
        }
      }
    }

    .content__carousel__item--viewall-img {
      width: 30px;
      height: 30px;
      margin-bottom: 8px;
    }

    .content__carousel__item--viewall {
      color: #003553;
      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
    }

    .content__carousel__item--name {
      // max-width: 150px;
      white-space: wrap;
      .text(16px, 400, 19px, center);
      margin: 0;

      svg {
        vertical-align: top;
        margin-top: 2px;
        @media (max-width: 576px) {
          margin-top: 0;
          width: 13px;
          height: 13px;
        }
      }

      @media (max-width: 576px) {
        font-size: 14px;
        line-height: 16.41px;
      }
      @media (max-width: 390px) {
        font-size: 13px;
      }
    }

    .content__carousel__item--name--paid {
      margin-left: 4px;
      @media (max-width: 576px) {
        margin-left: 2px;
      }
    }

    .content__scroll {
      display: flex;
      align-items: center;
      overflow: auto;
      gap: 15px;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .content__carousel__item--div {
        min-width: 150px !important;
        max-width: 150px !important;
      }
    }
  }
}

.Hospitalseleketon {
  display: flex;
  gap: 40px;
  @media (max-width: 576px) {
    gap: 12px;
  }

  .item_Hos {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 140px;
    @media (max-width: 576px) {
      width: 120px !important;
    }
    @media (max-width: 375px) {
      width: 92px !important;
    }
  }
}
