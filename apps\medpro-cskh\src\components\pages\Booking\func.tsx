import { BookingStepType } from '@medpro-libs/medpro-helper'
import {
  MP<PERSON><PERSON>ose<PERSON>oc<PERSON>,
  MPChooseRoom,
  MPChooseService,
  MPChooseSubject,
  MPIcon
} from '@medpro-libs/libs'
import { RedirectAfterPaymentParams, RenderHeaderProps } from './type'
import { Space } from 'antd'
import { DateAndTimeComponent } from './component/DateAndTime'
import moment from 'moment/moment'
import client from '../../../../config/medproSdk'
import { showErrorNotification } from '../../../../utils/utils.error'
import { PageRoutes } from '../../../../utils/PageRoutes'
import { setTransactionId } from '../../../../store/booking/slice'
import { LIST_APP_ID_MINI_APP } from '../../../../utils/utils.contants'
import { getRoutePartnerIdQueryParams } from '@medpro-libs/libs'

export const getStepItem = (step: BookingStepType): any | null => {
  switch (step) {
    case 'subject':
      return {
        breadcrumb: 'Chọn chuyên khoa',
        title: 'Vui lòng chọn chuyên khoa',
        key: 'subject',
        icon: ({ isActive }: any) => (
          <MPIcon
            name='ChuyenKhoa'
            size={24}
            fill={isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) => {
          return props.type === 'subject' ? (
            <MPChooseSubject handleSubject={props.processNextStep} {...props} />
          ) : null
        },
        after: {
          icon: <MPIcon size={24} name='Search' />,
          place: 'Tìm nhanh chuyên khoa',
          input: true
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'service':
      return {
        breadcrumb: 'Chọn dịch vụ',
        title: 'Vui lòng chọn dịch vụ',
        key: 'service',
        icon: (props: any) => (
          <MPIcon
            size={24}
            name='DichVu'
            fill={props.isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) =>
          props.type === 'service' ? (
            <MPChooseService
              handleService={(data: any) => {
                console.log('handleServic edata: ', data)
                const { service, ...rest } = data
                props.processNextStep({ ...service, ...rest })
              }}
              serviceProps={{}}
              {...props}
            />
          ) : null,
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'doctor':
      return {
        breadcrumb: 'Chọn Bác sĩ',
        title: 'Vui lòng chọn Bác sĩ',
        key: 'doctor',
        icon: (props: any) => (
          <MPIcon
            size={24}
            name='BacSi'
            fill={props.isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) =>
          props.type === 'doctor' ? (
            <MPChooseDoctor handleDoctor={props.processNextStep} {...props} />
          ) : null,
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'room':
      return {
        breadcrumb: 'Chọn phòng khám',
        title: 'Vui lòng chọn phòng khám',
        key: 'room',
        icon: (props: any) => (
          <MPIcon
            size={24}
            name='ChuyenKhoa'
            fill={props.isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) =>
          props.type === 'room' ? (
            <MPChooseRoom handleRoom={props.processNextStep} {...props} />
          ) : null,
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'time':
    case 'date':
      return {
        breadcrumb: 'Chọn ngày khám',
        title: 'Vui lòng chọn ngày khám',
        key: 'date',
        icon: ({ isActive }: any) => (
          <MPIcon
            name={isActive ? 'NgayKham_active' : 'NgayKham_default'}
            size={24}
          />
        ),
        content: (props: any) => {
          return <DateAndTimeComponent {...props} />
        },
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    default:
      return null
  }
}

export const renderHeader = (props: RenderHeaderProps): any => {
  const { type, selectedItem } = props
  return (
    <div>
      <Space>
        <h3>{type}</h3>
        <h3>
          {selectedItem?.name ||
            selectedItem?.date ||
            selectedItem?.startTime ||
            'Chưa chọn'}
        </h3>
      </Space>
    </div>
  )
}

// export const reserveBooking = async (state: {
//   partnerId: any
//   isMulti: any
//   selectedPaymentMethod: any
//   selectedPaymentType: any
//   multiSchedules: any
//   patient: any
//   handleRedirectPayment: any
//   treeId: any
// }) => {
//   const {
//     partnerId,
//     isMulti,
//     selectedPaymentMethod,
//     selectedPaymentType,
//     multiSchedules,
//     patient,
//     handleRedirectPayment,
//     treeId
//   } = state
//
//   const redirectUrl = `${window.location.origin}/${PageRoutes.booking.detail.path}`
//
//   if (isMulti) {
//     const { methodId } = selectedPaymentMethod
//     const { code, subTotal, grandTotal, totalFee, gatewayId } =
//       selectedPaymentType
//     const params = {
//       platform: 'pc',
//       methodId,
//       paymentTypeDetail: code,
//       patientId: patient?.id,
//       redirectUrl,
//       hasInsuranceCode: false,
//       // insuranceCode? : string;
//       // insuranceTransferCode? : string;
//       // patientProfileId? : string;
//       // referralCode? : string;
//       // insuranceChoice? : string;
//       // cbWebView? : number;
//       groupId: 1,
//       bookings: multiSchedules.map((b) => {
//         const {
//           serviceId,
//           subjectId,
//           roomId,
//           doctorId,
//           timeslot: { startTime, endTime, maxSlot, timeId, availableSlot },
//           date,
//           addonServices
//         } = b
//
//         const dateString = moment(date).format('YYYY-MM-DD')
//         const startTimeString = moment(
//           `${dateString} ${startTime}`
//         ).toISOString()
//         return {
//           serviceId,
//           subjectId,
//           roomId,
//           doctorId,
//           startTimeString,
//           startTime: startTimeString,
//           endTime: moment(`${dateString} ${endTime}`).toISOString(),
//           bookingSlotId: `${timeId}_${partnerId}`,
//           availableSlot,
//           maxSlot,
//           idReExam: '',
//           optionBHYT: 0,
//           addonServices
//         }
//       })
//     }
//     try {
//       const { data } = await client.booking.reserveMultipleBookings(params)
//       handleRedirectPayment(data)
//     } catch (err) {
//       showErrorNotification(err)
//     }
//   } else {
//     const { methodId } = selectedPaymentMethod
//     const { code, subTotal, grandTotal, totalFee, gatewayId } =
//       selectedPaymentType
//
//     const {
//       serviceId,
//       subjectId,
//       roomId,
//       doctorId,
//       timeslot: { timeId, maxSlot, startTime, endTime },
//       date,
//       addonServices = []
//     } = multiSchedules[0]
//
//     const dateString = moment(date).format('YYYY-MM-DD')
//     const params: ReserveBooking = {
//       treeId,
//       amount: grandTotal,
//       subTotal,
//       totalFee,
//       // gatewayId,
//       methodId,
//       groupId: 1,
//       patientId: patient.id,
//       paymentTypeDetail: code,
//       redirectUrl: `${window.location.origin}/${PageRoutes.booking.detail.path}`,
//       serviceId,
//       subjectId,
//       roomId,
//       doctorId,
//       startTimeString: moment(`${dateString} ${startTime}`).toISOString(),
//       startTime: moment(`${dateString} ${startTime}`).toISOString(),
//       endTime: moment(`${dateString} ${endTime}`).toISOString(),
//       bookingSlotId: `${timeId}_${partnerId}`,
//       platform: 'pc',
//       maxSlot,
//       insuranceCode: '',
//       hasInsuranceCode: false,
//       idReExam: '',
//       filterCheckData: [],
//       addonServices,
//       xcInfo: {
//         countryId: '',
//         interviewDate: '',
//         patientId: '',
//         profileCode: ''
//       }
//     }
//     try {
//       const { data } = await client.booking.reserveBooking(params)
//       handleRedirectPayment(data)
//     } catch (err) {
//       showErrorNotification(err)
//     }
//   }
// }

//TODO Hàm mới nhớ docs lại
//TODO gọi ở page xac-nhan-thong-tin để check xem redirect đi đâu khi bấm xác nhận

export const repayment = async ({
  selectedPaymentMethod,
  selectedPaymentType,
  bookingId,
  handleRedirectPayment
}: any) => {
  const { methodId } = selectedPaymentMethod
  const { code } = selectedPaymentType
  try {
    const { data } = await client.booking.rePayment({
      methodId,
      paymentTypeDetail: code,
      redirectUrl: `${window.location.origin}/${PageRoutes.booking.detail.path}`,
      groupId: 1,
      id: bookingId
    })

    handleRedirectPayment(data)
  } catch (err) {
    showErrorNotification(err)
  }
}

export const reserveSharePayment = async ({
  selectedPaymentMethod,
  selectedPaymentType,
  bookingId,
  handleRedirectPayment,
  partnerId
}: any) => {
  const { methodId } = selectedPaymentMethod
  const { code } = selectedPaymentType
  try {
    const { data } = await client.booking.rePaymentShareToPay(
      {
        methodId,
        paymentTypeDetail: code,
        redirectUrl: `${window.location.origin}/${PageRoutes.booking.detail.path}`,
        id: bookingId
      },
      { partnerid: partnerId }
    )

    console.log({
      methodId,
      paymentTypeDetail: code,
      redirectUrl: `${window.location.origin}/${PageRoutes.booking.detail.path}`,
      id: bookingId
    })

    handleRedirectPayment(data)
  } catch (err) {
    showErrorNotification(err)
  }
}

export const handleRedirectPayment = ({
  data,
  router,
  appId,
  dispatch
}: RedirectAfterPaymentParams) => {
  const { qrCodeUrl, transactionId, deeplink, deeplinkMiniApp, isGateway } =
    data
  if (isGateway === 0) {
    // No gateway
    router.push(
      `/${PageRoutes.booking.detail.path}?mpTransaction=${transactionId}`
    )
  }

  dispatch(setTransactionId(transactionId))
  if (LIST_APP_ID_MINI_APP.includes(appId)) {
    window.location.href = deeplinkMiniApp
  } else if (qrCodeUrl) {
    window.location.href = qrCodeUrl
  }
}

export const handleRedirectPaymentMethod = ({ router, partnerId }) => {
  router.push(
    getRoutePartnerIdQueryParams(PageRoutes.paymentMethod.path, partnerId)
  )
}

export const updateBookingEditByCS = async (
  multiSchedules: any[],
  bookingId: string,
  patient: any,
  router: any
) => {
  const {
    serviceId = '',
    subjectId = '',
    roomId = '',
    doctorId = '',
    date,
    timeslot: { startTime }
  } = multiSchedules[0]
  const dateString = moment(date).format('YYYY-MM-DD')

  const params = {
    bookingId,
    serviceId,
    subjectId,
    roomId,
    patientId: patient?.id || '',
    doctorId,
    date: moment(`${dateString} ${startTime}`).toISOString()
  }
  try {
    const { data } = await client.booking.updateBooking(params)
    if (data?.transactionId) {
      router.push(
        `/${PageRoutes.booking.detail.path}?mpTransaction=${data?.transactionId}`
      )
    }
  } catch (err) {
    showErrorNotification(err)
    return
  }
}
