import type { AxiosResponse } from 'axios'

import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import type { ClientOptions, HeadersParams } from '../../interfaces'
import {
  ADD_PATIENT_INTO_USER_FIND_PATIENT,
  CREATE_PATIENT_PROFILE,
  FIND_PATIENT_BY_EXTRA,
  FIND_PATIENT_BY_MSBN,
  FIND_PATIENT_HIS,
  GET_INSURANCE_DATE,
  GET_INSURANCE_INFO,
  GET_INSURANCE_PARSE_ADDRESS,
  GET_PATIENT_DETAIL_FOR_UPDATE,
  GET_PATIENT_PROFILES,
  GET_PATIENTS_BY_USER_ID_V2,
  GET_PHONE_LOCALE,
  GET_USER_CONFIG_BTN,
  INSERT_PATIENT,
  INSERT_PATIENT_BASIC_INFO,
  UNLINK_PATIENT,
  UPDATE_PATIENT_WITH_PATIENT_CODE,
  UPDATE_PATIENT_WITHOUT_PATIENT_CODE,
  UPDATE_USER_INFO,
  VALIDATE_BOOKING_RULE_PATIENT,
  VERIFY_INSURANCE_CODE_FIND_PATIENT,
  VERIFY_PHONE_FIND_PATIENT
} from './constants'
import type {
  AddPatientIntoUserQuery,
  CreatePatientProfileData,
  FindByExtraInfo,
  FindByMSBN,
  GetPatientByUserId,
  GetPatientProfilesData,
  InsuranceDate,
  InsuranceInfo,
  InsuranceParseAddress,
  PatientInsertBasicInfo,
  PatientInsertQuery,
  PatientToUpdateQuery,
  PatientUpdateQuery,
  UnlinkPatientQuery,
  UpdatePatientWithPatientCodeQuery,
  ValidateBookingRulePatientParam,
  VerifyInsuranceCodeInfo,
  VerifyPhoneInfo
} from './interfaces'
import { UpdateUserInfoParams } from './interfaces/booking-update-user.inteface'

export interface IPatient {
  insertPatient(
    data: PatientInsertQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  insertBasicInfo(
    data: PatientInsertBasicInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getPatientsByUserIdV2(headers?: HeadersParams): Promise<AxiosResponse>

  getValidPatientsByUserIdV2(
    data?: GetPatientByUserId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  createPatientProfile(
    data: CreatePatientProfileData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getPatientProfiles(
    data: GetPatientProfilesData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  findByMsbn(data: FindByMSBN, headers?: HeadersParams): Promise<AxiosResponse>

  findByExtraInfo(
    data: FindByExtraInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  verifyPhone(
    data: VerifyPhoneInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  verifyInsuranceCode(
    data: VerifyInsuranceCodeInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  addPatientIntoUser(
    data: AddPatientIntoUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getPatientInfoToUpdate(
    query: PatientToUpdateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  unlinkPatient(
    query: UnlinkPatientQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updatePatient(
    data: PatientUpdateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updatePatientWithPatientCode(
    data: UpdatePatientWithPatientCodeQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updatePatientWithoutPatientCode(
    data: PatientUpdateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getInsuranceInfo(
    data: InsuranceInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getInsuranceDate(
    data: InsuranceDate,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  parseAddress(
    data: InsuranceParseAddress,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  findPatientHis(
    data: { secretPatientId: string },
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  validateBookingRulePatient(
    data: ValidateBookingRulePatientParam,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getPhoneLocale(headers?: HeadersParams): Promise<AxiosResponse>

  updateUserInfo(
    data: UpdateUserInfoParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getUserConfigBtn(
    data?: GetPatientByUserId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class Patient extends Base implements IPatient {
  constructor(options?: ClientOptions) {
    super(options)
  }

  insertPatient(
    data: PatientInsertQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(INSERT_PATIENT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  insertBasicInfo(
    data: PatientInsertBasicInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(INSERT_PATIENT_BASIC_INFO),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getPatientsByUserIdV2(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_PATIENTS_BY_USER_ID_V2), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getValidPatientsByUserIdV2(
    data?: GetPatientByUserId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_PATIENTS_BY_USER_ID_V2),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  createPatientProfile(
    data: CreatePatientProfileData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CREATE_PATIENT_PROFILE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getPatientProfiles(
    data: GetPatientProfilesData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_PATIENT_PROFILES),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  findByMsbn(
    data: FindByMSBN,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(FIND_PATIENT_BY_MSBN),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  findByExtraInfo(
    data: FindByExtraInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(FIND_PATIENT_BY_EXTRA),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  findPatientHis(
    data: { secretPatientId: string },
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(FIND_PATIENT_HIS),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  verifyPhone(
    data: VerifyPhoneInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(VERIFY_PHONE_FIND_PATIENT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  verifyInsuranceCode(
    data: VerifyInsuranceCodeInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(VERIFY_INSURANCE_CODE_FIND_PATIENT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  addPatientIntoUser(
    data: AddPatientIntoUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(ADD_PATIENT_INTO_USER_FIND_PATIENT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getPatientInfoToUpdate(
    query: PatientToUpdateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_PATIENT_DETAIL_FOR_UPDATE, query),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  unlinkPatient(
    query: UnlinkPatientQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(UNLINK_PATIENT, query), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  updatePatientWithPatientCode(
    data: UpdatePatientWithPatientCodeQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_PATIENT_WITH_PATIENT_CODE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  updatePatientWithoutPatientCode(
    data: PatientUpdateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_PATIENT_WITHOUT_PATIENT_CODE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  updatePatient(
    data: PatientUpdateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_PATIENT_WITHOUT_PATIENT_CODE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getInsuranceInfo(
    data: InsuranceInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_INSURANCE_INFO),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getInsuranceDate(
    data: InsuranceDate,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_INSURANCE_DATE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  parseAddress(
    data: InsuranceParseAddress,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_INSURANCE_PARSE_ADDRESS),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  validateBookingRulePatient(
    data: ValidateBookingRulePatientParam,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(VALIDATE_BOOKING_RULE_PATIENT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getPhoneLocale(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_PHONE_LOCALE), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  updateUserInfo(
    data: UpdateUserInfoParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_USER_INFO),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  public getUserConfigBtn(
    data?: GetPatientByUserId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_USER_CONFIG_BTN),
      'POST',
      { ...this.options, ...headers },
      data
    )
  }
}
