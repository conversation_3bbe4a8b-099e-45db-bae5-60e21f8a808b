import App, { AppContext } from 'next/app'
import React, { useEffect, useMemo } from 'react'
import { QueryClient, QueryClientProvider } from 'react-query'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { newDefaultLayout, noLayout } from '../layout'
import HeadCustomApp from '../src/components/cores/HeadCustomApp'
import { AppPropsWithLayout } from '../src/type'
import { persistor, store } from '../store'
import '../styles/ant-override.less'
import '../styles/styles.less'
import { getAppInfoSEO, getSeoPageServer } from '../utils/method'
import AppReduxWrapper from './AppReduxWrapper'
import { fetchPartnerInfoServer } from '../utils/utils.query-server'
import useMyApp from '../hooks/useMyApp'
import { getStaticResourceUrlAppId } from '@medpro-libs/libs'
import { GOOGLE_RECAPCHA_SITEKEY, currentEnv } from '../config/envs'
import { AppErrorBoundary } from '../src/components/ErrorBoundary'
import { last } from 'lodash'
import Script from 'next/script'
import { v4 as uuidv4 } from 'uuid'

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }
function CustomApp({ Component, router, ...rest }: AppPropsWithLayout) {
  const { pageProps, appInfo, partnerInfoApp, seoPage } = rest
  const behaviorAnKhang = router.query?.behavior === 'AnKhang'
  const getLayout =
    router.query.isWebView === 'true' || pageProps.noLayout
      ? noLayout
      : Component.getLayout ?? newDefaultLayout

  const isSEODetail = Component.isSEODetail
  const showBreadcrumb = Component.showBreadcrumb

  const partnerId = router.query.partnerId || pageProps.partnerId
  const backgroundColor =
    Component?.backgroundColor || pageProps?.backgroundColor
  const CSYT_DYNAMIC = Component?.CSYT_DYNAMIC || pageProps?.CSYT_DYNAMIC
  const { appInitialized } = useMyApp({
    appInfo,
    partnerId
  })

  // Nếu có breadcrumb ở server side props thì gắn nó vào title
  const breadcrumbTitle = last<any>(pageProps.breadcrumb)?.title
  const keywords = pageProps?.seo_keywords
  const pageTitle =
    pageProps.detailSEO?.title ||
    seoPage?.title ||
    breadcrumbTitle ||
    pageProps.seoPage?.title ||
    pageProps?.pageTitle

  const site = useMemo(() => {
    // console.log(`site change => ${Component.displayName}`)
    return getLayout(
      <Component
        {...pageProps}
        appInfo={appInfo}
        partnerId={partnerId}
        partnerInfoApp={partnerInfoApp}
        appInitialized={appInitialized}
      />,
      {
        pageTitle,
        seoPage: pageProps.seoPage,
        partnerInfoApp,
        footerMobileMenu: behaviorAnKhang ? false : true,
        isSEODetail,
        showBreadcrumb,
        breadcrumb: pageProps.breadcrumb ?? Component.breadcrumb,
        breadcrumbBgColor: Component.breadcrumbBgColor,
        behavior: pageProps.behavior ?? router.query.behavior,
        backgroundColor,
        CSYT_DYNAMIC
      }
    )
  }, [
    getLayout,
    Component,
    pageProps,
    appInfo,
    partnerId,
    partnerInfoApp,
    appInitialized,
    pageProps.noLayout
  ])

  const [queryClient] = React.useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            cacheTime: 5 * 60 * 1000,
            staleTime: 5 * 60 * 1000,
            refetchOnWindowFocus: false,
            refetchInterval: false,
            retry: false
          }
        }
      })
  )

  const [UUID, setUUID] = React.useState('')
  const isTrackingRef = React.useRef(false)
  const currentUrlRef = React.useRef('')

  useEffect(() => {
    // Tránh đăng ký multiple listeners
    if (isTrackingRef.current) return

    // Tạo UUID chỉ một lần khi component mount
    const newUUID = uuidv4()
    isTrackingRef.current = true
    currentUrlRef.current = router.asPath // Set initial URL

    const routeChangeHandler = (url) => {
      // Kiểm tra nếu URL trùng thì không chạy tracking
      if (currentUrlRef.current === url) {
        console.log('Same URL detected, skipping tracking')
        return
      }

      // Update current URL
      currentUrlRef.current = url

      // Check nếu user back về homepage từ trang khác
      const isHomepage = url === '/' || url.split('?')[0] === '/'

      if (isHomepage) {
        // Push OFF_FLOW event trước khi cleanup
        console.log('OFF_FLOW', {
          event: 'pageview',
          Page: url,
          patient: newUUID,
          status: 'OFF_FLOW'
        })
        window.dataLayer.push({
          event: 'pageview',
          Page: url,
          patient: newUUID,
          status: 'OFF_FLOW'
        })

        setUUID('')
        isTrackingRef.current = false
        router.events.off('routeChangeComplete', routeChangeHandler)
        return
      }
      console.log('ON_FLOW', {
        event: 'pageview',
        Page: url,
        patient: newUUID,
        status: 'ON_FLOW'
      })
      window.dataLayer.push({
        event: 'pageview',
        Page: url,
        patient: newUUID,
        status: 'ON_FLOW'
      })
    }

    router.events.on('routeChangeComplete', routeChangeHandler)

    return () => {
      setUUID('')
      isTrackingRef.current = false
      router.events.off('routeChangeComplete', routeChangeHandler)
    }
  }, [])

  return (
    <>
      <Provider store={store}>
        <HeadCustomApp
          appInfo={appInfo}
          seoPage={pageProps.seoPage}
          seoPageBO={seoPage}
          removeChat={
            pageProps.removeChat ?? Component.removeChat ?? behaviorAnKhang
          }
          pageTitle={pageTitle}
          keywords={keywords}
          csChatConfig={pageProps.csChatConfig || appInfo.config}
          detail_seo_description={pageProps?.seo_description}
          robots={Component.robots || pageProps.robots}
          {...pageProps}
        />
        <PersistGate loading={null} persistor={persistor}>
          {() => (
            <AppReduxWrapper
              appInfo={appInfo}
              partnerInfoApp={partnerInfoApp}
              appInitialized={Component.ssr || appInitialized}
              pageProps={pageProps}
              partnerId={partnerId}
              platform={rest.platform}
            >
              <QueryClientProvider client={queryClient}>
                <AppErrorBoundary>{site}</AppErrorBoundary>
              </QueryClientProvider>
            </AppReduxWrapper>
          )}
        </PersistGate>
      </Provider>
      <Script
        src={`https://www.google.com/recaptcha/api.js?render=${GOOGLE_RECAPCHA_SITEKEY}`}
      />
    </>
  )
}

CustomApp.getInitialProps = async (context: AppContext) => {
  const isServer = typeof window === 'undefined'

  const ctx = await App.getInitialProps(context)
  const userAgent = context.ctx.req?.headers['user-agent']
  const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent)
  let partnerInfoApp,
    appInfo = { appId: 'medpro' },
    seoPage

  if (isServer) {
    const [_partnerInfoApp, _appInfo, _seoPage] = await Promise.all([
      fetchPartnerInfoServer({ partnerid: appInfo.appId }),
      getAppInfoSEO(context),
      getSeoPageServer({ path: context.ctx.asPath })
    ])

    partnerInfoApp = {
      ..._partnerInfoApp,
      ...getStaticResourceUrlAppId(currentEnv.BO_API, appInfo.appId),
      appId:'momo'
    }
    appInfo = _appInfo
    seoPage = _seoPage
  }

  return {
    ...ctx,
    appInfo,
    partnerInfoApp,
    seoPage,
    platform: isMobile ? 'web' : 'pc'
  }
}

export default CustomApp
