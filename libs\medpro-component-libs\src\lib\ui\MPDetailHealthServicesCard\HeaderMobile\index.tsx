import Image from 'next/image'
import Link from 'next/link'
import styles from './styles.module.less'
import cx from 'classnames'

interface propsHeaderMobile {
  title: any
  className?: any
}
export const HeaderMobile = ({ title, className }: propsHeaderMobile) => {
  const logoMobile = require('./img/logoMobile.svg')
  return (
    <div className={cx(styles['headerMobile'], className)}>
      <label className={styles['title']}>{title}</label>
      <Link href='/' passHref>
        <Image src={logoMobile} alt='logo' />
      </Link>
    </div>
  )
}
