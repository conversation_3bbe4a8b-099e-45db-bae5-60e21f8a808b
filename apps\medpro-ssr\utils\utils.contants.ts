import { currentEnv } from '../config/envs'

export const url = {
  Medpro: 'https://umc.medpro.vn'
}

export const listDate = [
  {
    key: 2,
    value: 'Thứ 2'
  },
  {
    key: 3,
    value: 'Thứ 3'
  },
  {
    key: 4,
    value: 'Thứ 4'
  },
  {
    key: 5,
    value: 'Thứ 5'
  },
  {
    key: 6,
    value: 'Thứ 6'
  },
  {
    key: 7,
    value: 'Thứ 7'
  }
]

export const news = {
  LIMIT_PAGE: 8
}
export const urlJson = {
  urldeployHospital: `https://api.npoint.io/f0fab4421642544f37bd`
}
export const kyTuDacBiet = /[`~!@#$%^&*()_|+\-=?;:'",.<>\\{\\}\\[\]\\\\/]/g

export const NO_PAYMENT = 'NO_PAYMENT'

export const keyMemory = {
  userInfo: 'userInfo',
  analytics: 'analytics',
  partnerId: 'partnerId',
  hostname: 'hostname',
  loginAt: 'loginAt',
  test: 'test',
  token: 'token'
}

export enum NewHospitalType {
  PUBLIC_HOSPITAL = 'benh-vien-cong',
  PRIVATE_HOSPITAL = 'benh-vien-tu',
  PHONG_KHAM = 'phong-kham',
  PHONG_MACH = 'phong-mach',
  XET_NGHIEM = 'xet-nghiem',
  Y_TE_TAI_NHA = 'y-te-tai-nha',
  TIEM_CHUNG = 'tiem-chung'
}

export const FEATURE_TYPE = {
  booking: 'booking.',
  bookingVaccine: 'booking.vaccine',
  bookingVip: 'booking.vip',
  bookingDate: 'booking.date',
  bookingDoctor: 'booking.doctor',
  bookingCls: 'booking.cls',

  insuranceCheck: 'insurance.check',

  paymentFee: 'payment.fee',
  paymentFeeSearch: 'payment.fee.search',
  paymentFeeHistory: 'payment.fee.history',

  instruc: 'instruc',
  declareYT: 'declare.yt',

  eInvoice: 'e.invoice',
  onlineTelemed: 'online.telemed'
}

export enum KeyTab {
  records = 'records',
  bills = 'bills',
  notifications = 'notifications'
}

export enum VISIABLE {
  CANCEL = 'cancel',
  UPDATE_PATIENT_RELATION = 'updatePatientRelation'
}

export enum ACTION_CSKH {
  BOOK = 'book',
  UPDATE_PATIENT = 'update_patient',
  CREATE_PATIENT = 'create_patient',
  HISTORY_BOOKING = 'history_booking'
}

export const LIST_APP_ID_MINI_APP = ['momo', 'zalopay']
export const LIST_APP_ID = ['medpro', 'momo', 'vnpt', 'umcmono', 'zalopay']
export const LIST_APP_ID_HAVE_NEWS = ['medpro', 'bvmathcm']

export const SSG_REVALIDATE_SECOND = 15
export const LIST_TYPE_PARTNER = [
  {
    key: 'benh-vien-cong',
    newHospitalType: 1,
    title: 'Bệnh viện công'
  },
  {
    key: 'benh-vien-tu',
    newHospitalType: 2,
    title: 'Bệnh viện tư'
  },
  {
    key: 'phong-kham',
    newHospitalType: 3,
    title: 'Phòng khám'
  },
  {
    key: 'phong-mach',
    newHospitalType: 4,
    title: 'Phòng mạch'
  },
  {
    key: 'xet-nghiem',
    newHospitalType: 5,
    title: 'Xét nghiệm'
  },
  {
    key: 'y-te-tai-nha',
    newHospitalType: 6,
    title: 'Y tế tại nhà'
  },
  {
    key: 'tiem-chung',
    newHospitalType: 7,
    title: 'Tiêm chủng'
  }
]

export const TYPE_HOSPITAL = [
  'benh-vien-cong',
  'benh-vien-tu',
  'phong-kham',
  'phong-mach',
  'xet-nghiem',
  'y-te-tai-nha',
  'tiem-chung'
]

export enum SEO_PAGE_PATHNAME {
  HOME = 'trang-chu',
  HOSPITAL_LIST = 'danh-sach-benh-vien',
  INTRODUCE = 'gioi-thieu'
}

export const LIST_SUBPATH_PARTNERS = [
  '/umc1',
  '/umc2',
  '/umc3',
  '/benh-vien-mat',
  '/benh-vien-cho-ray',
  '/benh-vien-nhi-dong-1',
  '/benh-vien-nhi-dong-thanh-pho',
  '/benh-vien-da-lieu',
  '/benh-vien-vung-tau',
  '/benh-vien-trung-vuong',
  '/benh-vien-binh-thanh',
  '/benh-vien-phu-san-can-tho',
  '/benh-vien-chan-thuong-chinh-hinh',
  '/benh-vien-nguyen-trai',
  '/benh-vien-minh-anh',
  '/benh-vien-dong-nai',
  '/benh-vien-van-hanh',
  '/benh-vien-khu-vuc-an-giang',
  '/benh-vien-an-phuoc',
  '/benh-vien-trung-uong-can-tho',
  '/benh-vien-an-hao',
  '/benh-vien-hoan-my-thu-duc',
  '/benh-vien-hoan-my-van-phuc-1',
  '/benh-vien-medic-binhduong'
]
export const TITLE_BREADCRUMB = {
  '/': 'Trang chủ',
  'tim-kiem': 'Tìm kiếm',
  'hinh-thuc-dat-kham': 'Hình thức đặt khám',
  'chon-ho-so': 'Chọn hồ sơ',
  'chon-lich-kham': 'Chọn lịch khám',
  'xac-nhan-thong-tin': 'Xác nhận thông tin',
  'chi-tiet-phieu-kham-benh': 'Chi tiết phiếu khám bệnh',
  'phuong-thuc-thanh-toan': 'Phương thức thanh toán',
  'thanh-toan-ho': 'Thanh toán hộ',
  'benh-vien-cong': 'Bệnh viện công',
  'benh-vien-tu': 'Bệnh viện tư',
  'phong-kham': 'Phòng khám',
  'phong-mach': 'Phòng mạch',
  'xet-nghiem': 'Xét nghiệm',
  'y-te-tai-nha': 'Y tế tại nhà',
  'tiem-chung': 'Tiêm chủng',
  'tin-tuc': 'Tin tức',
  'co-so-y-te': 'Cơ sở y tế',
  'dich-vu-y-te': 'Dịch vụ y tế',
  'dat-kham-tai-co-so': 'Đặt khám tại cơ sở',
  'dat-kham-theo-bac-si': 'Đặt khám theo bác sĩ',
  'dat-lich-xet-nghiem': 'Đặt lịch xét nghiệm',
  'huong-dan': 'Hướng dẫn',
  'huong-dan-app': 'Hướng dẫn',
  'cai-dat-ung-dung': 'Cài đặt ứng dụng',
  'dat-lich-kham': 'Đặt lịch khám',
  'tu-van-kham-benh-tu-xa': 'Tư vấn khám bệnh từ xa',
  'quy-trinh-hoan-phi': 'Quy trình hoàn phí',
  'cau-hoi-thuong-gap': 'Câu hỏi thường gặp',
  'goi-kham-suc-khoe': 'Gói khám sức khỏe',
  'bac-si': 'Bác sĩ',
  'gioi-thieu': 'Giới thiệu',
  'quy-dinh-su-dung': 'Quy định sử dụng',
  clinic: 'Medpro - Hệ thống phòng khám',
  user: 'Tài khoản'
}
export const STEP_BOOKING = {
  CHON_DICH_VU: 0,
  CHON_HO_SO: 1,
  PHUONG_THUC_THANH_TOAN: 2
}
