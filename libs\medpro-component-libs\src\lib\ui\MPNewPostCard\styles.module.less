.post {
  @media (max-width: 1024px) {
    padding: 16px;
    border-bottom: none;
  }

  .icon {
    display: flex;
    align-items: center;
  }

  .title {
    margin-bottom: 0;
    color: var(--Primary, #003553);
    font-size: 39px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    @media (max-width: 1024px) {
      color: var(--primary-body-text, #003553);
      font-size: 32px;
      font-style: normal;
      line-height: normal;
    }
  }

  .tag {
    display: flex;
    align-items: center;
    margin-top: 16px;
    font-size: 20px;
    gap: 8px;
    color: #858585;
    @media (max-width: 1024px) {
      color: var(--primary, #003553);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 23.682px; /* 148.012% */
    }

    > span {
      line-height: 20.537px;
      @media (max-width: 1024px) {
        color: var(--gray, #858585);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }

  .description {
    margin-top: 16px;
    margin-bottom: 32px;
    font-size: 16px;
    line-height: 23.682px;
    padding-left: 16px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      height: 100%;
      width: 2px;
      background: #ffb54a;
    }
  }

  img {
    width: 100%;
    object-fit: contain;
  }

  .contentWrapper {
    .content {
      position: relative;
      overflow: hidden;

      &.hiddenContent {
        max-height: 1200px;
        overflow-y: hidden;

        // ::after {
        //   content: '';
        //   position: absolute;
        //   bottom: 0;
        //   left: 0;
        //   width: 100%;
        //   height: 150px;
        //   background: linear-gradient(to bottom,
        //   rgba(255, 255, 255, 0) 0%,
        //   rgba(255, 255, 255, 0.1) 100%,
        //   rgba(255, 255, 255, 0.3) 100%);
          
        // }

        ::before{
          content: "";
          position: absolute;
          bottom: 100%;
          height: 200px;
          left: 0;
          background-image: linear-gradient(180deg,hsla(0,0%,100%,0),#fff);
          width: 100%;
        }
      }
    }
  }

  //Các tính năng nổi bật CTA
  .callToAction {
    display: flex;
    flex-direction: column;
    gap: 24px;
    max-height: 250px;
    margin-top: 10px;
    @media screen and (max-width: 430px) {
      max-height: 100%;
    }

    .titleCTA {
      h2 {
        text-align: center;
        color: #003553;
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;

        span {
          color: #00b5f1;
        }
      }
    }

    .contentCTA {
      display: flex;
      justify-content: space-between;
      gap: 12px;
      @media screen and (max-width: 430px) {
        flex-wrap: wrap;
      }

      .contentClickCTA {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 8px;
        border-radius: 12px;
        width: calc(100% / 4);
        background: #fff;
        border: 1px solid transparent;
        transition: all 0.19s ease-in;

        span {
          max-width: 150px;
          max-height: 100px;
        }

        @media screen and (max-width: 430px) {
          width: 48%;
        }

        &:hover {
          border-color: #00b5f1;
          box-shadow: 0 4px 20px 0 rgba(54, 133, 217, 0.2);
        }

        span {
          text-align: center;
          color: #003553;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          cursor: pointer;
          line-height: normal;
        }
      }
    }
  }

  .viewMore {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 20px;
    flex-wrap: wrap;
    line-height: 23px;
    cursor: pointer;

    .image {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }

  .viewCard {
    margin-top: 35px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    border: 1px solid #e1e3e6;
    background: #f6f7f9;
    padding: 6px 12px;
    font-size: 13px;

    .views {
      display: flex;
      align-items: center;
      gap: 4px;
      padding-right: 8px;
    }

    .btnLike {
      border-radius: 8px;
      border: 1px solid #eaeaea;
      background: #ffffff;
      padding: 7px;

      svg {
        fill: #003553;
      }
    }
  }

  .tags {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    font-size: 13px;

    .titleTag {
      color: #858585;
    }

    span {
      border-radius: 20px;
      background: #eaeaea;
      padding: 6px 12px;
    }
  }
}
