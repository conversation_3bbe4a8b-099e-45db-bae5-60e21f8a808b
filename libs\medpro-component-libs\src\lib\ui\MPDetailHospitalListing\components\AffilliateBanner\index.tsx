import cx from 'classnames'
import React from 'react'
import { Carousel, CarouselProps } from 'antd'
import Image from 'next/image'
import styles from './styles.module.less'
import { size } from 'lodash'

interface IF_BANNER {
  partnerInfo: any
  type?: 'right' | 'body' | undefined
}

const AffiliateBanner = ({ partnerInfo, type }: IF_BANNER) => {
  const dataBanner = partnerInfo?.banner.filter(Boolean) || []
  const settings: CarouselProps = {
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    swipe: true,
    draggable: true,
    fade: false,
    dots: true,
    infinite: true,
    responsive: []
  }

  return (
    <div className={cx(styles['affiliateBannerWrapper'], type && styles[type])}>
      {type && type === 'body' ? (
        <>
          <div className={cx(styles['title'])}>
            <h3>Mô tả</h3>
          </div>
          <div
            className={styles['content']}
            dangerouslySetInnerHTML={{
              __html: partnerInfo?.description
            }}
          />
          {dataBanner.length ? (
            <div className={styles['scrollPadding']}>
              {dataBanner?.map((image: any, i: number) => {
                return (
                  <div
                    className={cx(
                      styles['banner'],
                      size(dataBanner) === 1 && styles['bannerSingle']
                    )}
                    key={i}
                  >
                    <Image
                      src={image}
                      alt=''
                      width={352}
                      height={168}
                      objectFit='cover'
                      objectPosition='50% 15%'
                    />
                  </div>
                )
              })}
            </div>
          ) : null}
        </>
      ) : (
        <Carousel {...settings} className={styles['carousel']}>
          {dataBanner?.map((image: any, i: number) => {
            return (
              <div className={styles['banner']} key={i}>
                <Image
                  src={image}
                  alt=''
                  width={345}
                  height={165}
                  objectFit='cover'
                  objectPosition='50% 15%'
                />
              </div>
            )
          })}
        </Carousel>
      )}
    </div>
  )
}
export default AffiliateBanner
