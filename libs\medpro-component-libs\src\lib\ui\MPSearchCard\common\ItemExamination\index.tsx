import { Card, Popover, Rate, Tooltip } from 'antd'
import cx from 'classnames'
import { SEARCH_LIST } from 'libs/medpro-component-libs/src/lib/common/constant'
import { size } from 'lodash'
import { FiUser } from 'react-icons/fi'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import MPButton from '../../../MPButton'
import styles from './styles.module.less'
import Image from 'next/image'
import { useState } from 'react'
import { useWindowDimensions } from 'libs/medpro-component-libs/src/lib/hooks/useWindowDimesion'
import DefaultDrawer from '../../../DefaultDrawer'
export interface ItemExaminationProps {
  data: any
  handleViewMore: (type: string, item: any) => void
  handleBooking: (item: any, type: string) => void
}
const ItemExamination = (props: ItemExaminationProps) => {
  const { windowWidth } = useWindowDimensions()
  const [drawerCashBack, setDrawerCashBack] = useState(false)
  const [Visible, setVisible] = useState(false)
  const trigger = windowWidth > 576 ? 'hover' : 'click'
  const onShow = (e: any) => {
    e.stopPropagation()
    if (trigger === 'click') {
      setDrawerCashBack(true)
    }
  }
  const onHidden = (e: any) => {
    e.stopPropagation()
    setDrawerCashBack(false)
  }
  return (
    <div className={styles['body']}>
      <Card
        className={styles['ItemExamination']}
        cover={
          <div className={styles['banner']}>
            <Image
              alt={`${props.data.role} ${props.data.title}`}
              className={styles['img']}
              src={props.data.imageUrl}
              width={260}
              height={200}
              layout='fixed'
              objectFit='cover'
              objectPosition='top'
            />
            <p className={styles['showStar']}>
              <Rate disabled={true} style={{ fontSize: 16 }} defaultValue={1} />
              <p>{props.data?.doctorDescription?.rating?.rate || 4.8}</p>
            </p>
            {props.data?.isCashBack && (
              <div className={styles['tagCashBack']} onClick={onShow}>
                <Popover
                  showArrow={true}
                  overlayClassName={styles['popoverCashBack']}
                  overlayInnerStyle={{ width: 510 }}
                  content={
                    props.data?.partner?.popup?.content && (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: props.data?.partner?.popup?.content
                        }}
                      />
                    )
                  }
                  onOpenChange={(visible) => {
                    if (windowWidth > 576 && visible) {
                      setVisible(true)
                    } else {
                      setVisible(false)
                    }
                  }}
                  open={Visible}
                  placement='bottomLeft'
                >
                  Hoàn tiền
                </Popover>
              </div>
            )}
          </div>
        }
      >
        <div className={styles['groupInfo']}>
          <div className={styles['info']}>
            <h3 className={styles['infoDegree']}>
              {props.data.role} &nbsp;
              <strong>{props.data.title}</strong>
            </h3>
          </div>
          {props.data?.doctorDescription?.position && (
            <div className={styles['infoPosition']}>
              <FiUser size={16} /> &nbsp;
              {props.data?.doctorDescription?.position}
            </div>
          )}

          <div className={styles['specialist']}>
            {props.data?.tags?.slice(0, 1) &&
              props.data?.tags?.slice(0, 1).map((item: any, index: number) => {
                return (
                  <Tooltip
                    overlayClassName={styles['specialistTooltip']}
                    placement='bottom'
                    title={item.name}
                  >
                    <div className={styles['specialistItem']} key={index}>
                      {item.name}
                    </div>
                  </Tooltip>
                )
              })}
            {size(props.data?.tags?.slice(1)) > 0 && (
              <Tooltip
                overlayClassName={styles['specialistTooltip']}
                placement='bottom'
                title={[
                  ...props.data?.tags
                    .slice(1)
                    .map((item: any) => item.name)
                    .join(', ')
                ]}
              >
                <div className={styles['specialistItem']}>
                  +{size(props.data.tags.slice(1))} Chuyên khoa
                </div>
              </Tooltip>
            )}
          </div>
          {/* <div className={styles['rateMobile']}>
            <Rate
              disabled={true}
              defaultValue={props.data.doctorDescription.rating.rate}
              style={{ fontSize: 16 }}
            />
            <p className={styles['rate']}>
              {props.data.doctorDescription.rating.rate}
            </p>
          </div> */}
        </div>
      </Card>
      <div className={styles['doctorAddress']}>
        <div className={styles['bottomLeft']}>
          <HiOutlineLocationMarker className={styles['linear-location']} />
          <div className={styles['groupAddress']}>
            <p className={styles['hopital']}>
              {props.data?.desc2 || 'Đang cập nhật'}
            </p>
            <p className={styles['address']}>
              {props.data?.hospitalAddress || 'Đang cập nhật'}
            </p>
          </div>
        </div>
        <div className={styles['groupButton']}>
          {props.data?.description?.isDetailVisible && (
            <MPButton
              className={styles['btnView']}
              onClick={(e) => {
                e.stopPropagation()
                props.handleViewMore(props.data, SEARCH_LIST[2])
              }}
            >
              Xem chi tiết
            </MPButton>
          )}
          <MPButton
            className={cx(
              styles['btnBooking'],
              !props.data?.description?.isDetailVisible && styles['onlyBtn']
            )}
            onClick={(e) => {
              e.stopPropagation()
              props.handleBooking(SEARCH_LIST[2], props.data)
            }}
          >
            Đặt lịch ngay
          </MPButton>
        </div>
      </div>
      {
        <DefaultDrawer
          title={props.data?.partner?.popup?.title || 'Thông tin hoàn tiền'}
          className={styles['modalCashBack']}
          open={drawerCashBack}
          onClose={onHidden}
          children={
            <div
              dangerouslySetInnerHTML={{
                __html: props.data?.partner?.popup?.content
              }}
            />
          }
          height={'calc(70%)'}
          style={{ zIndex: 999999999 }}
        />
      }
    </div>
  )
}

export default ItemExamination
