// /apps/care247/pages/_app.tsx
import { AppProps } from 'next/app'
import Script from 'next/script'
import React from 'react'
import Head from 'next/head'
import './styles.less'
import 'antd/dist/antd.css'
import '../styles/ant-override.less'
import '../styles/styles.less'
import { GOOGLE_RECAPCHA_SITEKEY } from '../config/envs'
import StoreProvider from '../src/components/pages/thanh-toan/StoreProvider'

function CustomApp({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <meta name='viewport' content='viewport-fit=cover' />
        <meta content='width=device-width, initial-scale=1' name='viewport' />
        <title>Welcome to care247!</title>
        <link rel='icon' href='/favicon.ico' />
      </Head>
      <StoreProvider>
        <main className='app'>
          <Component {...pageProps} />
        </main>
      </StoreProvider>
      <Script
        src={`https://www.google.com/recaptcha/api.js?render=${GOOGLE_RECAPCHA_SITEKEY}`}
      />
    </>
  )
}

export default CustomApp
