import { MPButton } from '@medpro-libs/libs'
import { Form, Modal, RadioChangeEvent, Select } from 'antd'
import {
  debounce,
  dropRight,
  every,
  first,
  floor,
  get,
  identity,
  pickBy,
  size
} from 'lodash'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'
import client from '../../../../config/medproSdk'
import { useBookingTreeHelperApp } from '../../../../hooks/booking-tree'

import { BsCalendar2DateFill } from 'react-icons/bs'
import { FaHandHoldingHeart, FaStethoscope, FaUserMd } from 'react-icons/fa'
import { getDataKey } from '../../../../hooks/booking-tree/func'
import {
  selectBookingTreeDynamicData,
  selectBookingTreeState,
  selectFirstSchedule,
  selectMultiSchedules,
  selectTreeId,
  selectUrlParams
} from '../../../../store/booking/selector'
import {
  bookingActions,
  resetPaymentInfo,
  setTreeId
} from '../../../../store/booking/slice'
import { useAppSelector } from '../../../../store/hooks'
import { hospitalActions } from '../../../../store/hospital/hospitalSlice'
import {
  selectExtraConfig,
  selectPartnerId,
  selectPartnerInfo
} from '../../../../store/hospital/selector'
import { selectPatient } from '../../../../store/patient/patientSelector'
import { patientActions } from '../../../../store/patient/patientSlice'
import {
  selectReExamBookingData,
  selectReExamBookingTreeData
} from '../../../../store/reexam/selector'
import CalendarContent from '../CalendarContent'
import DoctorItem from '../DoctorItem'
import Seleketon from '../seleketon/seleketon'
import ServiceItem from '../ServiceItem'
import SubjectItem from '../SubjectItem'
import TimeContent from '../TimeContent'
import {
  ContentSteps,
  handleFilterByKeyword,
  onBackStepQuery,
  PopupStep,
  sortFlowBookingByStep
} from './funct'
import styles from './styles.module.less'
import RoomItem from '../RoomItem'
import { HiCheckBadge } from 'react-icons/hi2'
interface bookingAppProps {
  next: () => void
  current: number
}

const PAGE_SIZE = 10
let timeout
const { Option } = Select
export const ConfirmBookingApp = ({ next, current }: bookingAppProps) => {
  const router = useRouter()
  const featureType = router.query.feature as string
  const treeId = useAppSelector(selectTreeId)
  const step = router.query.step as string
  const partnerId =
    (router.query.partnerId as string) || useAppSelector(selectPartnerId)
  const serviceId = router.query.serviceId as string
  const subjectId = router.query.subjectId as string
  const doctorId = router.query.doctorId as string
  const dateId = router.query.date as string
  const timeId = router.query.timeId as string
  const isBack = router.query.isBack as string
  const addSubject = router.query.addSubject as string

  const [form] = Form.useForm()
  const partnerInfo = useAppSelector(selectPartnerInfo)
  const bookingTreeState = useAppSelector(selectBookingTreeState)
  const reExamBookingTreeData = useAppSelector(selectReExamBookingTreeData)
  const hospital = useAppSelector((state) => state.hospital)
  const booking = useAppSelector((state) => state.booking)
  const bookingTreeParams = useAppSelector(selectBookingTreeDynamicData)
  const firstSchedule = useAppSelector(selectFirstSchedule)
  const PatientInfo = useAppSelector(selectPatient)
  const [isOkPopup, setIsOkPopup] = useState(false)
  const [showPopup, setShowPopup] = useState(false)
  const [currentSteps, setCurrentSteps] = useState<any>([])
  const [open, setOpen] = useState({
    step: undefined,
    status: false
  })
  const [stepName, setStepName] = useState('')
  const extraConfig: any = useAppSelector(selectExtraConfig)
  const [selectedDate, setSelectedDate] = useState('')
  const [count, setCount] = useState(PAGE_SIZE)
  const [loading, setLoading] = useState(false)
  const urlParams = useAppSelector(selectUrlParams)
  const reExamBookingData = useAppSelector(selectReExamBookingData)
  const multiSchedules = useAppSelector(selectMultiSchedules)
  const [sliceMultiSchedule, setSliceMultiSchedule] = useState(multiSchedules)
  const [waitingResetBookingTree, setWaitingResetBookingTree] = useState(
    !reExamBookingTreeData
  )
  const [date, setDate] = useState(moment())
  const [doctors, setDoctors] = useState<any>()
  const [subjects, setSubjects] = useState<any>()
  const [rooms, setRooms] = useState<any>()
  const [services, setServices] = useState<any>()
  const [dates, setDates] = useState<any>()
  const [times, setTimes] = useState<any>()
  const [serviceData, setServiceData] = useState<any>()
  const [openCheckBHYT, setOpenCheckBHYT] = useState<any>({
    id: '',
    open: false
  })
  const [addon, setAddon] = useState([]) as any[]
  const [isClearBookingTreeState] = useState(router.query.clear === 'true')
  const [temp, setTemp] = useState([])
  const [dataResult, setDataResult] = useState([])
  const dispatch = useDispatch()
  const [serviceProps, setServiceProps] = useState({
    checkBHYT: false,
    idService: '',
    error: undefined,
    collapseID: undefined,
    service: undefined,
    checkAddonService: false,
    optionBHYT: ''
  })

  const headers = useMemo(() => {
    return { partnerid: partnerId }
  }, [partnerId])

  const limitBookingByTreeId = useMemo(() => {
    return get(extraConfig, `treeId.${treeId?.toLowerCase()}.bookingLimit`)
  }, [extraConfig, treeId])

  useEffect(() => {
    // reset multi schedule theo config bookingLimit
    if (extraConfig && treeId) {
      if (limitBookingByTreeId === 1 && step === 'chon-thong-tin-kham') {
        dispatch(bookingActions.resetMultiSchedules())
        setSliceMultiSchedule([])
      }
      setWaitingResetBookingTree(false)
    }
  }, [extraConfig, treeId])

  useEffect(() => {
    // reset multi schedule theo config bookingLimit
    if (isBack) {
      delete router.query.findExtra
      router.replace(
        { pathname: router.pathname, query: router.query },
        undefined,
        { shallow: true }
      )
      const slice = dropRight(multiSchedules)

      dispatch(bookingActions.setScheduleMulti(slice))
      setSliceMultiSchedule(slice)
      dispatch(patientActions.resetSelectPatient())
    }
  }, [isBack])

  useEffect(() => {
    if (isClearBookingTreeState) {
      dispatch(bookingActions.resetBookingFlow({}))
    }
  }, [isClearBookingTreeState])

  useEffect(() => {
    dispatch(bookingActions.resetMedproCare())
    dispatch(hospitalActions.getPartnerInfo())
    if (router.query.step !== 'xac-nhan-thong-tin') {
      dispatch(resetPaymentInfo())
    }
    delete router.query.clear
    router.replace(router, null, { shallow: true })

    if (featureType && featureType.startsWith('booking.')) {
      const treeId = featureType.split('.')[1].trim().toUpperCase()
      dispatch(setTreeId(treeId))
    }

    let params: any = {
      serviceId,
      subjectId,
      doctorId,
      date: dateId,
      timeId,
      partnerId
    }
    params = pickBy(params, identity)
    if (size(params) > 0) {
      dispatch(bookingActions.setUrlParams(params))
    }
  }, [])

  useEffect(() => {
    // reset multi schedule khi khác partnerId
    if (
      (firstSchedule && firstSchedule?.partnerId !== partnerId) ||
      (bookingTreeState?.headers?.partnerid &&
        bookingTreeState?.headers?.partnerid !== partnerId) ||
      (bookingTreeState?.queryData?.treeId &&
        bookingTreeState?.queryData?.treeId !== treeId)
    ) {
      dispatch(bookingActions.resetBookingFlow({}))
      setWaitingResetBookingTree(false)
    }
  }, [firstSchedule])

  const {
    index,
    steps,
    dataList,
    isComplete,
    selectedStepsData,
    processNextStep,
    backToStep,
    type,
    loading: loadingBookingTree,
    selectedSchedule,
    canBackToStep,
    isLater,
    filterCheckData,
    warningData,
    setWarningData
  } = useBookingTreeHelperApp(
    bookingTreeParams,
    {
      headers,
      waiting: waitingResetBookingTree,
      isChoosePatientStepOne: !!addSubject,
      patientId: PatientInfo?.id,
      initialBookingTreeData: reExamBookingTreeData,
      selectedData: reExamBookingData || urlParams,
      // selectedDataFromUrl: dataFromUrl,
      bookingTreeState: isClearBookingTreeState ? undefined : bookingTreeState
    },
    limitBookingByTreeId
  )

  const CanBackStep = (steps, step_back, step_current) => {
    const index_back = steps.indexOf(step_back)
    const index_current = steps.indexOf(step_current)

    const arr_remove = steps.slice(
      steps.findIndex((item) => step_back === item)
    )
    const isRemove = index_back < index_current

    if (isRemove) {
      arr_remove.map((item) => {
        if (!selectedSchedule?.[getDataKey(item)]) {
          delete router.query[getDataKey(item)]
        }
      })
    }
    step_current = step_back
    // router.query.stepName = step_current
  }

  useEffect(() => {
    const queryParams = router.query
    if (type) {
      CanBackStep(steps, type, stepName)
      setStepName(type)
    }
    if (selectedSchedule?.subjectId) {
      queryParams.subjectId = selectedSchedule?.subjectId
    }
    if (selectedSchedule?.serviceId) {
      queryParams.serviceId = selectedSchedule?.serviceId
    }
    if (selectedSchedule?.doctorId) {
      queryParams.doctorId = selectedSchedule?.doctorId
    }
    if (selectedSchedule?.date) {
      queryParams.date = selectedSchedule?.date
    }
    if (selectedSchedule?.timeId) {
      queryParams.timeId = selectedSchedule?.timeId
    }

    router.replace(
      { pathname: router.pathname, query: { ...queryParams } },
      undefined,
      { shallow: true }
    )
    // set UrlParams khi chọn lại step
    if (size(queryParams) > 0) {
      dispatch(bookingActions.setUrlParams({ ...queryParams, partnerId }))
    }
  }, [type])

  useEffect(() => {
    if (type) {
      updateStates([{ type: type, data: dataList }])
    }
  }, [type, dataList])

  useEffect(() => {
    if (isComplete) {
      return
    } else if (size(bookingTreeState?.selectedStepsData) > 0) {
      const filteredItems = bookingTreeState?.steps.filter(
        (item) =>
          bookingTreeState?.selectedStepsData?.some((i) => i.type === item) &&
          !bookingTreeState?.autoComplete?.includes(item)
      )
      updateStates(bookingTreeState?.selectedStepsData)
      const step = [
        ...new Set([
          ...filteredItems,
          bookingTreeState?.steps[bookingTreeState?.stepIndex]
        ])
      ]
      setCurrentSteps(step)
    } else {
      setCurrentSteps(
        bookingTreeState?.steps[0] ? [bookingTreeState.steps[0]] : []
      )
    }
  }, [bookingTreeState, index])

  const updateStates = (data) => {
    data.forEach((item) => {
      switch (item.type) {
        case 'doctor':
          setDoctors(item.data)
          break
        case 'subject':
          setSubjects(item.data)
          break
        case 'room':
          setRooms(item.data)
          break
        case 'service':
          setServices(item.data)
          break
        case 'date':
          setDates(item.data)
          setTimes(item.detail)
          break
        default:
          break
      }
    })
  }

  useEffect(() => {
    setLoading(true)
    timeout = setTimeout(() => {
      onSearch({ kw: undefined, pageIndex: count / PAGE_SIZE })
    }, 500)
  }, [count, partnerId])

  const handleCheckBHYT = (item: any) => {
    setServiceProps(item)
  }

  const handleAddon = (item: any) => {
    setServiceProps(item)
  }

  const handleSelectService = (data: any) => {
    const { service, extraData, addonServices } = data
    if (service && extraData?.optionBHYT !== undefined) {
      chooseService(data, service?.detail?.popupType, {
        isFilterCheckOk: isOkPopup
      })
    }

    if (size(addonServices) > 0) {
      chooseService(data, service?.detail?.popupType, {
        isFilterCheckOk: isOkPopup
      })
    }

    // Handle toggle select option BHYT
    if (
      service?.detail?.serviceType === 'BOTH'
      // || extraConfig.partnerId === 'umc'
    ) {
      handleCheckBHYT({
        checkBHYT: true,
        idService: service?.detail?.id,
        error: undefined,
        collapseID: '',
        optionBHYT: extraData?.optionBHYT,
        service
      })
    } else if (size(service?.detail?.addonServices) > 0) {
      handleAddon({
        idService: service?.detail?.id,
        checkAddonService: true
      })
    } else {
      chooseService(data, service?.detail?.popupType, {
        isFilterCheckOk: isOkPopup
      })
    }
  }

  // const handleChangeAddon = (e: RadioChangeEvent, service: any, v?: any) => {
  //   const dataAddon = [...addon, { addon: v, checkAddon: e.target.value }]
  //   setAddon(dataAddon)
  //   if (size(dataAddon) === size(service?.detail.addonServices)) {
  //     handleSelectService({
  //       service: service,
  //       addonServices: dataAddon
  //     })
  //   }
  // }

  const handleChangeBHYT = (
    e: RadioChangeEvent,
    service: any,
    titleOptionSelect?: string
  ) => {
    handleSelectService({
      service: service,
      extraData: {
        id: service?.detail.id,
        selectedBHYT: 'bhyt',
        typeInsurance: e.target.value,
        optionBHYT: e.target.value,
        contentBHYT: titleOptionSelect
      }
    })
  }

  const onSearch = useCallback(
    debounce(
      async ({
        kw,
        pageIndex,
        limmit
      }: {
        kw: string
        pageIndex?: number | string
        limmit?: number
      }) => {
        try {
          await client.doctor.doctor(
            {
              offset: floor(Number(pageIndex || 0)),
              limit: limmit || PAGE_SIZE,
              search_key: kw
            },
            {
              appid: '',
              partnerid: partnerId
            }
          )
        } catch (err) {
          // showError(err)
        } finally {
          clearTimeout(timeout)
          setLoading(false)
        }
      }
    ),
    [count, partnerId]
  )
  const nextStep = () => {
    if (size(selectedStepsData) > 0) {
      const filteredItems = bookingTreeState?.steps.filter(
        (item) =>
          bookingTreeState?.selectedStepsData?.some((i) => i.type === item) &&
          !bookingTreeState?.autoComplete?.includes(item)
      )
      updateStates(bookingTreeState?.selectedStepsData)
      const step = [
        ...new Set([
          ...filteredItems,
          !isLater && bookingTreeState?.steps[bookingTreeState?.stepIndex]
        ])
      ]

      setCurrentSteps(step)
    } else {
      setCurrentSteps([...steps.slice(0, index + 1)])
    }
  }

  const chooseDoctor = (item) => {
    const dataDoctor = selectedStepsData.find((item) => item.type === open.step)
    if (dataDoctor) {
      backToStep(
        open.step,
        item,
        undefined,
        false,
        onBackStepQuery({
          query: bookingTreeParams,
          stepBack: 'doctor',
          steps,
          setServiceProps
        })
      )
      form.resetFields()
      setDoctors(dataDoctor?.data)
    } else {
      processNextStep(item)
    }
    nextStep()
    onClose()
  }

  const chooseService = async (data, check?: any, nextStepOptions?: any) => {
    if (check === 1) {
      setShowPopup(true)
      setServiceData(data)
    } else {
      const { service, ...rest }: any = {
        service: {
          ...data?.service,
          detail: {
            ...data?.service?.detail,
            isPopupOk: true,
            popupType: check
          }
        },
        addonServices: data?.addonServices || {},
        extraData: data?.extraData || {}
      }
      if (selectedStepsData.find((item) => item.type === 'service')) {
        backToStep(
          'service',
          { ...service, ...rest },
          undefined,
          isOkPopup,
          onBackStepQuery({
            query: bookingTreeParams,
            stepBack: 'service',
            steps,
            multiBooking: addSubject
          })
        )
      } else if (nextStepOptions) {
        processNextStep({ ...service, ...rest }, nextStepOptions)
      } else {
        processNextStep({ ...service, ...rest })
      }
      nextStep()
      setOpen({
        step: 'service',
        status: false
      })
    }
  }

  const chooseSubject = async (item) => {
    nextStep()
    if (selectedStepsData.find((item) => item.type === 'subject')) {
      backToStep(
        'subject',
        item,
        undefined,
        false,
        onBackStepQuery({
          query: bookingTreeParams,
          stepBack: 'subject',
          steps,
          setServiceProps
        })
      )
    } else {
      processNextStep(item)
    }
    setOpen({
      step: 'subject',
      status: false
    })
  }

  const chooseRoom = async (item) => {
    nextStep()
    if (selectedStepsData.find((item) => item.type === 'room')) {
      backToStep(
        'room',
        item,
        undefined,
        false,
        onBackStepQuery({
          query: bookingTreeParams,
          stepBack: 'room',
          steps,
          setServiceProps
        })
      )
    } else {
      processNextStep(item)
    }
    setOpen({
      step: 'room',
      status: false
    })
  }

  const chooseDate = async (value) => {
    const pickDate = dates?.find(
      (item) =>
        moment(item.date).format('DD/MM/YYYY') ===
        moment(value).format('DD/MM/YYYY')
    )
    if (selectedStepsData.find((item) => item.type === 'date')) {
      backToStep('date', pickDate)
    } else {
      processNextStep(pickDate)
    }
    setSelectedDate(moment(value).format('DD/MM/YYYY'))
    setTimes(pickDate)
    nextStep()
    onClose()
  }

  const chooseTime = (value) => {
    router.query.timeId = value.timeId
    router.replace(
      { pathname: router.pathname, query: router.query },
      undefined,
      { shallow: true }
    )
    onClose()
    processNextStep(value)
  }

  const onFinish = () => {
    canBackToStep()
    if (size(multiSchedules) > 0) {
      router.replace(
        {
          pathname: router.pathname,
          query: pickBy(
            {
              ...router.query,
              step: 'xac-nhan-thong-tin',
              isBack: false
            },
            identity
          )
        },
        undefined,
        { shallow: true }
      )
      return
    }
    router.replace(
      {
        pathname: router.pathname,
        query: pickBy(
          {
            ...router.query,
            step: 'chon-ho-so',
            isBack: false
          },
          identity
        )
      },
      undefined,
      { shallow: true }
    )
  }

  const onClose = () => {
    setOpen({
      step: undefined,
      status: false
    })
    setOpenCheckBHYT({ id: '', open: false })
    // reset search
    setCount(PAGE_SIZE)
    form.resetFields()
  }

  const handleSearch = (v2: any) => {
    const { kw, type, setData } = v2
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      let stepData
      switch (type) {
        case 'doctor':
          stepData = doctors
          break
        case 'subject':
          stepData = subjects
          break
        case 'room':
          stepData = rooms
          break
        case 'service':
          stepData = services
          break
        default:
          stepData = []
          break
      }
      if (!kw) {
        setData(stepData)
        return
      }
      hangdleFilter({
        search: kw,
        data: stepData,
        fieldSearch: 'detail.name',
        setData
      })
    }, 500)
  }

  const hangdleFilter = ({ search, data, fieldSearch, setData }: any) => {
    let list = []
    list = handleFilterByKeyword(search, data, fieldSearch)
    setData(list)
  }
  // const onSearchDebounce = useCallback(async (event: any) => {
  // clearTimeout(timeout)
  // const kw = event.target.value
  // timeout = setTimeout(() => {
  //   setCount(PAGE_SIZE)
  //   void onSearch({ kw: kw, pageIndex: 0 })
  // }, 500)
  // }, [])

  const loadingBookingTreeTotal = !every(
    [waitingResetBookingTree, loadingBookingTree],
    (b) => !b
  )

  const renderContentStep = (step: string) => {
    switch (step) {
      case 'doctor': {
        const doctorValidIds = multiSchedules.map(
          (schedule) => schedule.doctorId
        )
        return {
          data: selectedSchedule?.doctor?.name,
          done:
            selectedStepsData?.find((item) => item.type === 'doctor') || false,
          disabled: !currentSteps?.includes(step),
          next: currentSteps[size(currentSteps) - 1] === 'doctor',
          onClick: () => {
            setTemp(
              doctors.filter((doctor) => !doctorValidIds.includes(doctor.id))
            )
            setOpen({
              step: 'doctor',
              status: true
            })
          }
        }
      }

      case 'subject': {
        const subjectValidIds = multiSchedules.map(
          (schedule) => schedule.subjectId
        )
        return {
          data: selectedSchedule?.subject?.name,
          done: selectedStepsData?.find((item) => item.type === 'subject'),
          disabled: !currentSteps?.includes(step),
          next: currentSteps[size(currentSteps) - 1] === 'subject',
          onClick: () => {
            setTemp(
              subjects.filter(
                (subject) => !subjectValidIds.includes(subject.id)
              )
            )
            setOpen({
              step: 'subject',
              status: true
            })
          }
        }
      }

      case 'room': {
        return {
          data: selectedSchedule?.room?.name,
          done: selectedStepsData?.find((item) => item.type === 'room'),
          disabled: !currentSteps?.includes(step),
          next: currentSteps[size(currentSteps) - 1] === 'room',
          onClick: () => {
            setTemp(rooms)
            setOpen({
              step: 'room',
              status: true
            })
          }
        }
      }
      case 'service':
        return {
          data: selectedSchedule?.service?.name,
          done: selectedStepsData?.find((item) => item.type === 'service'),
          disabled: !currentSteps?.includes(step),
          next: currentSteps[size(currentSteps) - 1] === 'service',
          onClick: () => {
            setTemp(services)
            setOpen({
              step: 'service',
              status: true
            })
          },
          contentBHYT: selectedSchedule?.service?.extraData?.contentBHYT
        }
      case 'date':
        return {
          data: selectedSchedule?.date
            ? moment(selectedSchedule?.date).format('DD/MM/YYYY')
            : size(multiSchedules) > 0
            ? moment(first(multiSchedules).date).format('DD/MM/YYYY')
            : '',
          done: selectedStepsData?.find((item) => item.type === 'date'),
          disabled:
            !currentSteps?.includes(step) ||
            size(
              multiSchedules.filter((item) => item.subjectId !== subjectId)
            ) > 0,
          next: currentSteps[size(currentSteps) - 1] === 'date',
          hidden: isLater,
          onClick: () => {
            setOpen({
              step: 'date',
              status: true
            })
            // Next-month nếu hết lịch khám trong tháng
            if (moment(dates?.[0]?.date).isAfter(moment(), 'month')) {
              setDate(moment()?.clone().add(1, 'month'))
            } else {
              setDate(moment())
            }
          }
        }
      case 'time':
        return {
          data: selectedSchedule?.timeslot
            ? selectedSchedule?.timeslot?.startTime +
              ' - ' +
              selectedSchedule?.timeslot?.endTime
            : '',
          done: selectedStepsData?.find((item) => item.type === 'time'),
          next: currentSteps[size(currentSteps) - 1] === 'time',
          hidden: isLater,
          onClick: () => {
            setOpen({
              step: 'time',
              status: true
            })
          },
          disabled: !currentSteps?.includes(step),
          selectedDate: selectedDate
        }
      case 'date-multi':
        return {
          data: moment(first(multiSchedules).date).format('DD/MM/YYYY'),
          done: true,
          disabled: true,
          next: currentSteps[size(currentSteps) - 1] === 'date',
          hidden: false,
          onClick: undefined
        }
      default:
        return
    }
  }

  const handleFilterByRole = (value: any, list: any[]) => {
    return list.filter((item) => value.includes(item.detail.role))
  }

  // Tìm theo giới tính
  const handleFilterBySex = (value: any, list: any[]) => {
    const listGenders = { Nam: 1, Nữ: 0 }
    return list.filter((item) => {
      return Number(item.detail.gender) === listGenders[value]
    })
  }

  // Tìm theo chuyên khoa
  const handleFilterBySubject = (value: any, list: any[]) => {
    const filterResult = list.filter((item) => {
      const subjectList = item?.detail?.subjects
      return subjectList.find((el: any) => value.includes(el.subject))
    })
    return filterResult
  }

  const handleSearchList = (value: any) => {
    setDataResult(value)
  }

  const hangdleFilterDoctor = async (role, sex, subject, keySearch) => {
    let list = doctors
    if (keySearch) {
      list = handleFilterByKeyword(keySearch, list, 'detail.name')
    }
    if (size(role) > 0) {
      list = handleFilterByRole(role, list)
    }
    if (size(subject) > 0) {
      list = handleFilterBySubject(subject, list)
    }
    if (size(sex) > 0) {
      list = handleFilterBySex(sex, list)
    }
    if (
      !(size(role) > 0) &&
      !(size(subject) > 0) &&
      !(size(sex) > 0) &&
      !keySearch
    ) {
      setTemp(doctors)
    } else {
      setTemp(list)
      // clearTimeout(timeout)
    }
  }
  console.log('partnerInfo', partnerInfo)
  return (
    <>
      <div className={styles['confirmPage']}>
        <div className={styles['hospitalTitle']}>
          <h1>
            {partnerInfo?.name}
            {partnerInfo?.listingPackagePaid && (
              <HiCheckBadge color='#0097FF' size={14} />
            )}
          </h1>
          <p>{partnerInfo?.address}</p>
        </div>
        <div className={styles['patientInfo']}>
          {loadingBookingTreeTotal ? (
            <Seleketon />
          ) : (
            <>
              {addSubject && (
                <>
                  <p className={styles['titleListSubject']}>
                    Thông tin đặt khám đã chọn
                  </p>
                  {multiSchedules.map((item: any, index: number) => {
                    return (
                      <div
                        key={index + 'multi'}
                        className={styles['bookingInfo']}
                      >
                        {item?.subject?.name && (
                          <p>
                            <FaStethoscope size={18} color='#11A2F3' />
                            {item?.subject?.name}
                          </p>
                        )}
                        {item?.doctor?.name && (
                          <p>
                            <FaUserMd size={18} color='#11A2F3' />
                            {item?.doctor?.role} {item?.doctor?.name}
                          </p>
                        )}
                        {item?.service?.name && (
                          <p style={{ alignItems: 'flex-start' }}>
                            <FaHandHoldingHeart size={18} color='#11A2F3' />
                            {item?.service?.name}
                          </p>
                        )}
                        {item?.date ? (
                          <p>
                            <BsCalendar2DateFill size={18} color='#11A2F3' />
                            {moment(item?.date).format('DD/MM/YYYY')}
                            {item?.timeslot?.startTime && (
                              <span>
                                ({item?.timeslot?.startTime} -{' '}
                                {item?.timeslot?.endTime})
                              </span>
                            )}
                          </p>
                        ) : (
                          <p>
                            <BsCalendar2DateFill size={18} color='#11A2F3' />
                            Chờ cập nhật
                          </p>
                        )}
                      </div>
                    )
                  })}
                </>
              )}
              {sortFlowBookingByStep(
                addSubject ? ['date-multi'].concat(steps) : steps
              ).map((item) => {
                return (
                  <>
                    {item.content({
                      ...renderContentStep(item.key)
                    })}
                  </>
                )
              })}
            </>
          )}
        </div>
        <ContentSteps
          step={open.step}
          title={PopupStep[open.step]?.title}
          placeholder={PopupStep[open.step]?.placeholder}
          loading={loading}
          stateSteps={open.status}
          dataSelect={
            selectedStepsData.find((item) => item.type === open.step)?.detail
              ?.id
          }
          hangdleFilterDoctor={hangdleFilterDoctor}
          onToggle={onClose}
          stepData={temp}
          setData={setTemp}
          listDoctors={doctors}
          stepRender={({
            isSelect,
            item,
            step
          }: {
            isSelect?: boolean
            item?: any
            step?: string
          }) => {
            switch (step) {
              case 'doctor':
                return (
                  <div key={item.id} className={styles['doctorItem']}>
                    <DoctorItem
                      isSelect={isSelect}
                      data={item}
                      handleBooking={chooseDoctor}
                      handleViewMore={() => {
                        console.log('function viewDetail :>> ')
                      }}
                    />
                  </div>
                )
              case 'subject':
                return (
                  <SubjectItem
                    data={item}
                    isSelect={isSelect}
                    handleBooking={() => {
                      chooseSubject(item)
                      form.resetFields()
                    }}
                  />
                )
              case 'room':
                return (
                  <RoomItem
                    data={item}
                    isSelect={isSelect}
                    handleBooking={() => {
                      chooseRoom(item)
                      form.resetFields()
                    }}
                  />
                )
              case 'service':
                return (
                  <ServiceItem
                    data={item}
                    isSelect={isSelect}
                    serviceProps={serviceProps}
                    extraConfig={extraConfig}
                    handleBooking={() => {
                      setIsOkPopup(false)
                      handleSelectService({
                        service: item,
                        addonServices: addon,
                        extraData: {
                          id: item?.detail.id,
                          selectedBHYT: ''
                        }
                      })
                      form.resetFields()
                    }}
                    handleChangeBHYT={handleChangeBHYT}
                  />
                )
              case 'date':
                return (
                  <CalendarContent
                    date={date}
                    dates={dates}
                    setDate={setDate}
                    onClose={onClose}
                    chooseDate={chooseDate}
                    selectedDate={selectedSchedule?.date}
                    noteBooking={extraConfig.warning_truoc_ngay}
                  />
                )
              case 'time':
                return (
                  <TimeContent
                    timeSelected={
                      selectedStepsData.find((item) => item.type === open.step)
                        ?.detail?.timeId
                    }
                    data={times}
                    hospital={hospital}
                    booking={booking}
                    chooseTime={chooseTime}
                    extraConfig={extraConfig}
                  />
                )
              default:
                return <></>
            }
          }}
          handleMore={() => {
            setCount((prev: any) => prev + PAGE_SIZE)
          }}
          extraFn={{
            handleSearch
            // onSearchDebounce
          }}
          form={form}
        />
        {showPopup && (
          <Modal
            open={showPopup}
            onCancel={() => setShowPopup(false)}
            title={serviceData?.service?.detail?.name}
            footer={
              <MPButton
                className={styles['modalButton']}
                onClick={() => {
                  setShowPopup(false)
                  chooseService(serviceData, 2, {
                    isFilterCheckOk: true
                  })
                }}
              >
                Đồng ý
              </MPButton>
            }
            className={styles['modal']}
            centered
          >
            <div
              dangerouslySetInnerHTML={{
                __html: serviceData?.service?.detail?.popupContent || ''
              }}
            />
          </Modal>
        )}
        {filterCheckData && (
          <Modal
            open={filterCheckData}
            onCancel={() => filterCheckData.onCancel()}
            title={filterCheckData?.title || 'Thông báo'}
            footer={
              <MPButton
                className={styles['modalButton']}
                onClick={() => {
                  setIsOkPopup(true)
                  filterCheckData.onOk()
                }}
              >
                Đồng ý
              </MPButton>
            }
            className={styles['modal']}
            centered
            closable={false}
          >
            <div
              dangerouslySetInnerHTML={{
                __html: filterCheckData?.content || ''
              }}
            />
          </Modal>
        )}
        {warningData && (
          <Modal
            title={'Thông báo!'}
            open={!!warningData}
            closable={false}
            className={styles['modal']}
            centered
            footer={
              <MPButton
                className={styles['modalButton']}
                onClick={() => setWarningData(undefined)}
              >
                Đóng
              </MPButton>
            }
          >
            {warningData && (
              <div
                dangerouslySetInnerHTML={{
                  __html: warningData?.warningMessage
                }}
              />
            )}
          </Modal>
        )}
      </div>
      <div className={styles['stickyButton']}>
        <MPButton
          className={styles['mpButton']}
          disabled={!isComplete}
          onClick={onFinish}
        >
          Tiếp tục
        </MPButton>
      </div>
    </>
  )
}
