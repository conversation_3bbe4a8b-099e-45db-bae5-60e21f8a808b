import cx from 'classnames'
import React, { useState } from 'react'
import styles from './styles.module.less'
import MPButton from '../../../MPButton'
import Image from 'next/image'
import { Rate, Space } from 'antd'
import { HiBadgeCheck } from 'react-icons/hi'
import { BiHeart } from 'react-icons/bi'
import { RiShareForwardLine } from 'react-icons/ri'
import ShareModal from '../ShareModal'
import { FormattedText } from 'libs/medpro-component-libs/src/lib/common/func'

interface IF_INFO {
  partnerInfo: any
  onBooking: (item: any) => void
}

export const InfoWrapper = ({ partnerInfo, onBooking }: IF_INFO) => {
  console.log('partnerInfo :>> ', partnerInfo)
  const rating = Number(partnerInfo?.rating)
  const [shareModalVisible, setShareModalVisible] = useState(false)
  return (
    <div className={cx(styles['InfoWrapper'])}>
      <div className={styles['Hospital']}>
        <Space
          direction='horizontal'
          className={styles['favourite']}
          align='center'
          style={{ width: '100%', justifyContent: 'flex-end' }}
        >
          <BiHeart size={20} />
          <RiShareForwardLine
            size={20}
            onClick={() => setShareModalVisible(true)}
          />
        </Space>
        <div className={styles['logo']}>
          <span className={styles['imageLogo']}>
            <Image
              src={partnerInfo?.image || defaultImageHospital}
              alt={partnerInfo?.name}
              width={150}
              height={156}
              objectFit='contain'
              layout='responsive'
            />
          </span>
          <div className={styles['logo_name']}>
            <h1 className={styles['name']}>
              {FormattedText(partnerInfo?.name)}
              {partnerInfo?.listingPackagePaid && <HiBadgeCheck size={18} />}
            </h1>
            <Space className={styles['ratingWrapper']} align='center'>
              <p
                className={cx(styles['rating'], !rating && styles['ratingOff'])}
              >
                ({rating + '/5' || 'Chưa đánh giá'})
                <Rate
                  disabled
                  allowHalf
                  defaultValue={rating}
                  style={{
                    fontSize: 18,
                    marginLeft: 4,
                    color: '#FFB54A'
                  }}
                />
              </p>
              <p className={styles['evaluate']}>
                {partnerInfo?.numberReviews || 0} đánh giá
              </p>
            </Space>
          </div>
        </div>
        <hr className={styles['line']} />
        <Space className={styles['info']} direction='vertical'>
          <h5>
            <strong>Địa chỉ: </strong>
            {partnerInfo?.address || 'Đang cập nhật'}
          </h5>
          <h5>
            <strong>Thời gian: </strong>
            {partnerInfo?.workingTime || 'Đang cập nhật'}
          </h5>
          <h5>
            <strong>Tổng đài đặt khám nhanh: </strong>
            <a
              href={
                partnerInfo?.hotlineMedpro
                  ? `tel:${partnerInfo?.hotlineMedpro?.replace(/\D/g, '')}`
                  : 'tel:19002115'
              }
            >
              {partnerInfo?.hotlineMedpro || '19002115'}
            </a>
          </h5>
        </Space>
      </div>
      <div className={styles['booking']}>
        <MPButton
          onClick={() => onBooking(partnerInfo)}
          className={styles['button']}
        >
          Đặt khám ngay
        </MPButton>
      </div>
      <ShareModal
        partnerInfo={partnerInfo}
        rating={rating}
        visible={shareModalVisible}
        onClose={() => setShareModalVisible(false)}
        url={typeof window !== 'undefined' ? window.location.href : ''}
      />
    </div>
  )
}
export const InfoMiniWrapper = ({ partnerInfo }: IF_INFO) => {
  const rating = Number(partnerInfo?.rating)

  return (
    <div className={cx(styles['InfoMiniWrapper'])}>
      <div className={styles['HospitalWrapper']}>
        <div className={styles['Logo']}>
          <Image
            src={partnerInfo?.image || defaultImageHospital}
            alt={partnerInfo?.name}
            width={60}
            height={60}
            objectFit='contain'
            layout='fixed'
          />
        </div>
        <div className={styles['Info']}>
          <h1 className={styles['name']}>
            {partnerInfo.name} <HiBadgeCheck size={24} />
          </h1>
          <Space className={styles['general']} direction='vertical'>
            <h5>
              <strong>Địa chỉ: </strong>
              {partnerInfo.address || 'Đang cập nhật'}
            </h5>
            <h5>
              <strong>Thời gian: </strong>
              {partnerInfo?.workingTime || 'Đang cập nhật'}
            </h5>
            <h5>
              <strong>Tổng đài đặt khám nhanh: </strong>1900 2115
            </h5>
          </Space>
        </div>
      </div>
      <div className={styles['HospitalExtra']}>
        <Space
          direction='horizontal'
          className={styles['favourite']}
          align='center'
          style={{ width: '100%', justifyContent: 'flex-end' }}
        >
          <BiHeart size={20} />
          <RiShareForwardLine size={20} />
        </Space>
        <Space className={styles['ratingWrapper']} align='center'>
          <p className={cx(styles['rating'], !rating && styles['ratingOff'])}>
            ({rating + '/5' || 'Chưa đánh giá'})
            <Rate
              disabled
              allowHalf
              defaultValue={rating}
              style={{
                fontSize: 18,
                marginLeft: 4,
                color: '#FFB54A'
              }}
            />
          </p>
          <p className={styles['evaluate']}>30 đánh giá</p>
        </Space>

        <div className={styles['booking']}>
          <MPButton
            // onClick={() => onBooking(partnerInfo)}
            className={styles['button']}
          >
            Đặt khám ngay
          </MPButton>
        </div>
      </div>
    </div>
  )
}
const defaultImageHospital =
  'https://cdn.medpro.vn/prod-partner/b4a3e286-ed97-40b5-ad7e-bb0ceb2dac4b-logo-hospital-default.png'
