.enterCustom {
  padding: 0 10px;
  border-radius: 5px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 0.875rem;
  line-height: 1.5715;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
  position: relative;
  display: inline-block;
  width: 100%;
}

.main {
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: left;
  padding: 0;
  background: #f6f6f6;
  // min-height: 87vh;

  .headerTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    background: #0ea2f3;
    font-size: 18px;
    font-weight: 600;
    line-height: 18px;
    text-align: center;
    color: #FFFFFF;
    margin-bottom: 1rem;
    padding: 0 16px;
    p {
      margin-bottom: 0;
    }
    .headerSide {
      width: 10%;
    }
  }
  
  .infoBox {
    padding: 16px;
    font-size: 16px;
    font-weight: 400;
    line-height: 19.36px;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    .infoItem {
      display: flex;
      gap: 0.5rem;
      align-items: flex-start;
      .label {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        min-width: 150px;
      }
      // span:first-child {
      //   width: 35%;
      // }
      // span:last-child {
      //   width: 65%;
      // }
    }
  }
  .footerBtn {
    position: sticky;
    bottom: 0;
    z-index: 199;
    width: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #ffffff;
    margin-top: 1rem;
    .checkedConfirm {
      margin-bottom: 1rem;
    }
    .btnSubmit {
      width: 100%;
      height: 50px;
      // top: 3rem;
      
      padding: 12px;
      border-radius: 12px;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
    }
    .disabledBtn {
      background: #d7dbe0;
      border: none;
      color: #ffffff;
    }
  }
}
