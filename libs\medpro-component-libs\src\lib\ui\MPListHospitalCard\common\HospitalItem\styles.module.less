.pickCard {
  border-color: #00b5f1 !important;
}
.boxHospital {
  border: 2px solid #00b5f1 !important;
  box-shadow: 0px 4px 15px rgba(116, 157, 206, 0.5) !important;
}

.tagCashBack {
  position: absolute;
  top: 13px;
  right: -62px;
  width: 190.81px;
  height: 25.86px;
  border: none;
  transform: rotate(35deg);
  background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
  text-transform: uppercase;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 12px;
  font-weight: 700;
  line-height: 17.58px;
  @media (max-width: 576px) {
    width: 107.53px;
    height: 20.77px !important;
    top: 10px;
    right: -25px;
    font-size: 10px;
    line-height: 11.72px;
  }
  &:hover {
    cursor: pointer;
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    color: white;
  }
  &:focus {
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    color: white;
  }
}
.popoverCashBack {
  :global {
    .ant-popover-content {
      border-radius: 12px !important;
      max-width: 510px;
    }
    .ant-popover-inner {
      backdrop-filter: blur(30px);
      background-color: rgba(255, 255, 255, 0.9);
    }
    .ant-popover-inner-content {
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
}

.card {
  position: relative;
  align-items: center;
  overflow: hidden;
  gap: 14px;
  padding: 20px 20px;
  box-shadow: 0px 4px 30px rgba(149, 179, 207, 0.2);
  border-radius: 20px;
  width: 100%;
  // max-height: 180px;
  margin-bottom: 26px;
  background: #ffffff;
  border: 2px solid transparent;
  transition: all 0.23s ease;

  &:hover {
    box-shadow: none;
    border: 2px solid #00b5f1;
  }
  &:last-child {
    margin-bottom: 0;
  }
  @media only screen and (max-width: 768px) {
    padding: 14px 16px;
  }
  @media only screen and (max-width: 577px) {
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px;
    border-radius: 12px;
    gap: 12px;
  }
  @media (max-width: 576px) {
    padding: 0;
    border: 1px solid transparent;
    &:hover {
      border: 1px solid #00b5f1;
    }
  }
  .DetailInfo {
    width: 100%;
    display: flex;
    gap: 10px;
    @media (max-width: 576px) {
      padding: 14px 16px 0 16px;
    }
  }
  .sponsorImage {
    position: absolute;
    top: 15px;
    right: -50px;
    border: none;
    color: white;
    width: 190.81px;
    height: 36.86px;
    transform: rotate(35deg);
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    text-transform: uppercase;
    text-align: center;
    padding: 0;
    @media (max-width: 576px) {
      top: 10px;
      right: -27px;
      width: 107.53px;
      height: 20.77px;
      font-size: 10px;
      font-weight: 700;
      line-height: 11.72px;
    }
    &:hover {
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }
    &:focus {
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }
  }
  .cardImage {
    min-width: 100px !important;
    min-height: 90px !important;
    @media (max-width: 768px) {
      width: fit-content !important;
      min-width: 60px !important;
      min-height: 50px !important;
      .sponsorImage {
        font-size: 10px;
      }
    }

    .contentDistance {
      display: flex;

      .contentDropdownDistance {
        font-size: 14px;
        color: #11a2f3;
        font-weight: 400;
      }

      .distanceNumber {
        font-size: 16px;
      }

      .distanceUnit {
        font-size: 16px;
      }
    }
  }
  .cardBody {
    // display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .cardContent {
      width: 100%;
      margin-bottom: 12px;
      @media only screen and (max-width: 630px) {
        max-width: 100%;
        margin-bottom: 8px;
      }
      .title {
        font-weight: 500;
        font-size: 25px;
        line-height: 29px;
        margin-bottom: 12px;
        width: 90%;
        color: var(--primary-body-text, #003553);
        svg {
          margin-top: 3px;
          vertical-align: top;
          @media (max-width: 576px) {
            margin-top: 2px;
            width: 18px;
            height: 18px;
          }
        }
        @media only screen and (max-width: 768px) {
          font-size: 20px;
          font-weight: 500;
          line-height: normal;
          width: 91%;
          margin-bottom: 4px;
        }
      }
      // .contentPrice {
      //   color: var(--accent, #ffb54a);
      //   font-size: 20px;
      //   font-style: normal;
      //   font-weight: 400;
      //   line-height: normal;
      //   margin: 0;
      //   strong {
      //     font-weight: 700;
      //   }
      // }
      .contentItem {
        display: flex;
        gap: 5px;
        font-weight: 400;
        font-size: 17px;
        line-height: 21px;
        color: #858585;
        margin-bottom: 8px;
        &:last-of-type {
          margin-bottom: 0;
        }
        @media only screen and (max-width: 768px) {
          font-size: 14px;
          line-height: 16px;
        }
        @media only screen and (max-width: 577px) {
          font-size: 15px;
          font-weight: 400;
          line-height: normal;
          svg {
            width: 12px !important;
            height: 12px !important;
          }
        }
      }
      .rating {
        color: #ffb54a;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
      }
      .ratingOff {
        color: #8f8e8d;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
      }
      .status {
        background: #ffb54a;
        border-radius: 16px;
        color: #ffffff;
        padding: 6px 12px;
        display: inline-block;
        font-weight: 500;
        font-size: 12px;
        line-height: 15px;
        margin-top: 10px;
        font-family: 'Montserrat', sans-serif !important;
        @media only screen and (max-width: 576px) {
          font-size: 12px;
          padding: 3px 7px;
        }
      }
    }
  }
  .desktop {
    // height: 42px;
    @media (max-width: 576px) {
      display: none !important;
    }
  }
  .mobile {
    display: none;
    @media (max-width: 576px) {
      position: relative;
      z-index: 0.2;
      display: block;
      background: #eff7ff;
      padding: 10px;
      justify-content: center;
      align-items: center;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
    }
  }
  .btnControl {
    display: flex;
    width: fit-content;
    gap: 12px;
    @media (max-width: 576px) {
      position: relative;
      bottom: 0;
      width: 100%;
    }
    button {
      border: none;
      padding: 10px 16px;
      width: 167px;

      transition: all 0.3s ease;
      border-radius: 30px;
      font-weight: 500;
      font-size: 16px;
      line-height: normal;
      &:hover {
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
      }
    }
    .btnView {
      color: #00b5f1;
      border: 1px solid #00b5f1;
      height: 42px !important;
      @media (max-width: 576px) {
        width: 50% !important;
      }
    }
    .btnBooking {
      background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
      color: white;
      height: 42px !important;
      @media (max-width: 576px) {
        width: 50% !important;
      }
    }
    .onlyBtn {
      @media (max-width: 576px) {
        width: 100% !important;
      }
    }
  }
  .sponsor {
    position: absolute;
    top: 0;
    right: 0;
    margin-top: -5px;
  }
  .sponsor_cashback {
    right: 60px;
    @media (max-width: 576px) {
      right: 48px;
      span {
        width: 35px !important;
        height: 42px !important;
      }
    }
  }
}
// Popover
.popover {
  // top: 668px !important;
  // left: 78px !important;
  // @media (max-width: 576px) {
  //   top: 668px !important;
  //   left: 78px !important;
  // }
  :global {
    .ant-popover-content {
      border-radius: 12px !important;
      width: fit-content;
      max-width: 700px;
    }
    .ant-popover-inner {
      backdrop-filter: blur(20px);
      // background-color: rgba(104, 126, 185, 0.16);
      background-color: #ffffffe5;
    }
    .ant-popover-inner-content {
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
}
// ************  Module Hoàn Tiền *************
.modalCashBack {
  .mobileCashBack {
    display: none;
    @media (max-width: 576px) {
      position: relative;
      z-index: 0.2;
      display: block;
      background: #eff7ff;
      padding: 10px;
      justify-content: center;
      align-items: center;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
    }
  }
  .btnControlCashBack {
    display: flex;
    width: 100%;
    gap: 12px;
    position: absolute;
    bottom: 20px;
    @media (max-width: 576px) {
      position: relative;
      bottom: 0;
    }
    button {
      border: none;
      padding: 10px 16px;
      width: 167px;

      transition: all 0.3s ease;
      border-radius: 30px;
      font-weight: 500;
      font-size: 16px;
      line-height: normal;
      &:hover {
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
      }
    }
    .btnViewCashBack {
      color: #00b5f1;
      border: 1px solid #00b5f1;
      height: 42px !important;
      @media (max-width: 576px) {
        width: 50% !important;
      }
    }
    .btnBookingCashBack {
      background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
      color: white;
      height: 42px !important;
      @media (max-width: 576px) {
        width: 50% !important;
      }
    }
    .onlyBtnCashBack {
      @media (max-width: 576px) {
        width: 100% !important;
      }
    }
  }
  :global {
    .ant-modal-footer {
      padding: 0;
      border-top: none;
    }
  }
}
