import client from '../config/medproSdk'
import { PaymentMethodQuery } from 'medpro-sdk-v2'

export const fetchExtraConfig = async () => {
  const { data } = await client.partnerConfig.getExtraConfig()
  return data
}

export const fetchPartnerInfo = async (partnerid?: string) => {
  const { data } = await client.partner.getPartnerInfo({ partnerid })
  return data
}

export const fetchRelationType = async () => {
  const { data } = await client.relation.getRelationType()
  return data
}

export const fetchProfession = async () => {
  const { data } = await client.profession.getList()
  return data
}

export const fetchNation = async () => {
  const { data } = await client.nation.getList()
  return data
}

// Fetch Data Home Page
export const fetchHomePageData = async () => {
  const { data } = await client.homePageGet.getListHomePage()
  return data
}

export const fetchPaymentInfo = async ({ queryKey }) => {
  const { data } = await client.paymentMethod.getPaymentInfo({
    transactionId: queryKey[1]
  })
  return data
}

export const fetchHospitalFeePaymentMethod = async (
  params: PaymentMethodQuery
) => {
  const { data } = await client.paymentMethod.getAllPaymentMethod(params)
  return data
}

export const fetchPaymentFeeTracking = async () => {
  const { data } = await client.booking.paymentFeeTracking()
  return data
}
export const fetchDeeplinkClient = async (slug: any) => {
  try {
    const { data } = await client.dynamicLink.getDoctorDynamicLink({ slug })
    return data
  } catch (err) {
    console.log('Lỗi fetchDeeplinkServer', err)
  }
}
export const fetchDeeplinkBookingClient = async (slug: any, action: any) => {
  try {
    const { data } = await client.dynamicLink.getDoctorBookingDynamicLink({
      slug,
      action
    })
    return data
  } catch (err) {
    console.log('Lỗi fetchDeeplinkBookingServer', err)
  }
}

export const fetchPartnerInfoBySlugV2 = async (slug: string) => {
  try {
    const { data } = await client.partner.getPartnerDetail(slug)
    return data
  } catch (err) {
    if (err.response?.status !== 404) {
      console.log('err fetchPartnerInfoBySlugServer: ', err)
    }
  }
}
