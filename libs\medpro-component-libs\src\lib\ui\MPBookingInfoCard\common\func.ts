import moment from 'moment'

export const getDay = (date: any) => {
  const nlBEFormatter = new Intl.DateTimeFormat('nl-BE')
  return nlBEFormatter.format(new Date(date).getTime())
}

export const getTimeBooking = (date: any) => {
  const arr = date.split(' ')
  return arr[0]
}

export const getTimezoneBooking = ({
  date,
  time,
  awaitMessage,
  waitingConfirmDate
}: {
  date: string
  time: string
  awaitMessage?: string
  waitingConfirmDate?: string
}) => {
  let DATE, TIME
  if (waitingConfirmDate && !date) {
    // chờ xác nhận
    DATE = waitingConfirmDate
    TIME = waitingConfirmDate
  } else if (awaitMessage) {
    // chờ cấp số bình thạnh => dùng biến timeStr
    DATE = getDay(date)
    TIME = time
  } else {
    // normal
    DATE = getDay(date)
    TIME = moment(date).format('HH:mm')
  }
  return [DATE, TIME]
}
