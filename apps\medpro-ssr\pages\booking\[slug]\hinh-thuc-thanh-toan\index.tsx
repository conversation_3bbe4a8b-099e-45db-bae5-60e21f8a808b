import SharePaymentApp from '../../../../src/page-content/bookingApp/sharePayment'
import CskhPaymentDesktop from '../../../../src/page-content/payment-cskh'
import { GetServerSidePropsContext } from 'next'

const SharePayment = (props: any) => {
  return props.isMobile ? <SharePaymentApp /> : <CskhPaymentDesktop />
}
export default SharePayment

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const userAgent = context.req.headers['user-agent']
  const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent)
  return {
    props: {
      isMobile
    }
  }
}
