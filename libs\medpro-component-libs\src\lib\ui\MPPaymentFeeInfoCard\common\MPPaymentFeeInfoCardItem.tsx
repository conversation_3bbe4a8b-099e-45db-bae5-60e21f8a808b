import React from 'react'
import { getFormatMoney } from '@medpro-libs/libs'
import { PaymentFeeInfo } from '../../../component/MPPaymentFeeInfoComponent/interface'
import styles from '../styles.module.less'
import {
  FaBriefcaseMedical,
  FaHandHoldingMedical,
  FaUserMd
} from 'react-icons/fa'
import { size } from 'lodash'
import { HiCalendarDays } from 'react-icons/hi2'
import { MdAccessTimeFilled } from 'react-icons/md'
import moment from 'moment'

const MPPaymentFeeInfoCardItem = ({
  subjectName,
  doctorName,
  serviceName,
  servicePrice,
  displayDetail,
  serviceAdvanced,
  addonServices,
  dateTime
}: PaymentFeeInfo) => {
  return (
    <div className={styles['paymentInfo']}>
      <li>
        {subjectName && (
          <div className={styles['itemSubject']}>
            <p className={styles['itemKeys']}>
              <FaBriefcaseMedical
                className={styles['itemIcon']}
                color='rgba(177, 177, 177, 1)'
                size={18}
              />
              <span>Chuyên khoa</span>
            </p>
            <p className={styles['itemValues']}>{subjectName}</p>
          </div>
        )}
      </li>
      <li>
        {doctorName && (
          <div className={styles['itemDoctor']}>
            <p className={styles['itemKeys']}>
              <FaUserMd
                className={styles['itemIcon']}
                color='rgba(177, 177, 177, 1)'
                size={18}
              />
              Bác sĩ
            </p>
            <p className={styles['itemValues']}>{doctorName}</p>
          </div>
        )}
      </li>
      {serviceName && (
        <li>
          <div className={styles['itemService']}>
            <p className={styles['itemKeys']}>
              <FaHandHoldingMedical
                className={styles['itemIcon']}
                color='rgba(177, 177, 177, 1)'
                size={18}
              />
              <span>Dịch vụ</span>
            </p>
            <p className={styles['itemValues']}>{serviceName}</p>
          </div>
        </li>
      )}
      {dateTime.date && (
        <li>
          <div className={styles['itemService']}>
            <p className={styles['itemKeys']}>
              <HiCalendarDays
                className={styles['itemIcon']}
                color='rgba(177, 177, 177, 1)'
                size={18}
              />
              <span>Ngày khám</span>
            </p>
            <p className={styles['itemValues']}>
              {moment(dateTime.date).format('DD/MM/YYYY')}
            </p>
          </div>
        </li>
      )}
      {dateTime.date && (
        <li>
          <div className={styles['itemService']}>
            <p className={styles['itemKeys']}>
              <MdAccessTimeFilled
                className={styles['itemIcon']}
                color='rgba(177, 177, 177, 1)'
                size={18}
              />
              <span>Giờ khám</span>
            </p>
            <p className={styles['itemValues']}>
              {dateTime.timeslot
                ? `${dateTime.timeslot.startTime} - ${dateTime.timeslot.endTime}`
                : moment(dateTime.date).format('HH:mm')}
            </p>
          </div>
        </li>
      )}
      <li>
        <div className={styles['itemMoney']}>
          <p className={styles['itemKeys']}>Tiền khám</p>
          <p className={styles['itemValues']}>
            {displayDetail
              ? displayDetail
              : getFormatMoney((servicePrice || 0) - (serviceAdvanced || 0)) +
                ' đ'}
          </p>
        </div>
      </li>
      {serviceAdvanced && serviceAdvanced > 0 ? (
        <li>
          <div className={styles['itemMoney']}>
            <p className={styles['itemKeys']}>Tạm ứng</p>
            <p className={styles['itemValues']}>
              {getFormatMoney(serviceAdvanced) + ' đ'}
            </p>
          </div>
        </li>
      ) : null}
      {size(addonServices) > 0 &&
        addonServices.map((item: any) => {
          return (
            <li key={item?.id}>
              <div className={styles['itemMoney']}>
                <p className={styles['itemKeys']}>{item.name}</p>
                <p className={styles['itemValues']}>
                  {Number(item?.price) !== 0
                    ? getFormatMoney(item.price) + ' đ'
                    : item.description}
                </p>
              </div>
            </li>
          )
        })}
    </div>
  )
}

export default MPPaymentFeeInfoCardItem
