import {
  FaBriefcaseMedical,
  FaHandHoldingMedical,
  FaHospitalAlt,
  FaUserMd
} from 'react-icons/fa'
import { Booking } from '../../../../component/MPAllBookingOfUserComponent/interface'
import { MdAccessTimeFilled, MdDateRange } from 'react-icons/md'
import { getTimezoneBooking } from '../../../MPBookingInfoCard/common/func'

export const HandleField = (Props: any) => {
  return Props?.listBooking.map((e: any) => {
    return {
      fullname: `${e?.surname} ${e?.name}`,
      bookingList: e?.bookings
    }
  })
}

export const mapStatus = (booking: Booking) => {
  let className = ''

  switch (booking.status) {
    case 1:
    case 2:
      className = 'SUCCESS'
      break

    case -2:
      className = 'CANCEL'
      break

    case 6:
    case 0:
      className = 'CHUATHANHTOAN'
      break
    default:
      break
  }
  return className
}

export const viewFilter = (item: any) => {
  const [DATE, TIME] = getTimezoneBooking({
    date: item?.date,
    time: item?.timeStr,
    awaitMessage: item?.awaitMessage,
    waitingConfirmDate: item?.waitingConfirmDate
  })
  const partnerId = item?.partnerId
  const serviceName = item?.serviceInfo?.name || item?.service?.name // Chọn service nếu serviceInfo không có
  return [
    {
      visible: true,
      key: 'name',
      value: item?.partner?.name,
      icon: <FaHospitalAlt size={18} color='#00b5f1' />,
      setting: {
        bold: '600',
        color: '#2698d6',
        upper: 'uppercase'
      },
      sort: 1
    },
    {
      visible: item?.subject?.name ? true : false,
      key: 'Chuyên khoa: ',
      value: `${item?.subject?.name}`,
      icon: <FaBriefcaseMedical size={18} color='#FFB54A' />,
      setting: {
        bold: '500',
        color: '#303233'
      },
      sort: 2
    },
    {
      visible: !!serviceName,
      key: item?.partnerId === 'bvmathcm' ? 'Hình thức khám:' : 'Dịch vụ:',
      value: `${serviceName}`,
      icon: <FaHandHoldingMedical size={18} color='#FFB54A' />,
      setting: {
        bold: '500',
        color: '#303233'
      },
      sort: 3
    },
    {
      visible: item?.doctor?.name ? true : false,
      key: 'Bác sĩ: ',
      value: `${item?.doctor?.name}`,
      icon: <FaUserMd size={18} color='#FFB54A' />,
      setting: {
        bold: '500',
        color: '#303233'
      },
      sort: 4
    },
    {
      visible: ![0, -2, 6].includes(item.status),
      key: 'Ngày khám: ',
      value: `${DATE}`,
      icon: <MdDateRange size={18} color='#FFB54A' />,
      setting: {
        bold: '500',
        color: '#2698d6'
      },
      sort: 5
    },
    {
      visible: ![0, -2, 6].includes(item.status),
      key:
        partnerId === 'trungvuong'
          ? 'Giờ tiếp nhận dự kiến: '
          : 'Giờ khám dự kiến: ',
      value: `${TIME}`,
      icon: <MdAccessTimeFilled size={18} color='#FFB54A' />,
      setting: {
        bold: '500',
        color: '#2698d6'
      },
      sort: 6
    }
  ]
}
