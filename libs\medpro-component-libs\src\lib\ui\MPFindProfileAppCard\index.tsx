import * as React from 'react'
import styles from './styles.module.less'
import cx from 'classnames'
import { Col, Form, Input, Modal, Row, Tabs, TabsProps } from 'antd'
import MPButton from '../MPButton'
import searchIcon from './common/searchIcon.svg'
import line from './common/line.svg'
import Image from 'next/image'
import { useState } from 'react'
import { handleDetails } from './common/handleDetails'
import SexSelectDrawer from '../common/SexSelectDrawer'
import DefautDrawer from '../DefaultDrawer'
import MobileSelectDrawer from '../common/MobileSelectDrawer'
import CardListProfile from './CardListProfile'
import { BiLeftArrowAlt } from 'react-icons/bi'
import { useRouter } from 'next/router'
import validator from 'validator'
import CardInfo from './common/CardInfo'

export interface Props {
  handleSubmit: (values: any) => void
  handleSubmitFindByInfo: (values: any) => void
  province: any[]
  data: any[]
  handleSelectPatient: (item: any) => void
  handleConfirmPhoneModal: (item: any) => void
  searching: boolean
  findExtra: boolean
  extraConfig: any
  partnerInfo: any
  selectedPatient: any
}

const MPFindProfileAppCard = ({
  handleSubmit,
  handleSubmitFindByInfo,
  province,
  data,
  handleSelectPatient,
  handleConfirmPhoneModal,
  searching,
  findExtra,
  extraConfig,
  partnerInfo,
  selectedPatient
}: Props) => {
  const router = useRouter()
  const [showFindWay, setShowFindWay] = useState(false)
  const [msbn, setMsbn] = useState('')
  const [phone, setPhone] = useState('')
  const [openPhoneConfirm, setOpenPhoneConfirm] = useState(false)
  const [openPickSex, setOpenPickSex] = useState(false)
  const [openPickCity, setOpenPickCity] = useState(false)
  const [dataSelect, setDataSelect] = useState<any>({})
  const [disabledBtn, setDisabledBtn] = useState(true)
  const [didSearch, setDidSearch] = useState(false)
  const [phoneError, setPhoneError] = useState(false)

  const [form] = Form.useForm()

  console.log(data)
  console.log(findExtra)
  console.log('axc', selectedPatient)

  const onFinish = () => {
    console.log({ msbn: msbn })
    handleSubmit({ msbn: msbn })
    setDidSearch(true)
  }

  const onFinishFindByInfo = (values: any) => {
    const splitName = values.name.split(' ')
    const name = splitName[splitName.length - 1]
    const surname = splitName.slice(0, -1).join(' ')

    const data = {
      ...values,
      surName: surname.trim().toUpperCase(),
      firstName: name.trim().toUpperCase(),
      gender: values.sex
    }
    delete data['name']
    delete data['sex']
    handleSubmitFindByInfo(data)
    setDidSearch(true)
  }

  const onChangeTab = () => {
    setDidSearch(false)
  }

  const handleFormChange = () => {
    const values = Object.values(form.getFieldsValue())
    const hasErrors = values.some((item) => item === undefined || item === '')
    setDisabledBtn(hasErrors)
  }

  const toggleSexDrawer = () => {
    setOpenPickSex((prev) => !prev)
  }

  const toggleCityDrawer = () => {
    setOpenPickCity((prev) => !prev)
  }

  const openSelect = (field: any) => {
    switch (field.id) {
      case 'sex':
        setOpenPickSex(true)
        return
      default:
        setDataSelect({ ...field })
        setOpenPickCity(true)
        return
    }
  }

  const handleSelect = (item: any) => {
    handleSelectPatient(item)
    if (item?.isVerifiedByPhone) {
      setOpenPhoneConfirm((prev) => !prev)
      setPhoneError(false)
    }
  }

  const handleChangePhone = (phone: string) => {
    setPhone(phone)
    if (phone) {
      if (!validator.isMobilePhone(phone, 'vi-VN')) {
        setPhoneError(true)
      } else {
        setPhoneError(false)
      }
    } else {
      setPhoneError(true)
    }
  }

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: `Nhập hồ sơ`,
      children: (
        <div className={styles['tabContent']}>
          <p className={styles['searchLabel']}>Nhập mã bệnh nhân/ mã hồ sơ</p>
          <div className={styles['searchBox']}>
            <Input
              type='  '
              placeholder='Nhập thông tin'
              onChange={(e) => setMsbn(e.target.value)}
            />
            <MPButton
              type='primary'
              htmlType='submit'
              full='true'
              className={cx({
                [styles['btnSubmit']]: true,
                [styles['disabledBtn']]: !msbn
              })}
              disabled={!msbn}
              onClick={onFinish}
            >
              Tìm
            </MPButton>
          </div>
          <p className={styles['note']}>
            Lưu ý: Số hồ sơ được in trên toa thuốc, phiếu chỉ định hoặc phiếu
            trả kết quả khám chữa bệnh
          </p>
          <div className={styles['findWay']}>
            <Image src={searchIcon} alt='search' />
            <span onClick={() => setShowFindWay((prev) => !prev)}>
              Xem cách tìm hồ sơ
            </span>
          </div>
          {showFindWay && (
            <div className={styles['findWayImage']}>
              <Image
                src={extraConfig?.findPatientCodeImg}
                alt='search'
                width={400}
                height={250}
              />
            </div>
          )}
          <CardListProfile
            data={data}
            handleSelect={handleSelect}
            searching={searching}
            didSearch={didSearch}
          />
        </div>
      )
    },
    {
      key: '2',
      label: `Quên hồ sơ`,
      children: (
        <div className={cx(styles['tabContent'], styles['tabContentForgot'])}>
          <p>Nhập thông tin bệnh nhân để tra cứu</p>
          <Form
            form={form}
            layout='vertical'
            initialValues={{ nation: 'medpro_1' }}
            onFinish={onFinishFindByInfo}
            onFieldsChange={handleFormChange}
            className={styles['listInfo']}
          >
            <Row gutter={12}>
              {handleDetails(openSelect, province, form).map((item, index) => {
                return (
                  <Col key={index} span={item.width === '100%' ? 24 : 12}>
                    <div
                      className={cx(
                        styles['inputItem'],
                        item.id === 'birthYear' && styles['inputItemHalfLeft'],
                        item.id === 'sex' && styles['inputItemHalfRight']
                      )}
                    >
                      {item?.enter && item?.enter(item)}
                    </div>
                  </Col>
                )
              })}
            </Row>
            <div className={styles['footerBtn']}>
              <MPButton
                className={cx({
                  [styles['btnSubmit']]: true,
                  [styles['disabledBtn']]: disabledBtn
                })}
                type='primary'
                htmlType='submit'
                full='true'
                disabled={disabledBtn}
              >
                Tìm
              </MPButton>
            </div>
          </Form>
          <CardListProfile
            data={data}
            handleSelect={handleSelect}
            searching={searching}
            didSearch={didSearch}
          />
          {openPickSex && (
            <SexSelectDrawer
              isOpen={openPickSex}
              toggleDrawer={toggleSexDrawer}
              form={form}
              handleFormChange={handleFormChange}
            />
          )}
          {openPickCity && (
            <MobileSelectDrawer
              isOpen={openPickCity}
              toggleDrawer={toggleCityDrawer}
              form={form}
              handleFormChange={handleFormChange}
              dataSelect={dataSelect}
            />
          )}
        </div>
      )
    }
  ]

  return (
    <div className={styles['main']}>
      <div className={styles['headerTitle']}>
        <BiLeftArrowAlt
          size={24}
          onClick={() => router.back()}
          className={styles['headerSide']}
        />
        <p>{!findExtra ? 'Tìm hồ sơ' : 'Tìm hồ sơ mở rộng'}</p>
        <div className={styles['headerSide']}></div>
      </div>
      {!findExtra ? (
        <Tabs
          defaultActiveKey='1'
          items={
            extraConfig?.tim_ho_so_mo_rong_btn ? items : items.slice(0, -1)
          }
          className={styles['tabs']}
          onChange={onChangeTab}
        />
      ) : (
        <div className={styles['findExtra']}>
          <div className={styles['selectedPatient']}>
            <p>Hồ sơ vừa được chọn</p>
            <CardInfo item={selectedPatient} />
          </div>
          <div className={styles['note']}>
            <p>
              Có <span>hồ sơ tương tự</span> với thông tin hồ sơ vừa chọn tại{' '}
              {partnerInfo?.name}. Nếu đã từng khám vui lòng chọn hồ sơ dưới
              đây.
            </p>
          </div>
          <CardListProfile
            data={data}
            handleSelect={handleSelect}
            searching={searching}
            didSearch={didSearch}
          />
        </div>
      )}
      {!findExtra && openPhoneConfirm && (
        <DefautDrawer
          onClose={() => setOpenPhoneConfirm((prev) => !prev)}
          open={openPhoneConfirm}
          title='Xác nhận hồ sơ'
          height='287px'
          className={styles['confirmDrawer']}
        >
          <p>
            Vui lòng nhập đầy đủ số điện thoại để xác nhận đây là hồ sơ của bạn
          </p>
          <input
            type='tel'
            // placeholder='Tìm kiếm'
            onChange={(e) => handleChangePhone(e.target.value)}
          />
          {!phone
            ? phoneError && (
                <p className={styles['phoneError']}>
                  Vui lòng nhập số điện thoại!
                </p>
              )
            : phoneError && (
                <p className={styles['phoneError']}>
                  Số điện thoại không đúng!
                </p>
              )}
          <MPButton
            className={cx({
              [styles['btnSubmit']]: true,
              [styles['disabledBtn']]: phoneError || !phone
            })}
            type='primary'
            // htmlType='submit'
            full='true'
            disabled={phoneError || !phone}
            onClick={() => handleConfirmPhoneModal(phone)}
          >
            Xác nhận
          </MPButton>
        </DefautDrawer>
      )}
      {findExtra && openPhoneConfirm && (
        <Modal
          title='Xác nhận hồ sơ'
          open={openPhoneConfirm}
          footer={null}
          centered
          onCancel={() => setOpenPhoneConfirm((prev) => !prev)}
          className={styles['confirmExtraDrawer']}
        >
          <p>
            Vui lòng nhập đầy đủ số điện thoại để xác nhận đây là hồ sơ của bạn
          </p>
          <input
            type='tel'
            autoFocus
            placeholder='Nhập số điện thoại'
            onChange={(e) => handleChangePhone(e.target.value)}
          />
          {!phone
            ? phoneError && (
                <p className={styles['phoneError']}>
                  Vui lòng nhập số điện thoại!
                </p>
              )
            : phoneError && (
                <p className={styles['phoneError']}>
                  Số điện thoại không đúng!
                </p>
              )}
          <MPButton
            className={cx({
              [styles['modalButton']]: true,
              [styles['disabledBtn']]: phoneError || !phone
            })}
            type='primary'
            // htmlType='submit'
            full='true'
            disabled={phoneError || !phone}
            onClick={() => handleConfirmPhoneModal(phone)}
          >
            Xác nhận
          </MPButton>
          {findExtra && (
            <div className={styles['phoneConfirmNote']}>
              {/* <p>Hoặc</p> */}
              <div className={styles['separate']}>
                <Image src={line} alt='separate' />
                <span>Hoặc</span>
                <Image src={line} alt='separate' />
              </div>
              <p
                className={styles['continue']}
                onClick={() => {
                  router.push({
                    pathname: '/chon-lich-kham',
                    query: {
                      ...router.query,
                      step: 'chon-ho-so'
                    }
                  })
                }}
              >
                Tiếp tục đặt khám không sử dụng hồ sơ này
              </p>
              <p>(Nếu bạn không nhớ chính xác số điện thoại)</p>
            </div>
          )}
        </Modal>
      )}
    </div>
  )
}

export default MPFindProfileAppCard
