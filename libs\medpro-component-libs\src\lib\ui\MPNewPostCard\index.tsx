import cx from 'classnames'
import moment from 'moment'
import Image from 'next/image'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { MdOutlineDateRange } from 'react-icons/md'
import MultiPlatform from './common/images/MultiPlatform.svg'
import Promotions from './common/images/Promotions.svg'
import RemoteConsultation from './common/images/RemoteConsultation.svg'
import SharedApplication from './common/images/SharedApplication.svg'
import Down from './common/images/direction-up.svg'
import styles from './styles.module.less'

export interface PostProps {
  data: any
}

const MPNewPostCard = (props: PostProps) => {
  const heightRef = React.useRef<HTMLDivElement>(null)
  const [showFull, setShowFull] = useState(false)
  useEffect(() => {
    if (
      heightRef?.current?.offsetHeight &&
      heightRef?.current?.offsetHeight < 1200
    ) {
      setShowFull(true)
    }
  }, [])

  return (
    <div className={styles['post']}>
      <h1 className={styles['title']}>{props.data.title}</h1>
      <div className={styles['tag']}>
        <div className={styles['icon']}>
          <MdOutlineDateRange size={16} />
        </div>
        <span>
          {moment(props.data?.created_at)
            .utc()
            .add(7, 'hours')
            .format('DD/MM/YYYY, hh:mm')}
        </span>
        {props.data?.author && props.data?.author !== ' ' && (
          <>
            <span> - </span>
            <span>{props.data?.author}</span>
          </>
        )}
      </div>
      <div className={styles['description']}>{props.data.description}</div>

      <div ref={heightRef} className={styles['contentWrapper']}>
        <div
          className={cx(
            styles['content'],
            !showFull ? styles['hiddenContent'] : styles['showFull']
          )}
          dangerouslySetInnerHTML={{
            __html: props.data.content
          }}
        />
        {!showFull && (
          <div
            onClick={() => {
              setShowFull(true)
            }}
            className={styles['viewMore']}
          >
            Xem tiếp
            <div className={styles['image']}>
              <Image
                src={Down}
                width={24}
                height={24}
                alt='Icon Down'
                layout='fixed'
              />
            </div>
          </div>
        )}
      </div>
      {showFull && (
        <div className={styles['callToAction']}>
          <div className={styles['titleCTA']}>
            <h2>
              Trải nghiệm Y Tế thông minh với <span>Medpro</span>
            </h2>
          </div>
          <div className={styles['contentCTA']}>
            <Link href='/#download'>
              <a
                className={cx(styles['contentClickCTA'])}
                title='Top 2 ứng dụng y tế được sử dụng nhiều nhất'
                rel='nofollow'
              >
                <Image
                  src={MultiPlatform}
                  alt='Top 2 ứng dụng y tế được sử dụng nhiều nhất'
                  width={150}
                  height={100}
                  layout='responsive'
                  title='Top 2 ứng dụng y tế được sử dụng nhiều nhất'
                />
                <span>Top 2 ứng dụng y tế được sử dụng nhiều nhất</span>
              </a>
            </Link>
            <Link href='https://medpro.vn/s/csyt'>
              <a
                className={styles['contentClickCTA']}
                title='01 ứng dụng kết nối bạn với hơn 100 cơ sở y tế'
                rel='nofollow'
              >
                <Image
                  src={SharedApplication}
                  alt='01 ứng dụng kết nối bạn với hơn 100 cơ sở y tế'
                  width={150}
                  height={100}
                  layout='responsive'
                  title='01 ứng dụng kết nối bạn với hơn 100 cơ sở y tế'
                />
                <span>01 ứng dụng kết nối bạn với hơn 100 cơ sở y tế</span>
              </a>
            </Link>
            <Link href='https://medpro.vn/s/gksk'>
              <a
                className={styles['contentClickCTA']}
                title='Đa dạng gói khám, dễ dàng chăm sóc sức khỏe'
                rel='nofollow'
              >
                <Image
                  src={Promotions}
                  alt='Đa dạng gói khám, dễ dàng chăm sóc sức khỏe'
                  width={150}
                  height={100}
                  layout='responsive'
                  title='Đa dạng gói khám, dễ dàng chăm sóc sức khỏe'
                />
                <span>Đa dạng gói khám, dễ dàng chăm sóc sức khỏe</span>
              </a>
            </Link>
            <Link href='https://medpro.vn/s/tu-van-tu-xa'>
              <a
                className={styles['contentClickCTA']}
                title='Kết nối từ xa với Bác Sĩ qua cuộc gọi Video'
                rel='nofollow'
              >
                <Image
                  src={RemoteConsultation}
                  alt='Kết nối từ xa với Bác Sĩ qua cuộc gọi Video'
                  width={150}
                  height={100}
                  layout='responsive'
                  title='Kết nối từ xa với Bác Sĩ qua cuộc gọi Video'
                />
                <span>Kết nối từ xa với Bác Sĩ qua cuộc gọi Video</span>
              </a>
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}

export default MPNewPostCard
// eslint-disable-next-line no-lone-blocks
{
  /* <div className={styles['viewCard']}>
        <div className={styles['views']}>
          <div className={styles['icon']}>
            <AiOutlineEye color='#003553' size={13} />
          </div>
          Lượt xem: {props.data.views}
        </div>
        <MPButton className={styles['btnLike']} type='default'>
          <AiFillLike color='#003553' size={16} />
        </MPButton>
        <MPButton className={styles['btnLike']} type='default'>
          <AiFillDislike color='#003553' size={16} />
        </MPButton>
        <div>Bài viết hữu ích?</div>
      </div>
      <div className={styles['tags']}>
        <div className={styles['titleTag']}>Chủ đề:</div>
        {props.data?.tags &&
          props.data?.tags?.map((item: any, index: number) => (
            <span key={index}>{item}</span>
          ))}
      </div> */
}
