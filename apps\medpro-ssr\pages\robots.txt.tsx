import { GetServerSideProps } from 'next'
// import { LIST_SUBPATH_PARTNERS } from '../utils/utils.contants'
// import { fetchHospitalsV6Server } from '../utils/utils.query-server'

const Robots = () => {
  return <>Robots</>
}

export default Robots

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  // const hospitals = await fetchHospitalsV6Server()
  const host = ctx.req?.headers['x-forwarded-host'] || ctx.req?.headers['host']
  ctx.res.setHeader('Content-Type', 'text/plain')
  let text = `Host: https://${host}\n`
  text += `\nUser-agent: *`

  if (
    !['medpro.vn', 'allnew-v2.medpro.com.vn', 'localhost:4200'].includes(
      host as string
    )
  ) {
    text += `\nDisallow: /*`
  } else {
    text += '\nAllow: /'
    text += `\nDisallow: /tim-kiem?kw=*`
    text += `\nDisallow: /authorized/`
    text += `\nDisallow: /_partner_v2/`
    text += `\nDisallow: /user/`
    text += `\nDisallow: /share/`
    text += `\nDisallow: /transactions/`
    text += `\nDisallow: /xac-nhan-thong-tin/`
    text += `\nDisallow: /reexam/`
    text += `\nDisallow: /phuong-thuc-thanh-toan/`
    text += `\nDisallow: /chi-tiet-ma-thanh-toan/`
    text += `\nDisallow: /hinh-thuc-thanh-toan-vien-phi/`
    text += `\nDisallow: /chi-tiet-phieu-kham-benh/`
    text += `\nDisallow: /getapp/`
    text += `\nDisallow: /getapp_bs/`
    text += `\nDisallow: /tao-moi-ho-so/`
    text += `\nDisallow: /thong-tin-dat-kham-momo/`
    text += `\nDisallow: /trans/`
    text += `\nDisallow: /xac-nhan-thong-tin-benh-nhan/`
    text += `\nDisallow: /xac-thuc-dien-thoai-benh-nhan/`
    text += `\nDisallow: /api/`
    text += `\nDisallow: /app-info-version/`
    text += `\nDisallow: /check-patient-before-create/`
    text += `\nDisallow: /chi-tiet-phieu-vien-phi/`
    text += `\nDisallow: /chi-tiet-thanh-toan-ho/`
    text += `\nDisallow: /chon-ho-so/`
    text += `\nDisallow: /clinic/`
    text += `\nDisallow: /dang-ki-phong-kham-phong-mach/`
    text += `\nDisallow: /dang-ky-xet-nghiem/`
    text += `\nDisallow: /danh-sach-hoa-don-dien-tu/`
    text += `\nDisallow: /hinh-thuc-dat-kham/`
    text += `\nDisallow: /huong-dan-bao-hiem-y-te-trai-tuyen/`
    text += `\nDisallow: /huong-dan-bao-hiem-y-te-trai-tuyen-app/`
    text += `\nDisallow: /kenh-tao-moi-ho-so/`
    text += `\nDisallow: /ket-qua-thanh-toan-vien-phi/`
    text += `\nDisallow: /ket-qua-tim-benh-nhan/`
    text += `\nDisallow: /lich-su-thanh-toan-vien-phi/`
    text += `\nDisallow: /thanh-toan-ho/`
    text += `\nDisallow: /tim-lai-ma-benh-nhan/`
    text += `\nDisallow: /tim-ma-thanh-toan-vien-phi/`
    text += `\nDisallow: /tra-cuu-hoa-don-dien-tu/`
    text += `\nDisallow: /tra-ket-qua-kham-benh/`
    text += `\nDisallow: /khao-sat/`
    text += `\nDisallow: /cap-nhat-thong-tin/`
    text += `\nDisallow: /quy-dinh-su-dung-app/*`
    text += `\nDisallow: /chinh-sach-bao-mat-app/*`
    text += `\nDisallow: /dieu-khoan-dich-vu-app/*`
    text += `\nDisallow: /thac-mac-app/*`
    text += `\nDisallow: /dich-vu-dat-kham`
    text += `\nDisallow: /b/*`
    text += `\nDisallow: /*?w=*&q=*`
    text += `\nDisallow: /*?w=*`
    text += `\nDisallow: /*&w=*`
    text += `\nDisallow: /*?q=*`
    text += `\nDisallow: /*&q=*`

    // for (const path of hospitals) {
    //   text += `\nDisallow: /dich-vu-y-te/*/${path?.slug}$`
    // }
    text += `\n`
  }

  text += `\nSitemap: https://${host}/sitemap/page-sitemap.xml`
  text += `\nSitemap: https://${host}/sitemap/post-service-sitemap.xml`
  text += `\nSitemap: https://${host}/sitemap/post-medical-sitemap.xml`
  text += `\nSitemap: https://${host}/sitemap/post-medicine-sitemap.xml`
  text += `\nSitemap: https://${host}/sitemap/doctor-sitemap.xml`
  text += `\nSitemap: https://${host}/sitemap/hospital-sitemap.xml`
  text += `\nSitemap: https://${host}/sitemap/package-sitemap.xml`

  ctx.res.write(text)
  ctx.res.end()

  return { props: {} }
}
