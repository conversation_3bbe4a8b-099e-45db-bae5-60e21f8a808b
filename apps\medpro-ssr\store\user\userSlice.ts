import { createAction, createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { UserInfo, UserState, UserMomoData } from './interface'

const initialState: UserState = {
  accessToken: '',
  loading: true,
  userMomo: {
    loading: false,
    data: null,
    error: null
  }
}

const reHydrate = createAction<any>('persist/REHYDRATE')
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Redux Toolkit allows us to write "mutating" logic in reducers. It
    // doesn't actually mutate the state because it uses the Immer library,
    // which detects changes to a "draft state" and produces a brand new
    // immutable state based off those changes
    setToken: (state, action: PayloadAction<string>) => {
      state.accessToken = action.payload
    },
    resetToken: (state) => {
      state.accessToken = ''
      // const { token, ...rest } = state
      // return rest
    },
    setUserInfo: (state, action: PayloadAction<UserInfo>) => {
      state.userInfo = action.payload
    },
    login: (state, action: PayloadAction<void>) => {
      // run saga
    },
    getUserInfo: (state, action: PayloadAction<void>) => {
      // run saga
    },
    logOut: (state, action: PayloadAction<void>) => {
      state.userInfo = undefined
      state.accessToken = undefined
    },
    loading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload
    },
  }
  // extraReducers: (builder) => {
  //   builder.addCase(reHydrate, (s, action) => {
  //     console.log('action: ', action)
  //     if ((action as any).key === 'user') {
  //       s.accessToken = ''
  //       s.userInfo = undefined
  //     }
  //   })
  // }
})

// Action creators are generated for each case reducer function
export const { setUserInfo, login, logOut } = userSlice.actions

export const userActions = userSlice.actions

export default userSlice.reducer
