import { MPFindProfileApp } from '@medpro-libs/libs'
import * as React from 'react'
import client from '../../../../config/medproSdk'
import { patientActions } from '../../../../store/patient/patientSlice'
import { useDispatch } from 'react-redux'
import { showErrorNotification } from '../../../../utils/utils.error'
import { useAppSelector } from '../../../../store/hooks'
import { useEffect, useState } from 'react'
import { size } from 'lodash'
import { fetchProvinces, setBreadcrumb } from '../../../../store/total/slice'
import router, { useRouter } from 'next/router'
import { openNotification } from '../../../../utils/utils.notification'

export interface IFindProfileAppProps {}

export default function FindProfileApp(props: IFindProfileAppProps) {
  const router = useRouter()
  const partnerId = router.query.partnerId as string
  const findExtra = router.query.findExtra as string
  const dispatch = useDispatch()
  const province = useAppSelector((s) => s.total.provinces)
  const oldPatient = useAppSelector((s) => s.patient.oldPatient)
  const extraConfig = useAppSelector((state) => state.hospital?.extraConfig)
  const partnerInfo = useAppSelector((state) => state.hospital?.partnerInfo)
  const selectedPatient = useAppSelector(
    (state) => state.patient.selectedPatient
  )
  const [patientInfoForVerifyPhone, setPatientInfoForVerifyPhone] = useState({
    msbn: '',
    patientId: '',
    secretPatientId: ''
  })
  const [searching, setSearching] = useState(false)

  console.log(oldPatient)

  useEffect(() => {
    if (!findExtra) {
      dispatch(patientActions.resetOldPatient())
      if (size(province) === 0) {
        dispatch(fetchProvinces({ country_code: '203' }))
      }
    }
    dispatch(setBreadcrumb([]))
  }, [])

  const onFindProfile = async (data: any) => {
    try {
      setSearching(true)
      const res = await client.patient.findByMsbn(data)
      dispatch(patientActions.findOldPatientOnSuccess(res.data))
    } catch (err) {
      dispatch(patientActions.resetOldPatient())
    } finally {
      setSearching(false)
    }
  }

  const onSelectPatient = async (item: any) => {
    console.log('params confirm phone', item)
    const { patientCode, id, secretPatientId } = item
    dispatch(patientActions.setSelectedOldPatient(item))
    if (item?.isVerifiedByPhone) {
      setPatientInfoForVerifyPhone({
        msbn: patientCode,
        patientId: id,
        secretPatientId: secretPatientId
      })
    } else {
      router.push({
        pathname: '/xac-nhan-thong-tin-benh-nhan-app',
        query: {
          ...router.query
        }
      })
    }
  }

  const onConfirmPhoneModal = async (phone: string) => {
    const { msbn, patientId, secretPatientId } = patientInfoForVerifyPhone
    try {
      const { data } = await client.patient.verifyPhone({
        phone,
        msbn,
        patientId,
        secretPatientId
      })
      if (data) {
        dispatch(patientActions.setSelectedOldPatient(data?.patient))
        router.push({
          pathname: '/xac-nhan-thong-tin-benh-nhan-app',
          query: {
            ...router.query
          }
        })
      }
    } catch (err) {
      openNotification('error', {
        message:
          'Xin lỗi, số điện thoại bạn nhập không trùng khớp với hồ sơ tại bệnh viện'
      })
    }
  }

  const onSubmitFindByInfo = async (values: any) => {
    try {
      setSearching(true)
      const res = await client.patient.findByExtraInfo(values)
      dispatch(patientActions.findOldPatientOnSuccess(res.data))
      // router.push(
      //   getRoutePartnerIdQueryParams(PageRoutes.oldPatient.confirmPhone.path, partnerId)
      // )
    } catch (err) {
      showErrorNotification(err)
      dispatch(patientActions.resetOldPatient())
    } finally {
      setSearching(false)
    }
  }

  console.log(findExtra)

  return (
    <MPFindProfileApp
      onSubmit={onFindProfile}
      onSubmitFindByInfo={onSubmitFindByInfo}
      province={province}
      data={oldPatient?.data}
      onSelectPatient={onSelectPatient}
      onConfirmPhoneModal={onConfirmPhoneModal}
      searching={searching}
      findExtra={!!findExtra}
      extraConfig={extraConfig}
      partnerInfo={partnerInfo}
      selectedPatient={selectedPatient}
    />
  )
}
