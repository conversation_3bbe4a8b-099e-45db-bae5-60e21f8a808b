.headerMobile {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f6f6f6;
  padding: 16px;
  .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 19.36px;
    text-align: center;
    color: #11a2f3;
    margin-bottom: 0;
  }
}
.body {
  background: #f6f6f6;
  min-height: calc(100vh - 54px);
  @media (max-width: 1024px) {
    overflow-x: hidden;
  }
  @media (max-width: 576px) {
    overflow-x: visible !important;
  }
  color: #003553;
  a {
    color: #003553;
    &:hover {
      color: #003553;
    }
  }
  * {
    font-family: 'Roboto', sans-serif !important;
  }
}
.support {
  z-index: 999;
  position: fixed;
  bottom: 120px;
  right: 20px;
  background: #ffffff;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  /* Drop shadow */
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.04),
    0px 2px 6px 0px rgba(0, 0, 0, 0.04), 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
  border-radius: 50%;
  cursor: pointer;
}
.modal {
  border-radius: 16px !important;
  :global {
    .ant-modal-header {
      background-color: #fff !important;
      border-radius: 16px;
      border: none;
      padding: 16px 24px 0 24px;
    }
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-body {
      color: var(--primary-body-text, #003553);
      font-family: 'Roboto' !important;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .ant-modal-close-x {
      svg {
        fill: #000;
      }
    }
    .ant-modal-title {
      text-align: center;
      font-family: 'Roboto' !important;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      background: var(
        --primary-gradient-title,
        linear-gradient(84deg, #00b5f1 33.34%, #00e0ff 113.91%)
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
.cashBackWrapper {
  display: flex;
  width: 100%;
  max-height: 43px;
  padding: 8px 16px;
  background: rgba(255, 179, 64, 1);
  white-space: nowrap;
  overflow: hidden;
  z-index: -1;
  .cashBackContent {
    animation: animationlefttoright 150s linear infinite;
    width: fit-content;
    margin: 0;
    font-weight: 500;
    font-size: 12px;
    line-height: 14.75px;
    color: rgba(255, 255, 255, 1);
  }
}
@keyframes animationlefttoright {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(-100%);
  }
}
