import { getContentPages } from '../../../utils/utils.query-server'
import { noLayout } from '../../../layout'
import ThacMac from '../../../src/components/pages/ThacMac'
import { GetServerSidePropsContext } from 'next'
const LayoutWebview = (props: any) => {
  return <ThacMac {...props} />
}
LayoutWebview.getLayout = noLayout
LayoutWebview.removeChat = true
export default LayoutWebview
export async function getServerSideProps(context: GetServerSidePropsContext) {
  const locale = (context.query.locale as string) || 'vi'

  const appid = 'medpro'
  const partnerId = 'canthozone'
  const data = await getContentPages('thac-mac', {
    appid,
    partnerid: partnerId,
    locale
  })
  console.log('data', data)
  const pathName = context.resolvedUrl
  const isWebView = pathName.includes('-app')

  return {
    props: {
      data,
      isWebView,
      robots: 'noindex, nofollow'
    }
  }
}
