.text(@fontsize: 0, @fontweight: 0, @lineheight: 0, @textalign: left, @color: #003553) {
  font-family: Roboto !important;
  font-size: @fontsize;
  font-weight: @fontweight;
  line-height: @lineheight;
  text-align: @textalign;
  color: @color;
}
.DetailDoctorTelemed {
  position: relative;
  overflow: hidden;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease-in;
  border-radius: 12px;
  box-shadow: 0px 4.5px 13px #01499e59;
  padding: 12px;
  min-width: 277px;
  max-width: 277px;
  height: 100%;
  &:hover {
    cursor: pointer;
  }
  @media (max-width: 768px) {
    // min-width: 280px;
    overflow-x: clip;
  }
  @media (max-width: 576px) {
    min-width: 183px;
    max-width: 183px;
    min-height: 237px;
    padding: 8px;
    box-shadow: 4px 8px 12px 0px #01499e3d;
  }
  @media (max-width: 430px) {
    min-width: 210px;
    max-width: 210px;
  }
  @media (max-width: 415px) {
    min-width: 200px;
    max-width: 200px;
  }
  @media (max-width: 390px) {
    min-width: 190px;
    max-width: 190px;
  }
  .tagCashBack {
    position: absolute;
    top: 13px;
    right: -62px;
    width: 190.81px;
    height: 25.86px;
    border: none;
    transform: rotate(35deg);
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    text-transform: uppercase;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 12px;
    font-weight: 700;
    line-height: 17.58px;
    @media (max-width: 576px) {
      width: 107.53px;
      height: 20.77px !important;
      top: 10px;
      right: -25px;
      font-size: 10px;
      line-height: 11.72px;
    }
    &:hover {
      cursor: pointer;
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }
    &:focus {
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }
  }
  .popoverCashBack {
    :global {
      .ant-popover-content {
        border-radius: 12px !important;
        max-width: 510px;
      }
      .ant-popover-inner {
        backdrop-filter: blur(30px);
        background-color: rgba(255, 255, 255, 0.9);
      }
      .ant-popover-inner-content {
        p {
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
  }
  .DetailDoctorTelemedCard {
    background: #ffffff;
    // margin: 12px 12px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 160px !important;
    max-height: 160px !important;
    @media (max-width: 576px) {
      min-height: 90px !important;
      max-height: 90px !important;
    }

    .DetailDoctorTelemedCardImage {
      border-radius: 12px;
      min-height: 130px !important;
      max-height: 130px !important;
      width: 100% !important;
      max-width: 130px;
      span,
      img {
        width: 130px !important;
        height: 130px !important;
        min-height: 130px !important;
        max-height: 130px !important;
        border-radius: 50%;
      }
      @media screen and (max-width: 576px) {
        min-height: 80px !important;
        max-height: 80px !important;
        width: 100% !important;
        max-width: 80px;

        span,
        img {
          width: 80px !important;
          height: 80px !important;
          min-height: 80px !important;
          max-height: 80px !important;
          border-radius: 50%;
        }
      }
    }
  }
  .DetailDoctorTelemedCardContent {
    .CardContentHeader {
      margin-top: 12px;
      @media screen and (max-width: 576px) {
        margin-top: 4px;
      }
      .role {
        .text(20px, 400, 23.44px, left, #003553);
        margin-bottom: 0;
        @media (max-width: 576px) {
          font-size: 16px;
          line-height: 18.75px;
        }
        @media (max-width: 390px) {
          font-size: 14px;
          line-height: 16.41px;
        }
      }
      .name {
        .text(20px, 500, 23.44px, left, #003553);
        margin-bottom: 0;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        overflow: hidden;
        @media (max-width: 576px) {
          font-size: 16px;
          line-height: 18.75px;
        }
        @media (max-width: 390px) {
          font-size: 14px;
          line-height: 16.41px;
        }
      }
    }
    .CardContentBody {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 12px;
      @media (max-width: 576px) {
        gap: 4px;
        margin-top: 8px;
      }
      .CardContentBodyText {
        display: flex;
        align-items: center;
        gap: 4px;
        p {
          margin-bottom: 0;
          .text(16px, 400, 18.75px);
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
          overflow: hidden;
          @media (max-width: 576px) {
            font-size: 14px;
            line-height: 16.41px;
          }
          @media (max-width: 390px) {
            font-size: 12px;
            line-height: normal;
          }
        }
        span {
          @media (max-width: 576px) {
            width: 13px !important;
            height: 13px !important;
          }
        }
      }
    }
    .CardContentBodyEvaluate {
      position: relative;
      display: flex;
      justify-content: space-between;

      background-color: #ebf9fd;
      padding: 9.5px 12px;
      margin-left: -12px;
      margin-right: -12px;
      @media (max-width: 576px) {
        margin-left: -8px;
        margin-right: -8px;
        padding: 6.5px 8px;
      }
      @media (max-width: 375px) {
        margin-left: -8px;
        margin-right: -8px;
        padding: 4.5px 8px;
      }
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 5px;
        margin-bottom: 0;

        label {
          .text(12.53px, 500, 14.68px, left, #003553);
          color: #003553;
          @media (max-width: 567px) {
            display: none;
          }
        }
        span {
          .text(15.03px, 500, 17.62px, left, #003553);
          color: #ffb54a;
          @media (max-width: 567px) {
            font-size: 16px;
            line-height: 18.75px;
            svg {
              width: 11.33px;
              height: 10.67px;
            }
          }
        }
      }
    }
  }
  .DoctorTelemedFoorterCard {
    background: #ffffff;
    margin-top: 12px;
    position: relative;
    .DoctorTelemedButton {
      width: 100%;
      border: 1px solid #00b5f1;
      border-radius: 8px;
      background-color: #00b5f1;
      color: white;
      font-weight: 600;
      font-size: 16px;
      line-height: 18.75px;
      &:hover {
        color: #00b5f1;
        background-color: white;
        border-color: #00b5f1;
      }
      @media (max-width: 567px) {
        font-size: 14px;
        font-weight: 500;
        line-height: 16.41px;
        height: fit-content !important;
      }
    }
  }
}
