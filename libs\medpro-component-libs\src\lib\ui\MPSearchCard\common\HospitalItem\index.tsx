import cx from 'classnames'
import { SEARCH_LIST, defaultImage } from '../../../../common/constant'
import Image from 'next/image'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import MPButton from '../../../MPButton'
import styles from './styles.module.less'
import { Popover, Rate } from 'antd'
import { useWindowDimensions } from 'libs/medpro-component-libs/src/lib/hooks/useWindowDimesion'
import { useState } from 'react'
import DefaultDrawer from '../../../DefaultDrawer'
import { HiCheckBadge } from 'react-icons/hi2'
const imgSponsor = require('../../../MPDetailHealthServicesCard/img/sponsor.svg')
interface Props {
  data: any
  handleBooking: (type: string, item: any) => void
  handleViewMore: (type: string, item: any) => void
}

const HospitalItem = (props: Props) => {
  const { data } = props
  const rating = Number(data?.description?.rating)

  const { windowWidth } = useWindowDimensions()
  const [drawerCashBack, setDrawerCashBack] = useState(false)
  const [Visible, setVisible] = useState(false)
  const trigger = windowWidth > 576 ? 'hover' : 'click'

  const onShow = (e: any) => {
    e.stopPropagation()
    if (trigger === 'click') {
      setDrawerCashBack(true)
    }
  }

  const onHidden = (e: any) => {
    e.stopPropagation()
    setDrawerCashBack(false)
  }

  return (
    <div className={cx(styles['card'])}>
      {data?.isCashBack && (
        <div className={styles['tagCashBack']} onClick={onShow}>
          <Popover
            showArrow={true}
            overlayClassName={styles['popoverCashBack']}
            overlayInnerStyle={{ width: 510 }}
            content={
              data?.popup?.content && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: data?.popup?.content
                  }}
                />
              )
            }
            onOpenChange={(visible) => {
              if (windowWidth > 576 && visible) {
                setVisible(true)
              } else {
                setVisible(false)
              }
            }}
            open={Visible}
            placement='bottomLeft'
          >
            Hoàn tiền
          </Popover>
        </div>
      )}
      <div className={styles['DetailInfo']}>
        <div className={styles['cardImage']}>
          <Image
            src={data?.imageUrl || defaultImage}
            width={90}
            height={90}
            objectFit='contain'
            alt={data.title}
          />
        </div>
        <div className={styles['cardBody']}>
          <div className={styles['cardContent']}>
            <h3
              className={cx(
                styles['title'],
                data?.description?.sponsored && styles['titleSponsor']
              )}
            >
              {data.title}
              {data?.partner?.listingPackagePaid && (
                <HiCheckBadge color='#0097FF' size={22} />
              )}
            </h3>
            {data.hospitalAddress && (
              <p className={styles['contentItem']}>
                <span>
                  <HiOutlineLocationMarker size={21} color='#858585' />
                </span>
                {data.hospitalAddress}
              </p>
            )}
            {data?.deliveryMessage && (
              <div className={styles['status']}>{data?.deliveryMessage}</div>
            )}
            <p className={cx(styles['rating'], !rating && styles['ratingOff'])}>
              ({rating || 'Chưa đánh giá'})
              <Rate
                disabled
                allowHalf
                value={rating}
                style={{
                  fontSize: 18,
                  marginLeft: 4,
                  color: '#FFB54A'
                }}
              />
            </p>
          </div>
          <div className={cx(styles['btnControl'], styles['desktop'])}>
            {data?.description?.showPartnerInfo && (
              <MPButton
                onClick={(e) => {
                  e.stopPropagation()
                  props.handleViewMore(data, SEARCH_LIST[1])
                }}
                className={styles['btnView']}
              >
                Xem chi tiết
              </MPButton>
            )}
            <MPButton
              onClick={(e) => {
                e.stopPropagation()
                props.handleBooking(SEARCH_LIST[1], data)
              }}
              className={cx(styles['btnBooking'])}
            >
              Đặt khám ngay
            </MPButton>
          </div>
        </div>
      </div>
      <div className={cx(styles['btnControl'], styles['mobile'])}>
        {data?.description?.showPartnerInfo && (
          <MPButton
            onClick={(e) => {
              e.stopPropagation()
              props.handleViewMore(data, SEARCH_LIST[1])
            }}
            className={styles['btnView']}
          >
            Xem chi tiết
          </MPButton>
        )}
        <MPButton
          onClick={(e) => {
            e.stopPropagation()
            props.handleBooking(SEARCH_LIST[1], data)
          }}
          className={cx(
            styles['btnBooking'],
            !data?.description?.showPartnerInfo && styles['onlyBtn']
          )}
        >
          Đặt khám ngay
        </MPButton>
      </div>
      {data?.description?.sponsored && (
        <div
          className={cx(
            styles['sponsor'],
            data?.isCashBack && styles['sponsor_cashback']
          )}
        >
          <Image
            src={imgSponsor}
            alt='Icon sponsor'
            width={50}
            height={58}
            objectFit='fill'
            layout='fixed'
          />
        </div>
      )}
      {
        <DefaultDrawer
          title={data?.popup?.title || 'Thông tin hoàn tiền'}
          open={drawerCashBack}
          onClose={onHidden}
          children={
            <div dangerouslySetInnerHTML={{ __html: data?.popup?.content }} />
          }
          height={'calc(70%)'}
          style={{ zIndex: 999999999 }}
        />
      }
    </div>
  )
}

export default HospitalItem
