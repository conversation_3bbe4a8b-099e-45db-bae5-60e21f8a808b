import { MPConfirmProfileApp } from '@medpro-libs/libs'
import * as React from 'react'
import { getError, showErrorNotification } from '../../../../utils/utils.error'
import { openNotification } from '../../../../utils/utils.notification'
import {
  fetchCountries,
  fetchDistricts,
  fetchProvinces,
  fetchWards,
  setDistricts,
  setWards
} from '../../../../store/total/slice'
import { size } from 'lodash'
import { useDispatch } from 'react-redux'
import { useEffect, useState } from 'react'
import { useAppSelector } from '../../../../store/hooks'
import { useRouter } from 'next/router'
import { selectUpdatePatientForm } from '../../../../store/patient/patientSelector'
import { useFetchNation, useFetchProfession } from '../../../../hooks/query'
import client from '../../../../config/medproSdk'
import { patientActions } from '../../../../store/patient/patientSlice'

export interface Props {}

export default function ConfirmProfileApp(props: Props) {
  const router = useRouter()
  const partnerId = router.query.partnerId as string
  const oldPatient = useAppSelector((s) => s.patient.selectedOldPatient)

  const dispatch = useDispatch()
  const countries = useAppSelector((s) => s.total.countries)
  const province = useAppSelector((s) => s.total.provinces)
  const district = useAppSelector((s) => s.total.districts)
  const ward = useAppSelector((s) => s.total.wards)

  const { data: profession, isLoading: loadingProfession } =
    useFetchProfession()
  const { data: nation, isLoading: loadingNation } = useFetchNation()

  useEffect(() => {
    cleanDistrictsAndWards()
    if (size(province) === 0) {
      dispatch(fetchProvinces({ country_code: '203' }))
    }
    if (size(countries) === 0) {
      dispatch(fetchCountries())
    }
  }, [])

  function cleanDistrictsAndWards() {
    dispatch(setDistricts([]))
    dispatch(setWards([]))
  }

  const onConfirmProfile = async () => {
    try {
      const { data } = await client.patient.addPatientIntoUser({
        secretKey: oldPatient?.secretKey
      })
      dispatch(patientActions.setSelectedPatient({ ...data, isCreated: true }))
      dispatch(patientActions.resetOldPatient())
      openNotification('success', { message: 'Cập nhật hồ sơ thành công.' })
      router.push({
        pathname: '/chon-lich-kham',
        query: {
          ...router.query,
          step: 'chon-ho-so'
        }
      })
    } catch (err) {
      openNotification('error', { message: getError(err).message })
    }
  }

  return <MPConfirmProfileApp onSubmit={onConfirmProfile} data={oldPatient} />
}
