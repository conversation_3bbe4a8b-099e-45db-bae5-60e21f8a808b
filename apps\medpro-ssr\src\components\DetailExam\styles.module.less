.container {
  @media (max-width: 1024px) {
    min-height: 50vh;
  }
}
.TabsBooking {
  :global {
    .ant-tabs-nav-wrap {
      background-color: white;
      padding: 0 16px;
    }
    .ant-tabs-nav {
      display: none;
    }
    .ant-tabs-tab-btn {
      width: 100%;
      font-size: 16px;
      font-weight: 500;
      line-height: 19.36px;
      text-align: center;
    }
  }
}
.titlePaymentCare247 {
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
  text-align: left;
  text-underline-position: 'from-font';
  text-decoration-skip-ink: none;
  color: #24313d;
  margin-bottom: 8px;
}
.itemPaymentCare247 {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
  text-align: left;
  text-underline-position: 'from-font';
  text-decoration-skip-ink: none;
  margin-bottom: 2px;
  .itemTitle {
    width: 85%;
  }
  p {
    margin-bottom: 0;
  }
}
.itemTotalPayment {
  font-weight: 600;
  margin-bottom: 8px;
}
.attentionConfirmPayment {
  text-align: center;
  border-radius: 12px;
  gap: 6px;
  display: flex;
  margin-bottom: 12px;
  .icon {
    min-width: 16px;
  }
  .content {
    font-weight: 400;
    line-height: 19px;
    color: #f5222d;
    margin-bottom: 0;
    font-size: 14px;
    font-style: italic;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
}
.buttonFooter {
  width: 100%;
  display: flex;
  text-align: center;
  align-items: center;
  gap: 8px;
  button {
    font-size: 16px;
    font-weight: 600;
    line-height: 19.36px;
    padding: 12px 10px;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    text-align: center;
    border-radius: 12px;
    border: 1px solid #11a2f3;
  }
  .buttonQuit {
    width: 40%;
    color: #11a2f3;
    background-color: #ffffff;
  }
  .buttonPayment {
    width: 60%;
    color: #ffffff;
    background-color: #11a2f3;
  }
}
.destroyTabs {
  :global {
    .ant-tabs-nav {
      display: flex !important;
    }
  }
}
.tabMedproCareDesktop {
  :global {
    .ant-tabs-nav-wrap {
      background-color: transparent !important;
      width: 100px !important;
    }
    .ant-tabs-nav-list {
      width: 280px;
    }
  }
}
.informHtml {
  width: fit-content;

  margin: auto;
  @media (max-width: 576px) {
    padding: 16px 16px 0;
  }
}
.informBooking {
  width: 600px;
  border-radius: 16px;

  background-color: white;
  @media (max-width: 576px) {
    width: 100% !important;
    min-width: 100%;
  }
}
.bannerInform {
  max-width: 600px;
  span {
    max-width: 600px;
    min-width: 600px;

    max-height: fit-content !important;
    aspect-ratio: 16/9;
    img {
      border-radius: 16px;
      min-height: fit-content !important;
      margin: 0 !important;
      max-height: fit-content !important;
    }
  }
}
.paymentInfo {
  .paymentCard {
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    column-gap: 20px;
  }
}
.button_extend {
  display: flex;
  gap: 16px;
  button {
    display: flex;
    align-items: center;
    white-space: wrap !important;
  }
  @media (max-width: 576px) {
    margin-top: 16px;
    gap: 8px;
    button {
      svg {
        width: 16px !important;
        height: 16px !important;
      }
      line-height: 12px;
      font-size: 12px;
    }
  }
}
.note {
  margin-top: 12px;
  display: flex;
  background: #ffefef;
  padding: 8px;
  border-radius: 12px;
  opacity: 0px;
  .icon {
    width: 18px;
    height: 18px;
  }
  .title {
    margin-left: 6px;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: #f5222d;
    b {
      text-decoration: underline;
    }
  }
}
.btnCloseModal {
  display: flex;
  justify-content: center;
  padding-top: 10px;
  button {
    background: linear-gradient(40deg, #00b5f1 0%, #00e0ff 100%);
    width: 50%;
    height: 45px;
    border-radius: 12px;
    color: #fff;
    border: none;
    font-size: 0.825rem;
    &:hover {
      background: linear-gradient(
        40deg,
        rgb(3, 146, 193) 0%,
        #03a0b5 100%
      ) !important;
      color: #fff;
    }
  }
}
.modalToken {
  :global {
    .ant-modal-content {
      border-radius: 16px !important;
    }
    .ant-modal-close-x {
      color: var(--prim-text, #0c1a43);
      font-weight: 600;
    }
    .ant-modal-close-x {
      display: none;
    }
    @media (max-width: 1024px) {
      .ant-result {
        padding: 0 !important;
      }
    }
  }
}
.modalComplain {
  :global {
    .ant-modal-body {
      padding: 16px !important;
    }
    .ant-modal-content {
      border-radius: 16px !important;
    }
    .ant-modal-close-x {
      color: var(--prim-text, #0c1a43);
      font-weight: 600;
    }
    .ant-modal-close-x {
      display: none;
    }
    @media (max-width: 1024px) {
      .ant-result {
        padding: 0 !important;
      }
    }
  }
}
// @media only screen and (max-width: 1160px) {
//   .paymentInfo {
//     .paymentCard {
//       :global {
//         .ant-card-body {
//           display: block !important;
//         }
//       }
//     }
//   }
// }

// @media only screen and (max-width: 576px) {
//   .paymentInfo {
//     .paymentCard {
//       flex-direction: column;
//     }
//   }
// }
.emptyList {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  text-align: center;
  font-family: 'Roboto', sans-serif !important;
  color: var(--Grey-field, #b1b1b1);
  text-align: center;
  font-size: 25px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;

  margin-top: 45px;

  span {
    width: 300px !important;
    height: 298px !important;
    position: relative;
  }
  @media (max-width: 576px) {
    font-size: 16px;
    margin-top: 16px;
    span {
      width: 200px !important;
      height: 198px !important;
    }
  }
}

.iconComplain {
  width: 80px;
  height: 80px;
  background-color: #e6f2ff;
  padding: 20px;
  border-radius: 50%;
}
.titleComplain {
  p {
    font-size: 18px;
    font-weight: 600;
    line-height: 21.78px;
    text-align: center;
    color: #11a2f3;
    margin-bottom: 8px;
  }
  span {
    color: #24313d;
    font-size: 16px;
    font-weight: 400;
    line-height: 19.36px;
    text-align: center;
  }
}
.titlePaymentSuccess {
  p {
    font-size: 18px;
    font-weight: 600;
    line-height: 21.78px;
    text-align: center;
    color: #24313d;
    margin-bottom: 8px;
  }
  span {
    color: #24313d;
    font-size: 16px;
    font-weight: 400;
    line-height: 19.36px;
    text-align: center;
  }
}
.resultComplain {
  padding: 0px;
  :global {
    .ant-result-title {
      font-size: 18px !important;
      @media (max-width: 576px) {
        font-size: 16px !important;
      }
    }
    .ant-result-icon {
      text-align: center;
      display: flex;
      justify-content: center;
      margin-bottom: 12px;
    }
    .ant-btn {
      width: 100%;
      font-size: 16px;
      height: 45px;
      border: none;
      border-radius: 12px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: center;

      border: 1px solid transparent;
      &:first-child {
        background-color: #11a2f3 !important;
        color: white;
        &:hover {
          border: 1px solid #0c669b;
        }
      }
    }
    .ant-result-extra {
      display: flex;
      margin-top: 12px;
    }
  }
}

.resultSuccessPayment {
  padding: 0px;
  :global {
    .ant-result-title {
      font-size: 18px !important;
      @media (max-width: 576px) {
        font-size: 16px !important;
      }
    }
    .ant-result-icon {
      text-align: center;
      display: flex;
      justify-content: center;
      margin-bottom: 12px;
    }
    .ant-btn {
      width: 100%;
      font-size: 16px;
      height: 45px;
      border: none;
      border-radius: 12px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: center;

      border: 1px solid transparent;
      &:first-child {
        background-color: #ffffff !important;
        border: 1px solid #11a2f3;
        color: #11a2f3;
        &:hover {
          border: 1px solid #0c669b;
        }
      }
      &:last-child {
        background-color: #11a2f3 !important;
        color: white;
        &:hover {
          border: 1px solid #0c669b;
        }
      }
    }
    .ant-result-extra {
      display: flex;
      margin-top: 12px;
    }
  }
}
.result {
  padding: 32px 48px;
  :global {
    .ant-result-title {
      font-size: 18px !important;
      @media (max-width: 576px) {
        font-size: 16px !important;
      }
    }
    .ant-btn {
      width: calc(100% / 2);
      font-size: 16px;
      height: 45px;
      border: none;
      border-radius: 12px;
      &:first-child {
        background-color: #ececec !important;
        color: #0c1a43;
        &:hover {
          background-color: rgb(212, 211, 211) !important;
        }
      }
    }
    .ant-result-extra {
      display: flex;
    }
  }
}
.ModuleNotification {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 394px !important;
  height: 184px;
  transform: translateX(-8px);
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }
  }
  .ModalHeaderNotifi {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    margin-left: 35%;

    span {
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      color: #11a2f3;
    }
  }
  .waitingMessage {
    font-size: 16px;
  }
  p {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    color: #24313d;
  }
  .waitingTime {
    color: #f5222d;
    font-size: 14px;
    margin: 10px auto;
    text-align: center;
    span {
      font-weight: bold;
    }
  }
  .btnNotifiContainer {
    display: flex;
    gap: 5px;
    .btnNotifiTel {
      width: 100%;
      height: 50px;
      padding: 12px 10px;
      border-radius: 12px;
      background: #ffffff;
      color: #11a2f3;
      font-size: 16px;
      line-height: 19px;
    }
  }
  .btnNotifi {
    width: 100%;
    height: 50px;
    padding: 12px 10px;
    border-radius: 12px;
    background: #11a2f3;
    color: white;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
  }
}
.cancelBooking {
  display: flex;
  justify-content: center;
  width: 360px;
  max-width: 360px;
  background: #ffe3e0;
  margin: auto;
  border-radius: 12px;

  :global {
    .ant-modal-header {
      background: linear-gradient(40deg, #00b5f1 0%, #00e0ff 100%) !important;
      font-family: 'Roboto' !important;
    }
    .ant-btn-primary {
      background: linear-gradient(40deg, #00b5f1 0%, #00e0ff 100%) !important;
      border: none;
      border-radius: 8px;
      &:hover {
        background: linear-gradient(
          40deg,
          rgba(7, 170, 224, 1) 0%,
          rgba(0, 198, 225, 1) 100%
        ) !important;
      }
    }
  }

  .btn {
    display: flex;
    gap: 6px;
    min-width: 360px;
    min-height: 50px;
    background-color: transparent;
    border: none;
    background: #e4e7ec;
    transition: all 0.3s ease-in-out;
    font-weight: 500;
    font-size: 16px;
    line-height: 19.36px;
    text-align: center;
    border-radius: 12px;

    color: #24313d;
    svg {
      transition: all 0.3s ease-in-out;
      color: #24313d;
      fill: #24313d;
    }
    &:hover {
      background-color: #d9d9d9 !important;
      color: #24313d !important;
      svg {
        color: #24313d !important;
        fill: #24313d !important;
      }
    }
  }
}
.refundNote {
  margin: auto;
  display: flex;
  justify-content: center;
  width: 360px;
  max-width: 360px;
  gap: 6px;
  svg {
    margin-top: 3px;
  }
  div {
    width: 321px;
  }
}

.modal {
  :global {
    .ant-modal-header {
      background: linear-gradient(40deg, #00b5f1 0%, #00e0ff 100%) !important;
      font-family: 'Roboto' !important;
    }
    .ant-btn-primary {
      background: linear-gradient(40deg, #00b5f1 0%, #00e0ff 100%) !important;
      border: none;
      border-radius: 8px;
      &:hover {
        background: linear-gradient(
          40deg,
          rgba(7, 170, 224, 1) 0%,
          rgba(0, 198, 225, 1) 100%
        ) !important;
      }
    }
  }
}
.cancelBookingMessage {
  display: flex;
  gap: 8px;
  width: 360px;
  max-width: 360px;
  margin: auto;
  margin-top: 16px !important;
  p {
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: #f5222d;
  }
}
.mobileMask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}
.modal-new {
  .description {
    font-family: 'Roboto' !important;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    margin-bottom: 8px;
    span {
      font-size: 16px;
      font-weight: 600;
      line-height: 18.75px;
    }
  }
  .notice {
    font-family: Roboto;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    margin-bottom: 0;
    span {
      color: red;
      font-weight: 600;
    }
  }
  .modal-new-footer {
    margin-top: 12px;
    .button-confirm {
      width: 100%;
      background-color: #11a2f3;
      border-radius: 12px;
      height: 50px;
      span {
        font-family: 'Roboto' !important;
        font-weight: 600;
        text-align: left;
        color: white;
      }
    }
  }
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-header {
      background-color: transparent;
      padding: 15px 0 12px;
      border-bottom: none;

      .ant-modal-title {
        font-family: 'Roboto' !important;
        color: #11a2f3;
        font-size: 18px;
        font-weight: 600;
        line-height: 21.78px;
        text-align: center;
      }
    }
    .ant-modal-body {
      padding: 0 16px 16px;
    }
  }
}
.listBooking {
  height: fit-content;
  font-weight: 500;
  border-color: transparent;
  color: #00b5f1;
  font-size: 16px;
  border-radius: 8px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  svg {
    margin-right: 6px;
  }
}

.completeProfileNote {
  margin-top: 1rem;
  display: flex;
  gap: 6px;
  justify-content: space-around;
  max-width: 360px;
  background: #ffefef;
  border-radius: 12px;
  padding: 8px;
  color: #f5222d;
  .infoIcon {
    width: 19px;
    transform: translateY(-12px);
  }
  .completeNote {
    width: 90%;
  }
  a {
    border-bottom: 1px solid #f5222d;
    font-weight: 500;
    color: #f5222d;
    font-style: italic;
    &:hover {
      color: #f5222d;
      opacity: 0.7;
    }
  }
}
.ModalNoticeModule {
  border-radius: 16px !important;
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }
  }
}
.ModalNoticeCare247 {
  border-radius: 16px !important;
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }
  }
}
.ModalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  span {
    font-size: 20px;
    font-weight: 600;
    line-height: 22px;
    color: #11a2f3;
    margin-left: 42%;
    @media (max-width: 375px) {
      margin-left: 36%;
    }
  }
  .iconCloseModule {
    width: 20px;
    height: 20px;
    // justify-content: flex-end;
  }
}
.carousel {
  width: 100%;
}
.TagExam {
  text-align: center;
  font-size: 18px;
}
.bufferZone {
  margin-bottom: 16px;
}
.btnPrev,
.btnNext {
  @media (max-width: 576px) {
    width: 28px !important;
    height: 28px !important;
    svg {
      width: 18px !important;
      height: 18px !important;
    }
  }
  position: absolute;
  top: 24px !important;
  transform: translateY(-50%);
  width: 35px !important;
  height: 35px !important;
  border-radius: 50%;
  background: #fff !important;
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.04),
    0px 2px 6px 0px rgba(0, 0, 0, 0.04), 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(5px);
  display: flex !important;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  &:hover {
    background: #ffffff;
  }
}
.btnNext {
  right: 26px !important;
}

.btnPrev {
  left: 26px !important;
}
.tabCare247 {
  display: flex;
  align-items: start;
  svg {
    margin-left: 4px;
  }
  &:hover {
    cursor: pointer;
  }
}
.ModalShare {
  :global {
    .ant-modal-header {
      background: #e6f2ff !important;
      text-align: center !important;

      border-radius: 12px 12px 0 0;
      padding: 8px 12px;
      @media (max-width: 576px) {
        padding: 12px 16px;
      }
    }
    .ant-modal-title {
      color: #11a2f3;
      font-weight: 500;
      font-size: 20px;
      line-height: normal;
      letter-spacing: 0%;
      @media (max-width: 576px) {
        font-size: 18px;
      }
    }
    .ant-modal-close-x {
      width: 47px;
      height: 47px;
      svg {
        fill: #24313d;
      }
    }
    .ant-modal-content {
      border-radius: 12px;
    }
    .ant-modal-body {
      padding: 16px 12px 0;
      @media (max-width: 576px) {
        padding: 16px;
      }
    }
  }
  .card {
    .description {
      text-align: center;
      font-size: 0.895rem;
      font-style: italic;
    }

    .subDescription {
      text-align: center;
      font-family: Inter;
      font-weight: 600;
      font-size: 16px;
      line-height: normal;
      letter-spacing: 0%;
      @media (max-width: 992px) {
        font-size: 14px;
      }
    }

    .groupInputCopy {
      display: flex;
      height: fit-content;
      :global {
        .ant-input-disabled {
          background-color: white;
        }
      }
      .formItemCustom {
        width: 100%;
        @media (max-width: 992px) {
          margin-bottom: 16px;
        }
      }
      input {
        height: 50px;
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
        color: #12263f;
        padding: 8px 0 8px 12px;
        font-size: 16px;
        line-height: normal;
        letter-spacing: 0%;
        @media (max-width: 992px) {
          height: 45.5px;
          font-size: 14px;
        }
      }

      button {
        width: 135px;
        height: 50px;
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;
        border: none;
        background: #11a2f3 !important;
        font-weight: 500;
        font-size: 16px;
        line-height: normal;
        @media (max-width: 992px) {
          width: 110px;
          height: 45.5px;
          font-size: 14px;
        }
      }
    }
  }
}
.adsBanner {
  margin: 0 auto 20px;
  max-width: 360px;
  border-radius: 16px;
  .carouselBanner {
    width: 100%;
    :global {
      .slick-dots {
        max-width: 360px;
        bottom: -10px;
        margin: 0 !important;
        button {
          &::before {
            display: none;
          }
        }
        .slick-active {
          background-color: #12263f;
        }
      }
    }
  }
  .adsBannerItem {
    width: 100%;
    height: 180px;
    border-radius: 16px;
    overflow: hidden;
  }
}
