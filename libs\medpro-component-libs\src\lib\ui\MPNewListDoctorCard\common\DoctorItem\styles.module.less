.cardDoctor_select {
  border: 1px solid #00b5f1 !important;
}
.tagCashBack {
  position: absolute;
  top: 13px;
  right: -62px;
  width: 190.81px;
  height: 25.86px;
  border: none;
  transform: rotate(35deg);
  background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
  text-transform: uppercase;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 12px;
  font-weight: 700;
  line-height: 17.58px;
  @media (max-width: 576px) {
    width: 107.53px;
    height: 20.77px;
    top: 10px;
    right: -25px;
    font-size: 10px;
    line-height: 11.72px;
  }
  &:hover {
    cursor: pointer;
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    color: white;
  }
  &:focus {
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    color: white;
  }
}
.popoverCashBack {
  :global {
    .ant-popover-content {
      max-width: 510px;
    }
    .ant-popover-inner {
      border-radius: 12px !important;
      backdrop-filter: blur(15px);
      background-color: rgba(255, 255, 255, 0.9);
    }
    .ant-popover-inner-content {
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
}
.cardDoctor {
  background-color: #ffffff;
  border-radius: 12px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  // box-shadow: 4px 8px 30px 0px rgba(177, 196, 218, 0.35);
  border: 1px solid #d7dbe0;
  &:hover {
    border: 1px solid #00b5f1;
    // cursor: pointer;
  }
  .frame {
    display: flex;
    align-items: center;
    background-color: #eff6ff;

    justify-content: space-between;
    padding: 12px 14px;
    width: 100%;
    border-radius: 0 0 12px 12px;
    .bottomLeft {
      align-items: flex-start;
      display: flex;
      flex: 0 0 auto;
      gap: 4px;
      width: 70%;
    }
    .bottomRight {
      width: fit-content;
      display: flex;
      justify-content: center;
      .btnBooking {
        width: 150px !important;
        display: flex;
        padding: 10px 30px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        height: 36px;
        align-self: stretch;
        color: #fff;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        border-radius: 30px;
        background: var(--blue, #11a2f3);
        @media only screen and (max-width: 992px) {
          font-size: 13px;
        }
        @media only screen and (max-width: 576px) {
          width: 87px !important;
        }
        background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
        border: 1px solid transparent;
        color: white;
        // @media (max-width: 992px) {
        //   // padding: 0;
        //   background-clip: text;
        //   -webkit-background-clip: text;
        //   -webkit-text-fill-color: transparent;
        //   box-shadow: none;
        // }
        span {
          font-family: 'Roboto', sans-serif !important;
        }
        &:active,
        &:focus {
          @media only screen and (max-width: 992px) {
            // border: none;
            background: none !important;
            -webkit-text-fill-color: #00b5f1;
          }
        }
        &:hover {
          @media (min-width: 578px) {
            box-shadow: 0px 4px 30px 0px rgba(116, 157, 206, 0.2);
            -webkit-text-fill-color: white;
            background: linear-gradient(83.63deg, #07aae0, #00c6e1) !important;
          }
        }
      }
    }
  }
  .rating {
    width: fit-content;
    text-align: center;
    margin-top: 8.5px;
    display: flex;
    gap: 8px;
    span {
      color: #11a2f3;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }

    .rate {
      display: flex;
      gap: 2px;
      p {
        margin-top: -2px;
        margin-bottom: 0;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .totalRate {
      display: flex;
      gap: 4px;
      p {
        margin-top: -4px;
        margin-bottom: 0;

        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .linear-location {
    height: 21px !important;
    width: 21px !important;
    @media (max-width: 576px) {
      height: 16px !important;
      width: 16px !important;
    }
  }
  .groupAddress {
    align-items: flex-start;
    display: flex;
    flex: 1;
    flex-direction: column;
    flex-grow: 1;
    gap: 2px;
  }
  .hospital {
    align-self: stretch;
    color: #003553;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: normal;
    margin-top: -1px;
    margin-bottom: 0px;
    word-wrap: break-word;
    font-family: 'Roboto', sans-serif !important;
    svg {
      vertical-align: top;
    }
    @media (max-width: 576px) {
      font-size: 16px;
    }
  }
  .leftGroup {
    gap: 12px;
    display: flex;
    padding: 14px;
    .logoImg {
      width: 120px;
      height: 120px;
      min-width: 120px;
      position: relative;
      background: #eaeaea;
      border-radius: 12px;
      @media (max-width: 576px) {
        width: 80px !important;
        height: 80px !important;
        min-width: 80px;
      }
      .Avatar {
        background-position: top;
        background-size: cover;
        width: 120px;
        height: 120px;
        border-radius: 12px;
        @media (max-width: 576px) {
          width: 80px !important;
          height: 80px !important;
        }
      }
      .detailDoctor {
        display: flex;
        width: 120px;
        height: 27px;
        justify-content: center;
        align-items: center;
        border-radius: 0px 0px 8px 8px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        border: none;
        position: absolute;
        bottom: 0;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        font-size: 14px;
        @media (max-width: 576px) {
          width: 80px;
          height: 20px;
          font-size: 10px;
        }
        &:hover {
          cursor: pointer;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
        }
      }
    }
  }
  .address {
    color: var(--grey-text, #858585);
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    word-wrap: break-word !important;
    font-family: 'Roboto', sans-serif !important;
    margin-bottom: 0;
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
  .rectangle {
    height: 118px;
    position: relative;
    width: 118px;
  }
  .groupInfo {
    // margin-top: 14px;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    @media (max-width: 768px) {
      margin-top: 0px;
    }
    h3 {
      width: 92%;
      color: #11a2f3;
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 21.09px;
      margin-bottom: 8px;
      font-family: 'Roboto', sans-serif !important;
      @media (max-width: 576px) {
        font-size: 18px;
      }
      strong {
        font-weight: 500;
        font-family: 'Roboto', sans-serif !important;
      }
    }

    .treatment {
      overflow: hidden;
      color: var(--text-web, #003553);
      text-overflow: ellipsis;
      font-size: 16px;
      font-style: normal;
      // font-weight: 500;
      font-family: 'Roboto', sans-serif !important;
      line-height: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      margin-bottom: 4px;
    }
  }
  .info {
    align-items: flex-start;
    display: inline-flex;
    flex: 0 0 auto;
    gap: 8px;
  }
  .infoDegree {
    color: var(--primary-body-text, #003553);
    font-size: 25px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    position: relative;
    width: fit-content;
    width: 100%;
    margin-bottom: 0;
    @media (max-width: 768px) {
      font-size: 18px;
    }
    strong {
      font-size: 25px;
      font-weight: 700;
      width: fit-content;
      @media (max-width: 768px) {
        font-size: 18px;
      }
    }
  }
  .infoPosition {
    color: var(--grey-text, #858585);
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 8px;
    @media (max-width: 768px) {
      font-size: 16px;
      margin-bottom: 4px;
    }
  }
  .evaluate {
    position: relative;
    @media (max-width: 576px) {
      display: none;
    }
  }
  .evaluateItem {
    align-items: flex-end;
    display: inline-flex;
    flex: 0 0 auto;
    flex-direction: column;
    height: 35px;
  }
  .evaluateLevel {
    color: #003553;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    margin-top: -1px;
    white-space: nowrap;
  }
  .evaluateCount {
    color: var(--grey-text, #858585);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    white-space: nowrap;
    width: fit-content;
  }
  .start {
    border-radius: 8px;
    border: 1px solid #00b5f1;
    background: #e6f2ff;
    display: flex;
    padding: 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }

  .img {
    flex: 0 0 auto;
  }
}

.groupButton {
  text-align: right;
  @media (max-width: 576px) {
    text-align: center;
    display: flex !important;
    width: 100%;
    gap: 8px;
  }
  .btnView {
    color: #00b5f1;
    font-size: 16px !important;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    width: 130px;
    padding: 10px 30px !important;

    height: 40px;
    gap: 10px;
    align-self: stretch;
    border-radius: 30px;
    border: 1px solid var(--primary-gradient-title, #00b5f1);
    @media (max-width: 576px) {
      width: 50%;
    }
    &:active,
    &:focus {
      @media only screen and (max-width: 992px) {
        border: none;
      }
    }
    &:hover {
      // background: linear-gradient(83.63deg, #d5f3fd, #f0f2f2) !important;
    }
  }
  .disable {
    color: rgba(0, 0, 0, 0.25);
    border: 1px solid #d9d9d9;
    background-color: #f5f5f5 !important;
  }
  .button {
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    width: 130px;
    padding: 10px 30px;
    gap: 10px;
    height: 40px;
    align-self: stretch;
    border-radius: 30px;
    background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
    border: 1px solid var(--primary-gradient-title, #00b5f1);
    margin-top: 8px;
    @media (max-width: 576px) {
      margin-top: 0px;
      width: 50%;
    }
    &:hover {
      cursor: pointer;
      background: linear-gradient(23.63deg, #07aae0, #00c6e1 113.91%);
      color: #fff;
    }
    // @media (max-width: 576px) {
    //   min-width: inherit !important;
    //   position: relative;
    //   width: inherit !important;
    //   font-size: 13px;
    //   padding: 10px;
    // }
  }
}

.btnControl {
  display: flex;
  gap: 12px;
  margin-top: 8px;
  @media only screen and (max-width: 576px) {
    display: none;
  }

  button {
    // border: none;
    padding: 10px 16px;
    width: 145px;
    transition: all 0.3s ease;
    border-radius: 30px;
    font-weight: 500;
    font-size: 16px;
    line-height: normal;
    @media only screen and (max-width: 992px) {
      margin-bottom: 5px;
      font-size: 13px;
    }
  }
  .btnView {
    color: #00b5f1;
    border: 1px solid #00b5f1;
    background-color: transparent !important;
    @media only screen and (max-width: 992px) {
      box-shadow: none;
    }
    &:active,
    &:focus {
      @media only screen and (max-width: 992px) {
        border: none;
      }
    }
    // &:hover {
    //   background: #00e0ff !important;
    //   color: white;
    // }
  }
  .onlyBtn {
    @media (max-width: 576px) {
      width: 100% !important;
    }
  }
}
.specialist {
  display: flex;
  margin-top: 8px;
  @media (max-width: 768px) {
    margin-top: 6px;
  }
  .specialistItem {
    height: fit-content;
    color: #11a2f3;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 6px 12px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 16px;
    background: #e6f2ff;
    margin-right: 8px;
    span {
      @media (max-width: 420px) {
        display: none;
      }
    }
  }
}

.groupEvaluate {
  padding: 14px 14px 0 0;
  justify-content: right;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 8px;
  width: 100%;
}
.countStart {
  padding-right: 14px;
  text-align: right;
}
.modal {
  border-radius: 16px !important;
  :global {
    .ant-modal-header {
      background-color: #fff !important;
      border-radius: 16px;
      border: none;
      padding: 16px 24px 0 24px;
    }
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-footer {
      padding: 0 16px 16px 16px !important;
      border-top: none !important;
      .ant-btn-default {
        display: none !important;
      }
      .ant-btn {
        border-radius: 12px;
      }
    }
    .ant-modal-body {
      color: var(--primary-body-text, #003553);
      font-family: 'Roboto' !important;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .ant-modal-close-x {
      svg {
        fill: #000;
      }
    }
    .ant-modal-title {
      text-align: center;
      font-family: 'Roboto' !important;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      background: var(
        --primary-gradient-title,
        linear-gradient(84deg, #00b5f1 33.34%, #00e0ff 113.91%)
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
