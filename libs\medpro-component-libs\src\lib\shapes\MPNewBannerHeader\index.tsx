import React from 'react'
import <PERSON><PERSON>ewBannerHeaderComponent from '../../component/MPNewBannerHeaderComponent'
import MPNewBannerHeaderCard from '../../ui/MPNewBannerHeaderCard'

interface IF {
  data: any
  onSearchDebounce: (event: any) => Promise<void>
  handleBookingSearch: ({ type, item }: any) => Promise<void>
  searchData: any
  searching: boolean
}

export const MPNewBannerHeader = (props: IF) => {
  return (
    <MPNewBannerHeaderComponent
      {...props}
      renderItem={() => {
        return <MPNewBannerHeaderCard {...props} />
      }}
    />
  )
}

export default MPNewBannerHeader
