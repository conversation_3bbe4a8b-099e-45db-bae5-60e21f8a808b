import { Col, Row } from 'antd'
import MPSearch from '../../shapes/MPSearchTyping'
import styles from './styles.module.less'

export interface NewBannerHeaderProps {
  data: any
  onSearchDebounce: (item: any) => Promise<void>
  searchData: any
  handleBookingSearch: ({ type, item }: any) => Promise<void>
  searching: boolean
}

const MPNewBannerHeaderCard = (props: NewBannerHeaderProps) => {
  const title = 'Kết nối Người Dân với \n Cơ sở & Dịch vụ Y tế hàng đầu'
  return (
    <div className={styles['bannerHeader']} aria-labelledby='Ảnh nền Banner'>
      <Row className={styles['container']}>
        <Col span={24} lg={24} className={styles['contentLeft']}>
          <div className={styles['wrapper']}>
            <h1 className={styles['title']}>{title}</h1>
            <MPSearch
              onSearchDebounce={props.onSearchDebounce}
              searchData={props.searchData}
              searching={props.searching}
              handleBookingSearch={props.handleBookingSearch}
            />
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default MPNewBannerHeaderCard
