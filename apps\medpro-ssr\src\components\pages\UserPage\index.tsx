import { <PERSON><PERSON><PERSON><PERSON> } from '@medpro-libs/libs'
import { Badge, Col, Row, Tabs } from 'antd'
import TabPane from 'antd/lib/tabs/TabPane'

import cx from 'classnames'
// import MPAllBookingOfUser from 'libs/medpro-component-libs/src/lib/shapes/MPAllBookingOfUser'
import { useEffect, useState } from 'react'
// import Notification from '../Notifications'
import { FaUserPlus } from 'react-icons/fa'
import BookingOfUser from '../../shapes/BookingOfUser'
import ListPatient from '../../shapes/ListPatient'
import Notification from '../../shapes/Notifications'
import styles from './styles.module.less'

interface TabsUserProps {
  device: any
  activeItem: string
  patients: any
  bookingAllUser: any
  notiOfUser: any
  groupNotiOfUser: any
  keyTab: any
  isMobile: boolean
  unreadNoti: number
  data: {
    key: string
    name: string
    mobileName?: string
    icon?: any
  }[]
  handleTabs: (key: any) => void
  onCreatePatient: () => void
  changeBreadCrumb: (title: string) => void
}
const UserPage = ({
  device,
  activeItem,
  patients,
  bookingAllUser,
  keyTab,
  data,
  notiOfUser,
  groupNotiOfUser,
  unreadNoti,
  isMobile,
  handleTabs,
  onCreatePatient,
  changeBreadCrumb
}: TabsUserProps) => {
  const activeKey = activeItem
  const renderUnreadNotifications = (item: any) => {
    if (item.key === 'notifications') {
      return <Badge style={{ boxShadow: 'none' }} count={unreadNoti} />
    }
  }
  const [title, setTitle] = useState('Hồ sơ bệnh nhân')
  const [showDropdown, setShowDropdown] = useState(false)
  const [selectedKey, setSelectedKey] = useState('records')

  useEffect(() => {
    const currentData = data.filter((item) => activeKey === item.key)
    setTitle(currentData[0]?.name)
    setSelectedKey(activeKey)
  }, [activeKey])

  const handleDropdown = () => {
    setShowDropdown(!showDropdown)
    // setSelectedKey(activeKey)
  }

  function handleDropdownItemClick(key): void {
    setSelectedKey(key)
    setShowDropdown(false)
    const selectedItem = data.find((item) => item.key === key)
    setTitle(selectedItem.name)
    handleTabs(key)
  }

  return (
    <div>
      <MPContainer tag='section' className={styles['ThongTinHoSoCustom']}>
        <Row className={styles['rowInformation']}>
          <Col span={24}>
            <Tabs
              activeKey={activeKey}
              defaultActiveKey={activeKey}
              tabPosition={'left'}
              className={styles['tabUser']}
            >
              <TabPane
                key=''
                tab={
                  <div className={styles['header']}>
                    <div
                      className={styles['btnCreate']}
                      onClick={onCreatePatient}
                    >
                      <FaUserPlus className={styles['iconAdd']} />
                      Thêm hồ sơ
                    </div>
                  </div>
                }
              ></TabPane>
              {data?.map((item: any) => (
                <TabPane
                  key={item.key}
                  tab={
                    <p
                      className={cx(
                        styles['tabsItem'],
                        activeKey === item.key && styles['active']
                      )}
                      onClick={() => handleTabs(item.key)}
                    >
                      <div>
                        {item.icon}
                        <span className={styles['textTabs']}>
                          {device.platform === 'pc'
                            ? item.name
                            : item.mobileName}{' '}
                          {renderUnreadNotifications(item)}
                        </span>
                      </div>
                    </p>
                  }
                >
                  {activeKey === 'records' && isMobile && (
                    <div className={styles['header']}>
                      <div
                        className={styles['btnCreate']}
                        onClick={onCreatePatient}
                      >
                        <FaUserPlus className={styles['iconAdd']} />
                        Thêm hồ sơ
                      </div>
                    </div>
                  )}
                  <div className={styles['listPatient']}>
                    {item.key === keyTab.records && (
                      <ListPatient patients={patients} />
                    )}
                    {item.key === keyTab.bills && (
                      <BookingOfUser bookingAllUser={bookingAllUser} />
                    )}
                    {item.key === keyTab.notifications && (
                      <Notification
                        notiOfUser={notiOfUser}
                        groupNotiOfUser={groupNotiOfUser}
                      />
                    )}
                  </div>
                </TabPane>
              ))}
            </Tabs>
          </Col>
        </Row>
      </MPContainer>
    </div>
  )
}

export default UserPage
