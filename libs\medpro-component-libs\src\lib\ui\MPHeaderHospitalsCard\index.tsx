import { Input } from 'antd'
import cx from 'classnames'
import { size } from 'lodash'
import { useState } from 'react'
import { CiSearch } from 'react-icons/ci'
// import { HiOutlineLocationMarker } from 'react-icons/hi'
// import { useWindowResize } from '../../common/func'
// import MPButton from '../MPButton'
import DrawerProvince from './common/DrawerProvince'
// import SvgFilter from './common/images/filter'
import styles from './styles.module.less'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import { useRouter } from 'next/router'

// const { Option } = Select

export interface HeaderHospitalsProps {
  province?: any[]
  title: string
  titleSearch?: string
  subTitle?: any
  isHidden?: boolean
  handleSearch: (values: any) => void
  MPFillterDoctorCard?: any
  onSearch?: (kw: any) => void
  hospital?: string
  isHiddenFilter?: boolean
  keySearch?: any
  dataAddress?: any
}

const MPHeaderHospitalsCard = (props: HeaderHospitalsProps) => {
  const {
    title,
    province = [],
    titleSearch,
    hospital,
    handleSearch,
    onSearch,
    isHiddenFilter = false
  } = props
  // const isMobile = useWindowResize(577)
  const router= useRouter()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [dataFilter, setDataFilter] = useState([])
  const kw= router.query.kw as string
  return (
    <div className={styles['hospitals']}>
      <div
        className={cx(styles['header'], props.isHidden ? styles['hidden'] : '')}
        // , props.isHidden ? styles['hidden'] : ''
      >
        <h1 className={styles['title']}>{title}</h1>
        {props.subTitle && (
          <div className={styles['sub_title']}>{props.subTitle}</div>
        )}
        {props.dataAddress?.name && (
          <>
            <h4 className={styles['partner_title']}>
              {props.dataAddress?.name}
            </h4>
            <h5 className={styles['partner_address']}>
              <HiOutlineLocationMarker className={styles['linear-location']} />
              {props.dataAddress?.address}
            </h5>
          </>
        )}
      </div>
      <div className={styles['body']}>
        <div
          className={cx(styles.form, props.isHidden && styles['stickyFilter'])}
        >
          <div
            className={cx(
              styles['formContent'],
              isHiddenFilter && styles['isHiddenFilter']
            )}
          >
            <div
              className={cx(
                styles['item'],
                size(province) === 0 && styles.unProvince
              )}
            >
              <div className={styles['icon']}>
                <CiSearch size={20} color='#B1B1B1' />
              </div>
              <div className={styles['inputItem']}>
                <div className={cx(styles['formItem'], styles['inputAntd'])}>
                  <Input
                    type='text'
                    value={hospital}
                    className={styles['selectItem']}
                    placeholder={titleSearch || 'Tìm kiếm cơ sở y tế...'}
                    onChange={onSearch}
                    allowClear={true}
                    style={{ border: 'none' }}
                    defaultValue={kw}
                  />
                </div>
              </div>
            </div>
            {/* {!isHiddenFilter &&
              (isMobile ? (
                <MPButton
                  onClick={() => setIsModalOpen(true)}
                  className={cx(
                    styles.button,
                    size(dataFilter) > 0 && styles.activite
                  )}
                >
                  {size(dataFilter) > 0 ? (
                    <SvgFilter fill='#00b5f1' />
                  ) : (
                    <SvgFilter />
                  )}
                  Lọc
                </MPButton>
              ) : (
                size(province) > 0 && (
                  <div className={styles.item}>
                    <div className={styles.icon}>
                      <HiOutlineLocationMarker size={20} />
                    </div>
                    <div className={styles.inputItem}>
                      <div className={styles.formItem}>
                        <Select
                          onChange={(v) => {
                            handleSearch({ province: v })
                          }}
                          className={styles.selectItem}
                          placeholder='Tất cả địa điểm'
                          showSearch
                          allowClear
                          filterOption={(input, option) =>
                            getReplaceUTF8(
                              (
                                option?.children as unknown as string
                              ).toLowerCase()
                            ).includes(getReplaceUTF8(input.toLowerCase()))
                          }
                          dropdownMatchSelectWidth={false}
                        >
                          {province?.map((item, index) => (
                            <Option key={index} value={item.id}>
                              {item.name}
                            </Option>
                          ))}
                        </Select>
                      </div>
                    </div>
                  </div>
                )
              ))} */}
            {isModalOpen && (
              <DrawerProvince
                open={isModalOpen}
                setOpen={setIsModalOpen}
                dataFilter={dataFilter}
                setDataFilter={setDataFilter}
                listFilter={province}
                handleFillterTabs={handleSearch}
              />
            )}
          </div>
        </div>
        {/*</Form>*/}
        {props.MPFillterDoctorCard && <div>{props.MPFillterDoctorCard}</div>}
      </div>
    </div>
  )
}

export default MPHeaderHospitalsCard
