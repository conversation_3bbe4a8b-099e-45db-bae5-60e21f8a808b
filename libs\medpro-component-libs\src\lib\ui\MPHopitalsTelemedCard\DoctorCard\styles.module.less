.pagination {
  justify-content: center;
  display: flex;
  margin: 0 auto;
  @media (max-width: 415px) {
    display: block;
    text-align: center;
    .button {
      margin-top: 12px;
    }
  }
  @media screen and (max-width: 992px) {
    margin-top: 0px;
    margin-bottom: 40px;
  }
  .button {
    height: 32px !important;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid transparent;
    box-shadow: 0px 3px 15px rgba(116, 157, 206, 0.2);
    transition: all 0.2s ease;
    color: #00b5f1;
    margin-left: 8px;
  }
  :global {
    .ant-pagination-item {
      line-height: 32px;
    }
    .ant-pagination-item,
    .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination-next .ant-pagination-item-link {
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid transparent;
      box-shadow: 0px 3px 15px rgba(116, 157, 206, 0.2);
      transition: all 0.2s ease;
      color: #00b5f1;
      &:hover {
        border: 1px solid #00b5f1;
        box-shadow: 0px 4px 15px rgba(116, 157, 206, 0.5);
      }
      a {
        color: #00b5f1;
        font-weight: 400;
        font-size: 16px;
      }
    }
    .ant-pagination-item-active {
      background: linear-gradient(98.96deg, #00b5f1 40.75%, #00e0ff 129.57%);
      border-radius: 4px;
      border: 1px solid transparent;
      a {
        color: #ffffff;
        font-weight: 400;
        font-size: 16px;
      }
    }

    .ant-pagination-disabled .ant-pagination-item-link {
      background: #ffffff;
      border-radius: 4px;
      border: none;
      box-shadow: 0px 3px 15px rgba(116, 157, 206, 0.2);
      color: #b1b1b1;
      &:hover {
        box-shadow: 0px 3px 15px rgba(116, 157, 206, 0.2);
      }
    }

    .ant-pagination-item {
      @media only screen and (max-width: 400px) {
        min-width: 25px;
        height: 25px;
        line-height: 25px;
      }
    }
    .ant-pagination-prev,
    .ant-pagination-next {
      @media only screen and (max-width: 400px) {
        min-width: 25px;
        height: 25px;
        line-height: 25px;
      }
    }
  }
}

.content {
  width: 100%;
  height: 100%;
  padding: 20px;
  border-radius: 16px;
  background: #ffffff;
  margin: 0px auto;
  border: 1px solid white;
  min-height: 140px;
  padding: 12px !important;

  .content_title {
    gap: 20px;
    display: flex;
    width: 100%;
    height: 100%;
    @media (max-width: 576px) {
      gap: 12px;
    }
    position: relative;
    .images {
      width: 120px;
      height: 120px;
      min-width: 120px;
      min-height: 120px;
      text-align: left;
      position: relative;
      border-radius: 16px !important;
      @media (max-width: 576px) {
        display: none;
      }
      .btnView {
        display: flex;
        width: 120px;
        height: 27px;
        padding: 10px 30px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 0px 0px 8px 8px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        border: none !important;
        position: absolute;
        bottom: 0;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        font-size: 14px;
        @media (max-width: 576px) {
          width: 80px;
          height: 20px;
          font-size: 10px;
        }
        &:hover {
          cursor: pointer;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
        }
      }
      .rating {
        width: 90%;
        border-radius: 8px;
        border: 0.5px solid var(--primary-gradient-title, #00b5f1);
        background: var(--Light-blue, #e6f2ff);
        position: absolute;
        bottom: -40px;
        left: 5%;
        display: flex;
        justify-content: space-between;
        // padding: 8px;
        text-align: center;
        height: 34px;
        gap: 8px;
        span {
          color: var(--text-blue, #11a2f3);
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          display: flex;
          align-items: center;
        }
        .rate {
          display: flex;
          gap: 2px;
          width: 100%;
          justify-content: center;
          p {
            margin-bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .totalRate {
          display: flex;
          gap: 2px;
          width: 100%;
          justify-content: center;
          p {
            margin-top: -2px;
            margin-bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .totalRate::before {
          content: '';
          position: absolute;
          top: 22%;
          left: 50%;
          width: 0.8px;
          height: 18px;
          background: #11a2f3;
        }
      }
    }

    .content_title_int {
      position: relative;
      height: 100%;
      width: 100%;
      @media (max-width: 576px) {
        height: fit-content;
      }
      .groupTitle {
        display: flex;
        gap: 12px;
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 1px solid #dedede;
        .img {
          display: none !important;
          @media (max-width: 576px) {
            display: flex !important;
            width: 60px;
            height: 60px;
            min-width: 60px;
            min-height: 60px;
            text-align: left;
            position: relative;
            border-radius: 16px !important;
            .btnViewMobile {
              display: flex;
              width: 50px !important;
              height: 27px;
              padding: 10px 30px;
              justify-content: center;
              align-items: center;
              gap: 10px;
              border-radius: 0px 0px 7px 7px;
              background: rgba(255, 255, 255, 0.8);
              backdrop-filter: blur(15px);
              border: none !important;
              position: absolute;
              bottom: 0;
              font-size: 14px;
              @media (max-width: 576px) {
                width: 50px;
                height: 20px;
                font-size: 10px;
              }
              &:hover {
                cursor: pointer;
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
              }
            }
          }
        }

        .title {
          width: 100%;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-bottom: 0px;
          background: var(
            --primary-gradient-title,
            linear-gradient(84deg, #00b5f1 33.34%, #00e0ff 113.91%)
          );
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          &::selection {
            background-color: linear-gradient(22deg, #00b5f1 0%, #00e0ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: white;
          }
          strong {
            font-weight: 700;
            &::selection {
              background-color: linear-gradient(
                22deg,
                #00b5f1 0%,
                #00e0ff 100%
              );
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: white;
            }
          }
        }
      }

      .time {
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
        color: #003553;
        display: flex;
        align-items: left;
        justify-content: left;
        gap: 8px;
        margin-bottom: 8px;
        span {
          min-height: 15px;
          min-width: 15px;
        }
        .address {
          text-align: left;
          @media (max-width: 576px) {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          p {
            color: var(--primary-body-text, #003553);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
        }
        @media only screen and (max-width: 500px) {
          font-size: 12px;
          line-height: 16px;
        }
      }
      .treatment {
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
        color: #003553;
        display: flex;
        align-items: left;
        justify-content: left;
        gap: 8px;
        margin-bottom: 8px;
        width: 100%;
        span {
          min-height: 15px;
          min-width: 15px;
        }
        .treatmentDesc {
          text-align: left;
          position: relative;
          display: flex;
          .treatmentContent {
            // @media (max-width: 576px) {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            // }
            color: var(--primary-body-text, #003553);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
          .setContent {
            width: 93%;
          }
          .arrow {
            background-color: #f1fcff;
            text-align: center;
            border: 1px solid #00e0ff;
            border-radius: 4px;
            padding: 0 4px;
            position: absolute;
            bottom: -4px;
            right: 0;
          }
        }
        @media only screen and (max-width: 500px) {
          font-size: 12px;
          line-height: 16px;
        }
      }
      p {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 0;
        @media (max-width: 520px) {
          font-weight: 500;
        }
      }
      // height: 46px;

      //styleName: Body/h4: Bold;
      font-family: 'Roboto';
      font-size: 20px;
      font-weight: 700;
      line-height: 23px;
      letter-spacing: 0em;
      text-align: left;
      @media (max-width: 480px) {
        font-size: 16px;
        width: 100%;
        height: 100%;
      }
    }
  }
  &:hover {
    box-shadow: none;
    border: 1px solid #00b5f1;
    cursor: pointer;
  }
}
.modal {
  border-radius: 16px !important;
  :global {
    .ant-modal-header {
      background-color: #fff !important;
      border-radius: 16px;
      border: none;
      padding: 16px 24px 0 24px;
    }
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-body {
      color: var(--primary-body-text, #003553);
      font-family: 'Roboto' !important;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .ant-modal-close-x {
      svg {
        fill: #000;
      }
    }
    .ant-modal-footer{
      padding: 0 16px 16px 16px !important;
      border-top: none !important;
      .ant-btn-default{
        display: none !important;
      }
      .ant-btn{
        border-radius: 12px;
      }
    }
    .ant-modal-title {
      text-align: center;
      font-family: 'Roboto' !important;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      background: var(
        --primary-gradient-title,
        linear-gradient(84deg, #00b5f1 33.34%, #00e0ff 113.91%)
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
.btnControl {
  display: flex;
  width: 100%;
  gap: 12px;
  height: 42px;
  position: absolute;
  bottom: 0;
  @media (max-width: 992px) {
    flex-direction: column-reverse;
  }
  @media only screen and (max-width: 992px) {
    display: flex;
    flex-direction: row;
  }
  @media (max-width: 576px) {
    gap: 8px;
  }
  button {
    width: 217px;
    height: 35px;
    border: none;
    padding: 10px 16px;
    transition: all 0.3s ease;
    border-radius: 30px;
    font-weight: 500;
    font-size: 16px;
    line-height: normal;

    @media (max-width: 576px) {
      width: 100%;
      height: 40px;
    }
  }
  .btnBooking {
    background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
    color: white;
    // position: absolute;
    // bottom: 0;
    &:active,
    &:focus {
      @media only screen and (max-width: 992px) {
        border: none;
        background: none !important;
        -webkit-text-fill-color: #00b5f1;
      }
    }
    &:hover {
      box-shadow: 0px 4px 30px 0px rgba(116, 157, 206, 0.2);
      color: #fff;
      background: linear-gradient(83.63deg, #07aae0, #239ecd) !important;
    }
  }
  .rating {
    width: 106px;
    border-radius: 8px;
    border: 0.5px solid var(--primary-gradient-title, #00b5f1);
    background: var(--Light-blue, #e6f2ff);
    justify-content: space-between;
    padding: 8px;
    text-align: center;
    height: 40px;
    min-width: 106px;
    display: none;
    gap: 8px;
    @media (max-width: 576px) {
      display: flex;
    }
    span {
      color: var(--text-blue, #11a2f3);
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      display: flex;
      align-items: center;
    }
    .rate {
      display: flex;
      gap: 2px;
      width: 100%;
      justify-content: center;
      p {
        margin-bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .totalRate {
      display: flex;
      gap: 2px;
      width: 100%;
      justify-content: center;
      p {
        margin-top: -2px;
        margin-bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .totalRate::before {
      content: '';
      position: absolute;
      top: 24%;
      left: 52px;
      // transform: translateX(-50%);
      width: 0.8px;
      height: 18px;
      background: #11a2f3;
    }
  }
}
.desktop {
  height: 42px;
}
