.cardBody {
  padding: 0px 20px 20px 20px;
  height: fit-content !important;
  @media only screen and (max-width: 991px) {
    border: none;
    padding: 0;
    // padding-bottom: 20px;
    // border-bottom: 3px solid #d4d5d8;
  }
  .groupButton {
    width: 100%;
    text-align: left;
    display: flex;
    justify-content: right;
    align-items: left;
    gap: 12px;
    height: 100%;
  }
  .chooseDetailButton {
    width: 100%;
    display: flex;
    height: 40px;
    padding: 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex: 1 0 0;
    border-radius: 8px;
    background: #ecf5ff;
    color: var(--text-blue, #11a2f3);
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    border: none;
    border: 1px solid transparent;
    @media (max-width: 768px) {
      height: 45px;
    }
    &:hover {
      border: 1px solid #11a2f3;
      color: #11a2f3;
    }
  }
  .chooseServiceButton {
    width: 100%;
    min-width: 125px;
    display: flex;
    height: 40px;
    padding: 8px;

    justify-content: center;
    align-items: center;
    gap: 10px;
    flex: 1 0 0;
    border-radius: 8px;
    background: linear-gradient(36deg, #00b5f1 0%, #00e0ff 100%);
    color: #fff;
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    border: none;
    @media (max-width: 768px) {
      height: 45px;
    }
    &:hover {
      background: linear-gradient(36deg, #239ecd 0%, #00b5f1 100%);
      color: #fff;
    }
    &:active {
      background: linear-gradient(36deg, #239ecd 0%, #00b5f1 100%);
      color: #fff;
    }
  }
  .listMobile {
    @media (max-width: 768px) {
      max-height: 68vh;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        width: 6px;
        display: block;
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #b2b2b2;
        background-image: linear-gradient(to top, #88d3ce 0%, #2d9cfd 100%);
        border: 1px solid #b5b5b5;
        border-radius: 5px;
      }
    }
  }
  .mobile {
    padding: 12px 6px 12px 12px;
    @media (min-width: 768px) {
      display: none;
    }
  }
  .showCheck {
    // border-bottom-left-radius: 0px !important;
    // border-bottom-right-radius: 0px !important;
  }
  .specialist {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 12px;
    border: 1px solid #e4e4e4;
    background: #fff;
    &:hover {
      cursor: pointer;
      // box-shadow: 0px 4px 15px rgba(116, 157, 206, 0.5) !important;
    }
    // &:last-child {
    //   border-bottom: none;
    // }
    @media (min-width: 768px) {
      display: none;
    }
  }
  .specialist .specialistItem {
    align-items: flex-start;
    align-self: stretch;
    display: flex;
    flex: 0 0 auto;
    gap: 8px;
    position: relative;
    width: 100%;
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0px;
    }
  }

  .specialist .itemTitle {
    color: var(--primary-body-text, #003553);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: -1px;
    position: relative;
    min-width: 100px;
  }
  .error {
    font-size: 1.2rem;
    color: red;
    text-align: left;
    margin: 0;
    padding: 0 16px;
    margin: 0;
    display: flex;
  }
  .hiddenErr {
    padding: 0 16px;
    display: none;
  }
  .contentStt {
    width: 100%;
    flex: 1;
    color: var(--primary-body-text, #003553) !important;
    font-size: 17px !important;
    font-style: normal;
    font-weight: 600 !important;
    line-height: normal;
    margin-top: -1px;
    position: relative;
    word-wrap: break-word;
  }
  .specialist .content {
    width: 100%;
    flex: 1;
    color: var(--primary-body-text, #003553) !important;
    font-size: 16px !important;
    font-style: normal;
    font-weight: 500 !important;
    line-height: normal;
    margin-top: -1px;
    position: relative;
    word-wrap: break-word;
    .button {
      background-color: rgba(253, 57, 122, 0.1);
      color: #fd397a;
      border: 1px solid rgba(253, 57, 122, 0.1);
      &:hover {
        color: #fff;
        background-color: #fd397a;
      }
    }
  }
}

.subService {
  font-style: italic;
}
.serviceTable {
  width: 100%;
  @media (max-width: 768px) {
    display: none;
  }
  .serviceDetail {
    cursor: pointer;
    border-top: 0.5px solid #b2b2b2;
    border-collapse: collapse;
  }
  td,
  th {
    padding: 20px 5px;
  }

  .stt {
    width: 7%;
    text-align: center;
  }

  .name {
    width: 60%;
    text-align: left;
  }

  .price {
    width: 17%;
    text-align: left;
  }
  .salePrice {
    margin-bottom: 0;
  }
  .originalPrice {
    text-decoration: line-through;
    color: #7b8794;
    text-align: left;
    margin-bottom: 0;
  }
  .action {
    button {
      max-width: 125px;
    }
  }
}

.checkBHYT {
  width: 100%;
  background: #bfbaba1c;
  .checkBHYTWrapper {
    display: flex;
    justify-content: space-between;
    padding-left: 7%;
    padding-right: 10px;
  }
  .inputRadio {
    font-weight: 600;
    width: 33%;
  }
}
.optionBHYT {
  width: 100%;
  background: #bfbaba1c;
  td {
    padding: 0 0 20px 0;
  }
  :global {
    .ant-radio-wrapper {
      span {
        color: #003553;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
.checkAddonMobile {
  display: none !important;
  @media (max-width: 768px) {
    display: flex !important;
    flex-direction: column;
  }
}
.checkBHYTMobile {
  width: 100%;
  display: none !important;
  @media (max-width: 768px) {
    display: flex !important;
  }
  height: 52px;
  border-bottom: none !important;
  text-align: center;
  display: flex;
  justify-content: space-between;
  .checkBHYTWrapper {
    display: flex;
    justify-content: space-between;
    // padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .name {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 16px;
  }
  .inputRadio {
    font-weight: 600;
    width: 33%;
    justify-content: right;
    align-items: center;
    display: flex;
    :global {
      .ant-radio-group {
        text-align: left;
      }
    }
  }

  background: #bfbaba1c;
}
.messengeGroup {
  padding: 8px 16px;
  width: 100%;
  display: flex;
  .nameErr {
    width: 100%;
    min-width: 0;
  }
  .inputRadio {
    font-weight: 600;
    width: 33%;
  }
}
.childrenCheck {
  transform-origin: 0% 0%;
  animation: showChildren 0.41s ease-in forwards;
  // border: 1px solid #e4e4e4;
}
.animation_fadeInDown {
  transform-origin: 0% 0%;
  animation: fadeInDown 0.3s forwards;
}
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes showChildren {
  0% {
    transform: scaleY(0);
    opacity: 0;
  }
  95% {
    transform: scaleY(1);
    opacity: 1;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}
