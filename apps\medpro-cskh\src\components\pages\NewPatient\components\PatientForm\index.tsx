import {
  MP<PERSON>reatePatientForm,
  MPLoading,
  PatientFormData
} from '@medpro-libs/libs'
import { get, size } from 'lodash'
import { PatientInsertQuery } from 'medpro-sdk-v2'
import moment from 'moment'
import router from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'
import client from '../../../../../../config/medproSdk'
import {
  useFetchNation,
  useFetchProfession
} from '../../../../../../hooks/query'
import { useAppSelector } from '../../../../../../store/hooks'
import { UserInfo } from '../../../../../../store/interface'
import { patientActions } from '../../../../../../store/patient/patientSlice'
import {
  fetchCountries,
  fetchDistricts,
  fetchProvinces,
  fetchRelative,
  fetchWards
} from '../../../../../../store/total/slice'
import { getRoutePartnerIdQueryParams } from '@medpro-libs/libs'
import { PageRoutes } from '../../../../../../utils/PageRoutes'
import { showErrorNotification } from '../../../../../../utils/utils.error'
import { splitName } from '../../../../../../utils/utils.function'
import { openNotification } from '../../../../../../utils/utils.notification'

interface PatientFormProps {
  partnerId?: string
  patientId?: string
  patientUpdate?: any
}

export const PatientForm = ({
  partnerId,
  patientUpdate,
  patientId
}: PatientFormProps) => {
  const dispatch = useDispatch()

  const [submitting, setSubmitting] = useState(false)

  const province = useAppSelector((s) => s.total.provinces)
  const district = useAppSelector((s) => s.total.districts)
  const ward = useAppSelector((s) => s.total.wards)
  const relative = useAppSelector((s) => s.total.relative)
  const countries = useAppSelector((s) => s.total?.countries)
  const user = useAppSelector((s) => s.user?.info)
  const redirectUrl = useAppSelector(
    (s) => s.patient.updatePatient?.redirectUrl
  )
  const redirectUrlMemo = useMemo(() => redirectUrl, [])

  const isUpdate = !!patientUpdate

  const createPatientRedirectUrl = useAppSelector(
    (s) => s.patient.createPatientRedirectUrl
  )
  useEffect(() => {
    if (size(province) === 0) {
      dispatch(fetchProvinces({ country_code: '203' }))
    }

    if (size(relative) === 0) {
      dispatch(fetchRelative())
    }
    if (size(countries) === 0) {
      dispatch(fetchCountries())
    }
  }, [])

  useEffect(() => {
    if (patientUpdate) {
      if (patientUpdate.district_id && patientUpdate.city_id) {
        dispatch(fetchDistricts({ city_id: patientUpdate.city_id }))
      }

      if (patientUpdate.ward_id && patientUpdate.district_id) {
        dispatch(fetchWards({ district_id: patientUpdate.district_id }))
      }
    }
  }, [patientUpdate])

  const { data: profession, isLoading: loadingProfession } =
    useFetchProfession()
  const { data: nation, isLoading: loadingNation } = useFetchNation()

  const dataNewPatient = {
    title: isUpdate ? 'Cập nhật thông tin' : 'Nhập thông tin bệnh nhân',
    description:
      'Vui lòng cung cấp thông tin chính xác để được phục vụ tốt nhất. Trong trường hợp cung cấp sai thông tin bệnh nhân & điện thoại, việc xác nhận cuộc hẹn sẽ không hiệu lực trước khi đặt khám.',
    profession,
    country: [],
    nation,
    province,
    district,
    ward,
    patient: patientUpdate,
    relative,
    countries
  }

  const onChangeAddress = (type: string, id: string) => {
    console.log(type, id)
    switch (type) {
      case 'district':
        dispatch(fetchDistricts({ city_id: id }))
        break
      case 'ward':
        dispatch(fetchWards({ district_id: id }))
        break
      default:
        break
    }
  }

  const onSubmit = async (params: PatientFormData) => {
    // console.log('value form submit: ', params)
    const {
      name,
      ward_id = '0',
      address,
      district_id = '0',
      city_id = '0',
      sex,
      profession_id,
      dantoc_id,
      quocgia_id,
      mobile,
      day,
      month,
      year,
      relation = {},
      email,
      cmnd
    } = params

    // console.log('splitName(name): ', splitName(name))
    const { relative_email, relative_mobile, relative_name, relative_type_id } =
      relation
    const body: PatientInsertQuery = {
      address,
      sex,
      ward_id,
      district_id,
      city_id,
      mobile,
      ...splitName(name),
      // fullname: name as string,
      birthdate: moment(`${year}-${month}-${day}`, 'YYYY-MM-DD')
        .utc(true)
        .toISOString(),
      birthyear: year,
      profession_id,
      dantoc_id,
      country_code: quocgia_id || 'VIE',
      // isUpdateFull: true, // todo: what is that?,
      relative_email,
      relative_mobile,
      relative_name,
      relative_type_id,
      // bv_id: '',
      force: false,
      id: patientId as any,
      email,
      cmnd
    }

    console.log('params save patient: ', body)

    if (patientId) {
      try {
        setSubmitting(true)
        await client.patient.updatePatient(body)
        dispatch(patientActions.resetUpdatePatient())
        openNotification('success', {
          message: 'Cập nhật thông tin thành công.'
        })

        router.push(redirectUrlMemo || PageRoutes.user.path)
      } catch (err) {
        showErrorNotification(err)
      } finally {
        setSubmitting(false)
      }
    } else {
      try {
        setSubmitting(true)
        const { data } = await client.patient.insertPatient(body)
        const { isRecommended } = data

        if (isRecommended) {
          dispatch(patientActions.setNewPatientRecommendation(data))
          router.push(
            getRoutePartnerIdQueryParams(
              PageRoutes.patient.recommendation.path,
              partnerId
            )
          )
        } else {
          openNotification('success', { message: 'Thêm hồ sơ thành công' })
          router.push(createPatientRedirectUrl || PageRoutes.user.path)
        }
      } catch (err) {
        showErrorNotification(err)
      } finally {
        setSubmitting(false)
      }
    }
  }

  if (loadingNation || loadingProfession) {
    return (
      <MPLoading loading={{ description: 'Đang lấy thông tin bệnh nhân...' }} />
    )
  }

  return (
    <MPCreatePatientForm
      data={dataNewPatient}
      submitting={submitting}
      onSubmit={onSubmit}
      userCountry={checkCSCountryOrganization(user)}
      appId='cskh'
      onChangeAddress={onChangeAddress}
    />
  )
}

//TODO Check the CS Organization
function checkCSCountryOrganization(user: UserInfo) {
  //[defaultValue] (*): The value returned for undefined resolved values. => VN
  const userCountry = get(user, 'belongToOrgs[0].country', 'VN')
  switch (userCountry) {
    case 'KH':
      return 'CAM'
    default:
      return userCountry
  }
}
