import { Form, Input, Select } from 'antd'
import { findIndex, get, size } from 'lodash'
import { sexData } from '../../../common/constant'
import {
  handleNumber,
  handleSpecialCharacters,
  year
} from '../../../common/func'
import { Valid } from '../../../common/helper/valid'
import styles from './../styles.module.less'

type BirthDayType = {
  day: number
  month: number
  year: number
}
const valid = new Valid()
const { Option } = Select

export const handleDetails = (
  data: any,
  age: number,
  birthDay: BirthDayType,
  isCSKHApp: boolean,
  country: string,
  onResetFieldDay: () => void,
  patientYearOldAccepted: number,
  partnerId?: string
) => {
  console.info(patientYearOldAccepted, partnerId)
  const requiredMobilePhone = !(age >= 0 && age < patientYearOldAccepted)
  const ignoreProperties = get(data, 'patient.propertyIgnoreUpdate', [])
  const label_code_ID = '<PERSON>ã định danh/CCCD/Passport'
  const list = [
    {
      id: 'name',
      type: 'text',
      label: 'Họ và tên (có dấu)',
      placeholder: 'Ví dụ: Nguyễn Văn Bảo',
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.name }]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
              style={{ textTransform: 'uppercase' }}
            />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'birthdate',
      type: 'text',
      label: 'Ngày sinh (năm/tháng/ngày)',
      require: true,
      birthDay: birthDay,
      enter: ({ require, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            className={styles['formInputGroup']}
          >
            <div className={styles['inputGroup']}>
              <Form.Item
                name='year'
                rules={[{ validator: valid.year }]}
                className={styles['selectItem']}
              >
                <Select
                  size='large'
                  showSearch
                  placeholder='Năm'
                  disabled={disabled}
                >
                  {year()?.map((item, index) => (
                    <Option key={index} value={item.value}>
                      {item.title}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                name='month'
                rules={[{ validator: valid.month }]}
                className={styles['selectItem']}
              >
                <Select
                  size='large'
                  disabled={disabled}
                  showSearch
                  placeholder='Tháng'
                >
                  {month()?.map((item, index) => (
                    <Option key={index} value={item.value}>
                      {item.title}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                name='day'
                rules={[{ validator: valid.day }]}
                className={styles['selectItem']}
              >
                <Select
                  size='large'
                  disabled={disabled}
                  showSearch
                  placeholder='Ngày'
                >
                  {getDaysArray(birthDay, onResetFieldDay)?.map(
                    (item, index) => (
                      <Option key={index} value={item.value}>
                        {item.title}
                      </Option>
                    )
                  )}
                </Select>
              </Form.Item>
            </div>
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'mobile',
      type: 'text',
      label: 'Số điện thoại',
      placeholder: 'Vui lòng nhập số điện thoại ...',
      require: requiredMobilePhone,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              {
                validator: requiredMobilePhone
                  ? valid.mobile
                  : valid.mobileNoRequired
              }
            ]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
              onKeyPress={handleNumber}
            />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'sex',
      type: 'text',
      label: 'Giới tính',
      placeholder: 'Chọn giới tính ...',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.sex }]}
            className={styles['selectItem']}
          >
            <Select size='large' placeholder={placeholder} disabled={disabled}>
              {sexData?.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'profession_id',
      type: 'text',
      label: 'Nghề nghiệp',
      placeholder: 'Chọn nghề nghiệp',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.profession }]}
            className={styles['selectItem']}
          >
            <Select
              size='large'
              disabled={disabled || (age >= 0 && age < 6)}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                (option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.profession) > 0 &&
                data?.profession?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'cmnd',
      type: 'text',
      label: label_code_ID,
      placeholder: `Vui lòng nhập ${label_code_ID}`,
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              {
                validator: valid.cccd
              }
            ]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
              onKeyPress={handleSpecialCharacters}
            />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'email',
      type: 'text',
      label: 'Địa chỉ Email',
      placeholder: 'Nhập địa chỉ email để nhận phiếu khám',
      require: false,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.emailNoRequired }]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false
    },
    //TODO case cskh => nation will insert to this position
    {
      id: 'quocgia_id',
      type: 'text',
      label: 'Quốc gia',
      placeholder: 'Chọn quốc gia',
      require: false,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ required: require }]}
            className={styles['formInputItem']}
          >
            <Select
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                (option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.countries) > 0 &&
                data?.countries?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'dantoc_id',
      type: 'text',
      label: 'Dân tộc',
      placeholder: 'Chọn dân tộc',
      require: false,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ required: require }]}
            className={styles['selectItem']}
          >
            <Select
              size='large'
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                (option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.nation) > 0 &&
                data?.nation?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    }
  ]

  //TODO Case: not cskh app
  if (!isCSKHApp) {
    // Tiến thành xoa field countries ở vị trí "findCountriesIndex"
    const findCountriesIndex = handleeFindFieldIndex(list, 'quocgia_id')
    list.splice(findCountriesIndex, 1)
  }

  switch (country) {
    case 'VN':
    case 'VIE':
      break
    default: {
      // Các case country (nước ngoài) còn lại ẩn field từ city_id đến field ward_id
      const findProvincesIndex = handleeFindFieldIndex(list, 'city_id')
      const findWardsIndex = handleeFindFieldIndex(list, 'ward_id')
      list.splice(findProvincesIndex, findWardsIndex - findProvincesIndex + 1)
    }
  }

  return list.map((l) => ({ ...l, disabled: ignoreProperties.includes(l.id) }))
}

export const days = () => {
  let array: any[] = []
  new Array(31).fill(null).forEach((el, index) => {
    array = [...array, { title: index + 1, value: index + 1 }]
  })
  return array
}

const getDaysArray = function (
  birthDay: BirthDayType,
  onResetFieldDay: () => void
): {
  title: number
  value: number
}[] {
  if (!birthDay?.year || !birthDay?.month) {
    return days()
  }
  const { year, month, day } = birthDay
  const monthIndex = month - 1 // 0..11 instead of 1..12
  const date = new Date(year, monthIndex, 1)
  const result = []
  while (date.getMonth() === monthIndex) {
    result.push({ title: date.getDate(), value: date.getDate() })
    date.setDate(date.getDate() + 1)
  }
  const lastDay = result[result.length - 1]
  if (day > lastDay?.value) {
    onResetFieldDay()
  }
  return result
}

export const month = () => {
  let array: any[] = []
  new Array(12).fill(null).forEach((el, index) => {
    array = [...array, { title: index + 1, value: index + 1 }]
  })
  return array
}

function handleeFindFieldIndex(list: any[], id: string) {
  return findIndex(list, (inputField) => inputField.id === id)
}

const handleRequireInput = (label: string, require: boolean) => {
  if (require) {
    return (
      <>
        {label} <sup className={styles['requireInput']}>*</sup>
      </>
    )
  }
  return <>{label}</>
}

export const checkDisableDay = (birthDay: BirthDayType, disabled: boolean) => {
  if (birthDay?.year && birthDay?.month && !disabled) {
    return false
  }
  return true
}
