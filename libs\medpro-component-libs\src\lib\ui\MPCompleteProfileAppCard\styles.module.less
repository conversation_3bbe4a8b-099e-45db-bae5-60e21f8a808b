.enterCustom {
  padding: 0 10px;
  border-radius: 5px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 0.875rem;
  line-height: 1.5715;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
  position: relative;
  display: inline-block;
  width: 100%;
}

.main {
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: left;
  padding: 0;
  background: #f6f6f6;
  min-height: 87vh;

  .scanBtn {
    width: 100%;
    height: 47px;
    padding: 14px 10px 14px 10px;
    border-radius: 12px;
    gap: 10px;
    font-size: 16px;
    font-weight: 400;
    color: #11a2f3;
    border: 1px solid #11a2f3;
    background: #eff7ff;
  }
  .separate {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 12px 0;
    gap: 12px;
    p {
      margin-bottom: 0;
      font-size: 16px;
      color: #bebebe;
    }
  }

  .headerTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    background: #0ea2f3;
    font-size: 18px;
    font-weight: 600;
    line-height: 18px;
    text-align: center;
    color: #ffffff;
    margin-bottom: 1rem;
    padding: 0 16px;
    p {
      margin-bottom: 0;
    }
    .headerSide {
      width: 10%;
    }
  }

  .listInfo {
    width: 100%;
    height: 100%;
    list-style-type: none;
    padding: 0;
    margin: 0;

    .inputItem {
      padding: 0 16px;
      label {
        font-size: 16px;
        font-weight: 500;
      }
      .requireInput {
        font-size: 100%;
        top: -0.2em;
        left: 5px;
        color: red;
      }
      input {
        width: 100%;
        height: 50px;
        padding: 15px 12px 16px 12px;
        border-radius: 12px;
        border: 1px solid #cbd2d9;
        gap: 8px;
        font-size: 16px;
      }
      .validInput {
        border: 1px solid #11a2f3;
        border-radius: 12px;
        :global {
          .ant-select-selector {
            border: none;
            height: 48px;
          }
        }
      }
      .disabledValidInput {
        :global {
          .ant-select-selector {
            background: #ebebeb !important;
          }
          .ant-select-selection-item {
            color: rgba(0, 0, 0, 0.35) !important;
          }
        }
      }
      :global {
        .ant-input-number-disabled,
        .ant-select-disabled.ant-select:not(.ant-select-customize-input)
          .ant-select-selector,
        .ant-input[disabled] {
          background: #ffffff;
          color: #000000;
        }
        .ant-input-number {
          width: 100%;
        }
        .ant-form-item-explain-error {
          font-size: 0.7rem;
        }
        .ant-input-status-success {
          border: 1px solid #11a2f3;
        }
        .ant-select-selector {
          width: 100%;
          height: 50px;
          padding: 15px 12px 16px 12px;
          border-radius: 12px;
          border: 1px solid #cbd2d9;
          gap: 8px;
          font-size: 16px;
          .ant-select-selection-placeholder {
            overflow: unset;
            display: flex;
            align-items: center;
          }
        }
        .ant-select-selection-item {
          color: #24313d;
          font-size: 16px;
        }
        .ant-select-arrow {
          transform: rotate(-90deg);
          color: #bebebe;
        }
        .ant-select-selector {
          display: flex;
          align-items: center;
          padding: 12px;
        }
        .ant-form-item-label
          > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
          display: none !important;
        }
        .ant-input[disabled] {
          color: rgba(0, 0, 0, 0.35) !important;
          background: #ebebeb !important;
        }
      }
    }
    .inputItemHalfLeft {
      padding: 0 0 0 16px;
    }
    .inputItemHalfRight {
      padding: 0 16px 0 0;
    }
    .phoneNumber {
      input {
        padding-left: 40%;
      }
      .selectPhoneLocale {
        position: absolute;
        left: 1px;
        // width: 60%;
        width: 36%;
        height: 100%;
        :global {
          .ant-select-selector {
            height: 50%;
            border: none;
            margin: auto;
            top: 25%;
            // left: -20%;
            left: 0%;
            box-shadow: none !important;
            border-right: 1px solid #bebebe !important;
            border-radius: 0;
            // width: 110px;
            width: 120px;
          }
          .ant-select-arrow {
            transform: rotate(0) translateX(-5px);
          }
          .ant-select-selection-item {
            text-overflow: unset !important;
          }
        }
        .countryName {
          display: none;
        }
      }
    }
    .footerBtn {
      position: sticky;
      bottom: 0;
      z-index: 199;
      width: 100%;
      padding: 16px;
      display: flex;
      align-items: center;
      background: #ffffff;
      margin-top: 1rem;
      .btnSubmit {
        width: 100%;
        height: 50px;
        // top: 3rem;

        padding: 12px;
        border-radius: 12px;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
      }
      .disabledBtn {
        background: #d7dbe0;
        border: none;
        color: #ffffff;
      }
    }

    .line {
      width: 88%;
      height: 2px;
      background: #dddddd;
      margin: 0.5rem auto;
    }
    .relativeTitle {
      font-size: 18px;
      font-weight: 500;
      margin: 1rem auto;
    }
  }
}

.drawerPickSelect {
  .sexItem {
    width: 100%;
    height: 50px;
    padding: 15.5px 12px 15.5px 12px;
    margin-top: 12px;
    border-radius: 12px;
    border: 1px;
    gap: 386px;
    border: 1px solid #d7dbe0;
    display: flex;
    align-items: center;
    &:focus {
      border: 1px solid #11a2f3;
    }
  }
  .searchItem {
    position: relative;
    height: 50px;
    margin-bottom: 16px;
    input + span {
      position: absolute;
      top: -39px;
      left: 15px;
      width: 28px !important;
      height: 28px !important;
      padding: 4px;
      gap: 10px;
    }
    input {
      width: 100%;
      height: 100%;
      padding: 10px 12px 10px 12px;
      padding-left: 48px;
      border-radius: 12px;
      gap: 8px;
      background: #f0f1f1;
      border: none;
      &:focus-visible {
        outline: 1px solid #11a2f3 !important;
      }
    }
  }
  .itemSelected {
    border-color: #11a2f3 !important;
  }
}

.notePopup {
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-header {
      background: #ffffff;
      border-bottom: none;
      padding: 15px;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
    .ant-modal-title {
      text-align: center;
      color: #11a2f3;
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: center;
    }
    .ant-modal-close-x {
      svg {
        fill: #24313d;
      }
    }
    .ant-modal-body {
      padding: 0 16px 16px 16px;
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        color: #24313d;
      }
    }
  }
  .btnContainer {
    display: flex;
    gap: 12px;
    button {
      width: 172px;
      height: 50px;
      padding: 12px 10px 12px 10px;
      gap: 10px;
      border-radius: 12px;
      border: 1px;
      font-size: 16px;
      font-weight: 600;
      line-height: 19px;
      &:nth-child(1) {
        width: 60%;
        border: 1px solid #11a2f3;
        color: #11a2f3;
      }
    }
  }
}

.modalAddress {
  :global {
    .ant-modal-content {
      height: fit-content;
      border-radius: 16px;
    }
  }
  .title {
    font-weight: 500;
    font-size: 24px;
    line-height: 28px;
    text-align: center;
    color: #11a2f3;
  }
  .desc {
    margin: 12px 0 12px 0;
    font-weight: 400;
    font-size: 20px;
    line-height: 23px;
    text-align: center;
    color: #24313d;
  }
  .input {
    height: fit-content;
    border-radius: 8px;
    padding: 12px;
    gap: 4px;
    border: 1px solid #cbd2d9;
    > p {
      margin: 0;
    }
    .add,
    .address {
      font-weight: 400;
    }
    .add {
      font-size: 14px;
      line-height: 18px;
      color: #52575c;
    }
    .address {
      font-size: 16px;
      line-height: 23px;
      color: #24313d;
    }
  }
  .btnWrapper {
    display: flex;
    gap: 16px;
    margin-top: 16px;

    button {
      width: 100%;
      height: 50px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 16px;
      line-height: 18.75px;
    }

    .btnCancel {
      background: #ffffff;
      color: #11a2f3;
      border: 1px solid #11a2f3;
    }

    .btnConfirm {
      background: #11a2f3;
      color: #ffffff;
    }
  }
}

.Divider {
  margin-top: 0 !important;
  margin-bottom: 20px !important;
  padding: 0 16px;
  &::after {
    top: 0 !important;
    border-color: #cbd2d9 !important;
  }
  @media (max-width: 576px) {
    padding-right: 16px !important;
    padding-left: 16px !important;
  }
  :global {
    .ant-divider-inner-text {
      font-weight: 700;
      font-size: 24px;
      line-height: 23.44px;
      color: #003553;
      padding-right: 12px !important;
    }
  }
}

.boxAddress {
  display: flex;
  flex-direction: column;
  .sup {
    font-weight: 400;
    font-style: italic;
    font-size: 16px;
    margin-bottom: 0;
    color: #f5222d;
  }
}
