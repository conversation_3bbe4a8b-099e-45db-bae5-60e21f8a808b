.cardDoctor {
  background-color: #ffffff;
  border-radius: 12px;
  height: fit-content;
  overflow: hidden;
  position: relative;
  width: 100%;
  margin-bottom: 20px;
  box-shadow: 4px 8px 30px 0px rgba(177, 196, 218, 0.35);
  border: 1px solid #e8f2f7;
  .frame {
    align-items: center;
    background-color: #eff6ff;
    display: flex;
    justify-content: space-between;
    padding: 12px 14px;
    width: 100%;
    .bottomLeft {
      align-items: flex-start;
      display: flex;
      flex: 0 0 auto;
      gap: 4px;
      width: 70%;
      @media (max-width: 425px) {
        width: 60%;
      }
    }
    .bottomRight {
      width: fit-content;
      display: flex;
      justify-content: center;
      .btnBooking {
        width: 150px !important;
        display: flex;
        padding: 10px 30px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        height: 36px;
        align-self: stretch;
        color: #fff;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        border-radius: 30px;
        background: var(--blue, #11a2f3);
        @media only screen and (max-width: 992px) {
          font-size: 13px;
        }
        @media only screen and (max-width: 576px) {
          width: 114px !important;
        }
        background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
        border: 1px solid transparent;
        color: white;
        // @media (max-width: 992px) {
        //   // padding: 0;
        //   background-clip: text;
        //   -webkit-background-clip: text;
        //   -webkit-text-fill-color: transparent;
        //   box-shadow: none;
        // }
        &:active,
        &:focus {
          @media only screen and (max-width: 992px) {
            // border: none;
            background: none !important;
            -webkit-text-fill-color: #00b5f1;
          }
        }
        &:hover {
          @media (min-width: 578px) {
            box-shadow: 0px 4px 30px 0px rgba(116, 157, 206, 0.2);
            -webkit-text-fill-color: white;
            background: linear-gradient(83.63deg, #07aae0, #00c6e1) !important;
          }
        }
      }
    }
  }

  .linear-location {
    height: 21px !important;
    width: 21px !important;
    @media (max-width: 576px) {
      height: 16px !important;
      width: 16px !important;
    }
  }
  .groupAddress {
    align-items: flex-start;
    display: flex;
    flex: 1;
    flex-direction: column;
    flex-grow: 1;
    gap: 4px;
  }
  .hopital {
    align-self: stretch;
    color: #003553;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: normal;
    margin-top: -1px;
    margin-bottom: 0px;
    word-wrap: break-word;
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
  .leftGroup {
    gap: 24px;
    display: flex;
    padding: 14px;
    @media (max-width: 768px) {
      display: block;
     }
    .logoImg {
      width: 175px;
      height: 175px;
      min-width: 175px;
      position: relative;
      background: #eaeaea;
      border-radius: 8px;
      @media (max-width: 768px) {
        margin: auto;
        width: 150px;
        height: 150px;
       }
      .btnView {
        display: flex;
        width: 120px;
        height: 27px;
        padding: 10px 30px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 0px 0px 8px 8px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        border: none !important;
        position: absolute;
        bottom: 0;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        font-size: 14px;
        @media (max-width: 576px) {
          width: 80px;
          height: 20px;
          font-size: 10px;
        }
        &:hover {
          cursor: pointer;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
        }
      }
    }
  }
  .address {
    color: var(--grey-text, #858585);
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    word-wrap: break-word !important;
    margin-bottom: 0;
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
  .rectangle {
    height: 118px;
    position: relative;
    width: 118px;
  }
  .groupInfo {
    // margin-top: 14px;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    @media (max-width: 768px) {
      margin-top: 0px;
    }
    h1 {
      color: #11a2f3;
      font-size: 29px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 8px;
      @media (max-width: 576px) {
        font-size: 24px;
        margin-top: 8px;
        text-align: center;
      }
      strong {
        font-weight: 500;
      }
    }
    .info_General {
      display: flex;
      align-items: flex-start;
      margin-bottom: 4px;
      line-height: 28px;
      label {
        min-width: 105px;
        font-weight: 600;
      }
      span {
        font-weight: 400;
      }
      :global {
        .ant-typography {
          margin-bottom: unset;
        }
      }
      .read{ 
        color: #11a2f3;
        margin-bottom: 0;
      }
    }
    .info_Position {
      // display: flex;
      .position_List {
        margin-bottom: 0;
        li {
          list-style: none;
        }
      }
    }
    .info_Rating {
      display: flex;
      align-items: center;
      // gap: 8px;
      span {
        color: #00b5f1;
        font-size: 16px;
        font-weight: 500;
        line-height: 6px;
      }
      .rating_General {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}
.modal {
  border-radius: 16px !important;
  :global {
    .ant-modal-header {
      background-color: #fff !important;
      border-radius: 16px;
      border: none;
      padding: 16px 24px 0 24px;
    }
    .ant-modal-footer{
      padding: 0 16px 16px 16px !important;
      border-top: none !important;
      .ant-btn-default{
        display: none !important;
      }
      .ant-btn{
        border-radius: 12px;
      }
    }
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-body {
      color: var(--primary-body-text, #003553);
      font-family: 'Roboto' !important;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .ant-modal-close-x {
      svg {
        fill: #000;
      }
    }
    .ant-modal-title {
      text-align: center;
      font-family: 'Roboto' !important;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      background: var(
        --primary-gradient-title,
        linear-gradient(84deg, #00b5f1 33.34%, #00e0ff 113.91%)
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
