import { MPCompleteProfileApp, MPCreateProfileApp } from '@medpro-libs/libs'
import * as React from 'react'

import { useEffect, useMemo, useState } from 'react'
import { useAppSelector } from '../../../../store/hooks'
import { size } from 'lodash'
import { useDispatch } from 'react-redux'
import {
  fetchCountries,
  fetchDistricts,
  fetchProvinces,
  fetchRelative,
  fetchWards,
  setBreadcrumb
} from '../../../../store/total/slice'
import client from '../../../../config/medproSdk'
import { openNotification } from '../../../../utils/utils.notification'
import { showErrorNotification } from '../../../../utils/utils.error'
import moment from 'moment'
import { useRouter } from 'next/router'
import { patientActions } from '../../../../store/patient/patientSlice'
import _ from 'lodash'
import { useFetchNation, useFetchProfession } from '../../../../hooks/query'
import withAuth from '../../../../layout/Private/withAuth'

export interface ICreateProfileAppProps {}

function CreateProfileApp(props: ICreateProfileAppProps) {
  const router = useRouter()
  const partnerId = router.query.partnerId as string
  const dispatch = useDispatch()
  const [showPopup, setShowPopup] = useState(false)
  const [idForUpdate, setIdForUpdate] = useState('')
  const [phoneLocale, setPhoneLocale] = useState([])

  const countries = useAppSelector((s) => s.total.countries)
  const district = useAppSelector((s) => s.total.districts)
  const ward = useAppSelector((s) => s.total.wards)
  const province = useAppSelector((s) => s.total.provinces)
  const relative = useAppSelector((s) => s.total.relative)
  const { data: profession, isLoading: loadingProfession } =
    useFetchProfession()
  const { data: nation, isLoading: loadingNation } = useFetchNation()
  const schedule: any = useAppSelector((s) => s.booking.schedule)
  const treeId = useAppSelector((s) => s.booking.treeId)
  const dataPatient = useAppSelector((s) => s.patient.listPatient)

  useEffect(() => {
    if (size(province) === 0) {
      dispatch(fetchProvinces({ country_code: '203' }))
    }
    if (size(countries) === 0) {
      dispatch(fetchCountries())
    }
    if (size(relative) === 0) {
      dispatch(fetchRelative())
    }
    fetchPhoneLocale()
    dispatch(setBreadcrumb([]))
  }, [])

  const onCreateProfile = async (values: any) => {
    try {
      let params = {}
      const { serviceId, subjectId, doctorId, roomId, date, timeslot } =
        schedule || {}
      const dateStr = `${moment(date).format('YYYY-MM-DD')} ${
        timeslot?.startTime
      }`
      const bookingDate = moment(dateStr, 'YYYY-MM-DD HH:mm').toISOString()
      params = { serviceId, subjectId, doctorId, roomId, bookingDate, treeId }

      if (partnerId !== 'choray') {
        if (values.surname) {
          values = { ...values, name: `${values.surname} ${values.name}` }
          delete values.surname
        }
        const { data } = await client.patient.insertBasicInfo({
          ...values,
          bookingData: { ...params }
        })
        openNotification('success', {
          message: 'Tạo hồ sơ thành công'
        })
        dispatch(
          patientActions.setSelectedPatient({ ...data, isCreated: true })
        )
        dispatch(patientActions.getPatientByUserIdApp())
        if (
          data &&
          data.constraintInfo &&
          data.constraintInfo.isValid === false
        ) {
          setShowPopup(!data.constraintInfo.isValid)
          setIdForUpdate(data.id)
        } else {
          router.push({
            pathname: '/chon-lich-kham',
            query: {
              ...router.query,
              step: 'chon-ho-so'
            }
          })
        }
      } else {
        const { data } = await client.patient.insertPatient({
          ...values,
          bookingData: { ...params }
        })
        openNotification('success', {
          message: 'Tạo hồ sơ thành công'
        })
        dispatch(
          patientActions.setSelectedPatient({ ...data, isCreated: true })
        )
        dispatch(patientActions.getPatientByUserIdApp())
        if (
          data &&
          data.constraintInfo &&
          data.constraintInfo.isValid === false
        ) {
          setShowPopup(!data.constraintInfo.isValid)
          setIdForUpdate(data.id)
        } else {
          router.push({
            pathname: '/chon-lich-kham',
            query: {
              ...router.query,
              step: 'chon-ho-so'
            }
          })
        }
      }
    } catch (err) {
      showErrorNotification(err)
    }
  }

  const onChangeAddress = (type: string, id: string) => {
    console.log(type, id)
    switch (type) {
      case 'district':
        dispatch(fetchDistricts({ city_id: id }))
        break
      case 'ward':
        dispatch(fetchWards({ district_id: id }))
        break
      default:
        break
    }
  }

  const fetchPhoneLocale = async () => {
    try {
      const { data } = await client.patient.getPhoneLocale()
      if (_.isArray(data)) {
        setPhoneLocale(data)
      }
    } catch (err) {
      console.log(err)
    }
  }

  const dataCreate = useMemo(() => {
    const phonePrefix = '+84'
    const iso = 'vi-VN'

    return {
      profession,
      country: [],
      nation,
      province,
      patient: {
        prefix: phonePrefix,
        iso,
        relativeMobileLocaleIso: iso
      },
      countries,
      relative: relative?.map((item) => {
        return {
          ...item,
          value: item.id,
          title: item.name
        }
      })
    }
  }, [profession, nation, province, countries, relative])

  return (
    <>
      {partnerId === 'choray' ? (
        <MPCompleteProfileApp
          onSubmit={onCreateProfile}
          data={dataCreate}
          onChangeAddress={onChangeAddress}
          phoneLocale={phoneLocale}
          district={district}
          ward={ward}
          partnerId={partnerId}
        />
      ) : (
        <MPCreateProfileApp
          province={province}
          onSubmit={onCreateProfile}
          showPopup={showPopup}
          partnerId={partnerId}
          idForUpdate={idForUpdate}
        />
      )}
    </>
  )
}
export default withAuth()(CreateProfileApp)
