.enterCustom {
  padding: 0 10px;
  border-radius: 5px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 0.875rem;
  line-height: 1.5715;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
  position: relative;
  display: inline-block;
  width: 100%;
}

.main {
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: left;
  padding: 0;
  background: #f6f6f6;
  min-height: 46vh;

  .headerTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    background: #0ea2f3;
    font-size: 18px;
    font-weight: 600;
    line-height: 18px;
    text-align: center;
    color: #ffffff;
    margin-bottom: 1rem;
    padding: 0 16px;
    p {
      margin-bottom: 0;
    }
    .headerSide {
      width: 10%;
    }
  }

  .scanBox {
    padding: 0 16px;
    .scanBtn {
      width: 100%;
      height: 47px;
      padding: 14px 10px 14px 10px;
      border-radius: 12px;
      gap: 10px;
      font-size: 16px;
      font-weight: 400;
      color: #11a2f3;
      border: 1px solid #11a2f3;
      background: #eff7ff;
    }
    .separate {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 12px 0;
      gap: 12px;
      span {
        margin-bottom: 0;
        font-size: 16px;
        color: #bebebe;
        min-width: 78px;
      }
    }
  }

  .listInfo {
    width: 100%;
    height: 100%;
    list-style-type: none;
    padding: 0;
    margin: 0;

    .inputItem {
      padding: 0 16px;
      label {
        font-size: 16px;
        font-weight: 500;
      }
      .requireInput {
        font-size: 100%;
        top: -0.2em;
        left: 5px;
        color: red;
      }
      input {
        width: 100%;
        height: 50px;
        padding: 15px 12px 16px 12px;
        border-radius: 12px;
        border: 1px solid #cbd2d9;
        gap: 8px;
        font-size: 16px;
        text-transform: uppercase;
      }
      .validInput {
        border: 1px solid #11a2f3;
        border-radius: 12px;
        :global {
          .ant-select-selector {
            border: none;
            height: 48px;
          }
        }
      }
      ::-webkit-input-placeholder {
        /* WebKit browsers */
        text-transform: none;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        text-transform: none;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        text-transform: none;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10+ */
        text-transform: none;
      }
      ::placeholder {
        /* Recent browsers */
        text-transform: none;
      }
      :global {
        .ant-inpiut::placeholder {
          text-transform: capitalize !important;
        }
        .ant-input-number-disabled,
        .ant-select-disabled.ant-select:not(.ant-select-customize-input)
          .ant-select-selector,
        .ant-input[disabled] {
          background: #ffffff;
          color: #000000;
        }
        .ant-input-number {
          width: 100%;
        }
        .ant-form-item-explain-error {
          font-size: 0.7rem;
        }
        .ant-input-status-success {
          border: 1px solid #11a2f3;
        }
        .ant-select-selector {
          width: 100%;
          height: 50px;
          padding: 15px 12px 16px 12px;
          border-radius: 12px;
          border: 1px solid #cbd2d9;
          gap: 8px;
          font-size: 16px;
          .ant-select-selection-placeholder {
            overflow: unset;
            display: flex;
            align-items: center;
          }
        }
        .ant-select-selection-item {
          color: #24313d;
          font-size: 16px;
        }
        .ant-select-arrow {
          transform: rotate(-90deg);
          color: #bebebe;
        }
        .ant-select-selector {
          display: flex;
          align-items: center;
          padding: 12px;
        }
      }
    }
    .inputItemHalfLeft {
      padding: 0 0 0 16px;
    }
    .inputItemHalfRight {
      padding: 0 16px 0 0;
    }
    .footerBtn {
       position: absolute;
      bottom: 0;
      z-index: 199;
      width: 100%;
      padding: 16px;
      display: flex;
      align-items: center;
      background: #ffffff;
      margin-top: 1rem;
      .btnSubmit {
        width: 100%;
        height: 50px;
        padding: 12px;
        border-radius: 12px;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
      }
      .disabledBtn {
        background: #d7dbe0;
        border: none;
        color: #ffffff;
      }
    }
  }
}

.drawerPickSelect {
  .sexItem {
    width: 100%;
    height: 50px;
    padding: 15.5px 12px 15.5px 12px;
    margin-top: 12px;
    border-radius: 12px;
    border: 1px;
    gap: 386px;
    border: 1px solid #d7dbe0;
    display: flex;
    align-items: center;
    &:focus {
      border: 1px solid #11a2f3;
    }
  }
  .searchItem {
    position: relative;
    height: 50px;
    margin-bottom: 16px;
    input + span {
      position: absolute;
      top: -39px;
      left: 15px;
      width: 28px !important;
      height: 28px !important;
      padding: 4px;
      gap: 10px;
    }
    input {
      width: 100%;
      height: 100%;
      padding: 10px 12px 10px 12px;
      padding-left: 48px;
      border-radius: 12px;
      gap: 8px;
      background: #f0f1f1;
      border: none;
      &:focus-visible {
        outline: 1px solid #11a2f3 !important;
      }
    }
  }
  .itemSelected {
    outline: 1px solid #11a2f3 !important;
  }
}

.notePopup {
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }
    .ant-modal-header {
      background: #ffffff;
      border-bottom: none;
      padding: 15px;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
    .ant-modal-title {
      text-align: center;
      color: #11a2f3;
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: center;
    }
    .ant-modal-close-x {
      svg {
        fill: #24313d;
      }
    }
    .ant-modal-body {
      padding: 0 16px 16px 16px;
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        color: #24313d;
      }
    }
    .ant-modal-close {
      display: none;
    }
  }
  .btnContainer {
    display: flex;
    gap: 12px;
    button {
      width: 172px;
      height: 50px;
      padding: 12px 10px 12px 10px;
      gap: 10px;
      border-radius: 12px;
      border: 1px;
      font-size: 16px;
      font-weight: 600;
      line-height: 19px;
      &:nth-child(1) {
        width: 60%;
        border: 1px solid #11a2f3;
        color: #11a2f3;
      }
    }
  }
}
