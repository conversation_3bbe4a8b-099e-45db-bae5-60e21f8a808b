import React from 'react'
import MPContainer from '../MPContainer'
import styles from './styles.module.less'
import { Col, Row, Skeleton } from 'antd'
import MPFeatureItem from './common/MPFeatureItem'
import cx from 'classnames'

export interface MPFeatureCardProps {
  mode?: string
  data: any[]
  keyItem?: any
  title: string
  isPage?: 'chooseFeature' | 'home' | undefined
  handleSelectFeature: any
  dynamicColumn: number
  isMobile: boolean
}

const MPFeatureCard = ({
  mode,
  data,
  title,
  isPage,
  handleSelectFeature,
  dynamicColumn,
  isMobile
}: MPFeatureCardProps) => {
  let index = -1
  console.log('data', data)
  return (
    <MPContainer
      className={cx(
        styles['contentBannerHome'],
        isPage === 'chooseFeature' && styles['chooseFeatureContainer']
      )}
    >
      <Row
        className={cx(
          styles['rowboxService'],
          isPage === 'chooseFeature' && styles['chooseFeature']
        )}
      >
        <Col span={24} className={styles['colTitle']}>
          <h1
            className={cx(
              styles['title'],
              isPage === 'chooseFeature' && styles['chooseFeatureTitle']
            )}
          >
            {title}
          </h1>
          {!isMobile && (
            <div className={styles['desc']}>
              Đặt khám nhanh chóng, không phải chờ đợi với nhiều cơ sở y tế trên
              khắp các tỉnh thành
            </div>
          )}
        </Col>
        <Col span={24} className={styles['colBoxService']}>
          {data.length < 1 ? (
            <Skeleton paragraph={{ rows: 4 }} active={true} />
          ) : (
            <ul
              className={cx(
                styles['listBoxService'],
                isPage === 'chooseFeature' && styles['chooseFeatureList']
              )}
              style={{
                gridTemplateColumns: `repeat(${dynamicColumn || 2},1fr)`
              }}
            >
              {data.map((item: any, i: number) => {
                if (item?.status) index++
                return (
                  item?.status && (
                    <MPFeatureItem
                      key={i}
                      index={index}
                      item={item}
                      mode={mode}
                      handleSelectFeature={handleSelectFeature}
                      isPage={isPage}
                    />
                  )
                )
              })}
            </ul>
          )}
        </Col>
      </Row>
    </MPContainer>
  )
}

export default MPFeatureCard
