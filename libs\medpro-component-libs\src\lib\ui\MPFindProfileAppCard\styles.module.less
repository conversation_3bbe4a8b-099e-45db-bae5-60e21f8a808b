.main {
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: left;
  // padding: 12px 16px;
  background: #f6f6f6;
  min-height: 40vh;

  .headerTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    background: #0ea2f3;
    font-size: 18px;
    font-weight: 600;
    line-height: 18px;
    text-align: center;
    color: #ffffff;
    // margin-bottom: 1rem;
    padding: 0 16px;
    p {
      margin-bottom: 0;
    }
    .headerSide {
      width: 10%;
    }
  }

  .tabs {
    :global {
      .ant-tabs-content-holder {
        .ant-tabs-content {
          position: unset !important;
        }
      }
      .ant-tabs-nav-list {
        width: 100%;
        background: #ffffff;
        padding: 0 16px;
      }
      .ant-tabs-nav {
        margin: unset;
      }
      .ant-tabs-tab {
        width: 100%;
      }
      .ant-tabs-tab-btn {
        margin: auto;
        font-size: 16px;
        line-height: 19px;
        letter-spacing: 0em;
      }
      .ant-tabs-tab + .ant-tabs-tab {
        margin: unset;
      }
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #11a2f3;
        font-weight: 500;
      }
    }
    .tabContent {
      padding: 16px;
    }
    .searchLabel {
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 19px;
      letter-spacing: 0em;
      text-align: left;
      margin-left: 0.2rem;
    }
    .searchBox {
      display: flex;
      gap: 8px;
      height: 50px;
      margin-bottom: 1rem;

      input {
        width: 72%;
        padding: 16px 12px 16px 12px;
        border-radius: 12px;
        border: 1px solid #cbd2d9;
        font-size: 16px;
      }
      .btnSubmit {
        width: 25%;
        padding: 6px 8px 6px 8px;
        border-radius: 12px;
        gap: 8px;
        font-size: 16px;
      }
      .disabledBtn {
        background: #d7dbe0;
        border: none;
        color: #ffffff;
      }
    }
    .note {
      font-family: Inter;
      font-size: 14px;
      font-style: italic;
      font-weight: 400;
      line-height: 18px;
      letter-spacing: 0em;
      text-align: left;
      color: #627792;
    }
    .findWay {
      display: flex;
      align-items: center;
      gap: 5px;
      img {
        width: 20px !important;
        height: 20px !important;
      }
      span {
        font-size: 16px;
        font-weight: 400;
        line-height: 19px;
        letter-spacing: 0em;
        text-align: left;
        text-decoration: underline;
      }
    }
    .findWayImage {
      margin: auto;
      text-align: center;
      margin-top: 1rem;
    }
    .cardList {
      .cardListTitle {
        font-size: 16px;
        font-weight: 500;
        line-height: 19px;
        letter-spacing: 0em;
        text-align: center;
        color: #11a2f3;
        margin-top: 16px;
      }
      .cardItem {
        margin-bottom: 1rem;
        border: 1px solid #d7dbe0;
        border-radius: 16px;
        .infoItem {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          margin-bottom: 0.5rem;
        }
        :global {
          .ant-card-head {
            background: #eff7ff;
            color: #11a2f3;
            text-align: center;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
          }
          .ant-card-body {
            padding: 12px;
          }
        }
      }
    }
  }
}

.confirmDrawer {
  .phoneError {
    color: #ff4d4f !important;
    font-size: 0.8rem !important;
    margin-bottom: 0;
    margin-top: 0.5rem;
    margin-left: 0.5rem;
    text-align: left !important;
  }
  .btnSubmit {
    width: 100%;
    height: 50px;
    padding: 12px 10px 12px 10px;
    gap: 10px;
    border-radius: 12px;
    background: #11a2f3;
    color: #ffffff;
    margin-top: 1rem;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
  }
  .disabledBtn {
    background: #d7dbe0;
    border: none;
    color: #ffffff;
  }
  .phoneConfirmNote {
    margin-top: 1rem;
    p {
      font-size: 14px !important;
      &.continue {
        text-decoration: underline !important;
        font-style: italic;
        color: #11a2f3;
        font-size: 16px !important;
      }
    }
  }
  :global {
    .ant-drawer-body {
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 19px;
        letter-spacing: 0px;
        text-align: center;
        color: #24313d;
      }
      input {
        width: 100%;
        height: 50px;
        padding: 15px 13px;
        border-radius: 12px;
        gap: 8px;
        background: #ffffff;
        border: 1px solid #11a2f3;
        &:focus-visible {
          outline: 0 !important;
        }
      }
    }
  }
}

.confirmExtraDrawer {
  p {
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: 0px;
    text-align: center;
    color: #24313d;
  }
  input {
    width: 100%;
    height: 50px;
    padding: 15px 13px;
    border-radius: 12px;
    gap: 8px;
    background: #ffffff;
    border: 1px solid #11a2f3;
    &:focus {
      outline: #11a2f3;
    }
  }
  .modalButton {
    width: 100%;
    height: 50px;
    padding: 12px 10px 12px 10px;
    border-radius: 12px;
    background: #11a2f3;
    color: white;
    font-size: 16px;
    margin-top: 1rem;
  }
  .phoneError {
    color: #ff4d4f !important;
    font-size: 0.8rem !important;
    margin-bottom: 0;
    margin-top: 0.5rem;
    margin-left: 0.5rem;
    text-align: left !important;
  }
  .disabledBtn {
    background: #d7dbe0;
    border: none;
    color: #ffffff;
  }
  .phoneConfirmNote {
    margin-top: 1rem;
    p {
      font-size: 14px !important;
      &.continue {
        text-decoration: underline !important;
        color: #11a2f3;
        font-size: 16px !important;
        margin-bottom: 0.5em !important;
      }
    }
    .separate {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 12px 0;
      gap: 12px;
      span {
        margin-bottom: 0;
        font-size: 16px;
        color: #bebebe;
        min-width: 40px;
      }
    }
  }
  :global {
    .ant-modal-content {
      border-radius: 12px;
      position: relative;
    }
    .ant-modal-body {
      padding: 16px;
      max-height: 70vh;
      overflow: auto;
    }
    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: center;
      color: #11a2f3;
    }
    .ant-modal-close-x {
      svg {
        fill: #333 !important;
      }
    }
    .ant-modal-header {
      background-color: #fff !important;
      border-bottom: none;
      // padding-bottom: 0;
      border-radius: 12px;
      padding: 15px 16px 10px 16px !important;
    }
  }
}

.tabContentForgot {
  padding: 16px 0 0 0 !important;
  p {
    padding: 0 16px;
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
    color: #24313d;
  }
  .listInfo {
    width: 100%;
    height: 100%;
    list-style-type: none;
    padding: 0;
    margin: 0;

    .inputItem {
      padding: 0 16px;
      label {
        font-size: 16px;
        font-weight: 500;
      }
      .requireInput {
        font-size: 100%;
        top: -0.2em;
        left: 5px;
        color: red;
      }
      input {
        width: 100%;
        height: 50px;
        padding: 15px 12px 16px 12px;
        border-radius: 12px;
        border: 1px solid #cbd2d9;
        gap: 8px;
        font-size: 16px;
        text-transform: uppercase;
      }
      .validInput {
        border: 1px solid #11a2f3;
        border-radius: 12px;
        :global {
          .ant-select-selector {
            border: none;
            height: 48px;
          }
        }
      }
      ::-webkit-input-placeholder {
        /* WebKit browsers */
        text-transform: none;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        text-transform: none;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        text-transform: none;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10+ */
        text-transform: none;
      }
      ::placeholder {
        /* Recent browsers */
        text-transform: none;
      }
      :global {
        .ant-input-number-disabled,
        .ant-select-disabled.ant-select:not(.ant-select-customize-input)
          .ant-select-selector,
        .ant-input[disabled] {
          background: #ffffff;
          color: #000000;
        }
        .ant-input-number {
          width: 100%;
        }
        .ant-form-item-explain-error {
          font-size: 0.7rem;
        }
        .ant-input-status-success {
          border: 1px solid #11a2f3;
        }
        .ant-select-selector {
          width: 100%;
          height: 50px;
          padding: 15px 12px 16px 12px;
          border-radius: 12px;
          border: 1px solid #cbd2d9;
          gap: 8px;
          font-size: 16px;
          .ant-select-selection-placeholder {
            overflow: unset;
            display: flex;
            align-items: center;
          }
        }
        .ant-select-selection-item {
          color: #24313d;
          font-size: 16px;
        }
        .ant-select-arrow {
          transform: rotate(-90deg);
          color: #bebebe;
        }
        .ant-select-selector {
          display: flex;
          align-items: center;
          padding: 12px;
        }
      }
    }
    .inputItemHalfLeft {
      padding: 0 0 0 16px;
    }
    .inputItemHalfRight {
      padding: 0 16px 0 0;
    }

    .footerBtn {
      position: absolute;
      bottom: 0;
      z-index: 199;
      width: 100%;
      padding: 16px;
      display: flex;
      align-items: center;
      background: #ffffff;
      margin-top: 1rem;
      .btnSubmit {
        width: 100%;
        height: 50px;
        padding: 12px;
        border-radius: 12px;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
      }
      .disabledBtn {
        background: #d7dbe0;
        border: none;
        color: #ffffff;
      }
    }
  }
  .cardList {
    padding: 0 16px;
  }
}

.emptyList {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  p {
    font-size: 16px;
    font-style: italic;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: center;
    color: #f5222d;
  }
  div {
    display: flex;
    justify-content: center;
  }
}

.cardListSkeleton {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  :global {
    .ant-card-head {
      height: 50px;
    }
  }
  .skeletonItemTitle {
    height: 30px;
    :global {
      .ant-skeleton-paragraph {
        li {
          margin: auto;
          transform: translateY(5px);
        }
      }
    }
  }
}

.findExtra {
  .selectedPatient {
    padding: 0 16px;
    margin-top: 1rem;
    p {
      font-size: 16px;
      font-weight: 500;
      line-height: 19px;
      letter-spacing: 0em;
      text-align: center;
      color: #11a2f3;
      margin-top: 16px;
    }
  }
  .cardItem {
    margin-bottom: 1rem;
    border: 1px solid #d7dbe0;
    border-radius: 16px;
    .infoItem {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      margin-bottom: 0.5rem;
      .icon {
        width: 8% !important;
        display: flex;
        align-items: center;
      }
      .info {
        width: 85% !important;
      }
    }
    :global {
      .ant-card-head {
        background: #eff7ff;
        color: #11a2f3;
        text-align: center;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
      }
      .ant-card-body {
        padding: 12px;
      }
    }
  }
  .note {
    padding: 0 20px 0 20px;
    p {
      span {
        color: #f5222d;
      }
    }
  }
  .cardList {
    padding: 0 16px 16px 16px;
    .cardListTitle {
      font-size: 16px;
      font-weight: 500;
      line-height: 19px;
      letter-spacing: 0em;
      text-align: center;
      color: #11a2f3;
      margin-top: 16px;
    }
  }
  .footerBtn {
    // position: fixed;
    bottom: 0;
    z-index: 199;
    width: 100%;
    padding: 0 16px 8px;
    display: flex;
    align-items: center;
    // background: #ffffff;
    margin-top: 1rem;
    .btnSubmit {
      width: 100%;
      height: 50px;
      padding: 12px;
      border-radius: 12px;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
