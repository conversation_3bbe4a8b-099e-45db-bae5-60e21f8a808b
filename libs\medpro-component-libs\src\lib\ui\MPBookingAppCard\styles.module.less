.stepsNav {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  justify-content: center;
  padding: 8px 16px 8px 16px !important;
  //   margin-left: 20px !important;
  background: #f6f6f6;
  :global {
    .ant-steps-item-tail {
      display: none !important;
    }
    .ant-steps-item-finish .ant-steps-item-icon {
      background-color: #1da1f2 !important;
      svg {
        fill: #fff !important;
      }
      path {
        fill: #fff !important;
      }
    }
    .ant-steps-item-icon {
      //   background-color: red !important;
      border-radius: 50px !important;
      margin-right: 0 !important;
      width: 35px !important;
      height: 35px !important;
    }
    .ant-steps-item-container {
      margin-left: 8px;
    }

    .ant-steps-item-finish {
      .ant-steps-item-content .ant-steps-item-title::after {
        background-color: #11a2f3 !important;
        width: 100px !important;
        left: 8px !important;
      }
    }
    .ant-steps-item-process {
      .ant-steps-item-content .ant-steps-item-title::after {
        background-color: #d7dbe0 !important;
        width: 60px !important;
        left: 15px !important;
      }
    }
    .ant-steps-item-wait {
      .ant-steps-item-content .ant-steps-item-title::after {
        background-color: #d7dbe0 !important;
        width: 64px !important;
      }
    }

    .ant-steps-with-progress .ant-steps-item-icon .ant-progress {
      top: -3px;
    }
    .ant-progress-inner {
      width: 45px !important;
      height: 45px !important;
    }
    .ant-steps-item-custom
      > .ant-steps-item-container
      > .ant-steps-item-icon
      > .ant-steps-icon {
      top: 2.5px;
      left: 0px;
    }
    .ant-steps-item-icon .ant-steps-icon {
      top: 7.5px;
    }
    .ant-progress-status-success
      .ant-progress-inner:not(.ant-progress-circle-gradient)
      .ant-progress-circle-path {
      stroke: #1da1f2 !important;
    }
    .ant-steps-item-title {
      position: relative;
      display: inline-block;
      padding-right: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      line-height: 32px;
    }
    .ant-steps-item-title::after {
      position: absolute !important;
      top: 16px !important;
      left: 8px !important;
      display: block !important;
      width: 9999px !important;
      height: 1px !important;
      background: #d7dbe0 !important;
      content: '' !important;
    }
  }
  .lastStep {
    :global {
      .ant-steps-item-title {
        display: none !important;
      }
      //   .ant-steps-item-content {
      //     display: none !important;
      //   }
    }
  }
  .firstStep {
    :global {
      .ant-steps-item-container {
        margin-left: 0px !important;
        // background-color: red !important;
      }
    }
  }
}
.arrow {
  position: absolute;
  top: 12px;
  left: 16px;
}
.modal {
  .modalButton {
    width: 100%;
    height: 50px;
    padding: 12px 10px 12px 10px;
    border-radius: 12px;
    background: #11a2f3;
    color: white;
    font-size: 16px;
    margin-top: 8px;
  }
  :global {
    .ant-modal-content {
      border-radius: 12px;
      position: relative;
      p {
        font-family: Roboto !important;
        color: #003553;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
      }
    }
    .ant-modal-body {
      padding: 16px;
      max-height: 70vh;
      overflow: auto;
    }
    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: center;
      color: #11a2f3;
    }
    .ant-modal-close-x {
      position: absolute;
      right: 0;
      top: -35px;
      opacity: 0.8;
      border: none;
      background-color: #fff;
      border-radius: 5px;
      font-size: 17px;
      line-height: 0;
      padding: 15px 13px;
      color: #0352cc;
      width: 25px;
      height: 25px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-content: center;
      svg {
        margin-top: -7px;
      }
    }
    .ant-modal-header {
      background-color: #fff !important;
      border-bottom: none;
      // padding-bottom: 0;
      border-radius: 12px;
      padding: 15px 16px 10px 16px !important;
    }
    .ant-btn-default {
      width: Fill (151.5px) px;
      height: Fixed (50px) px;
      gap: 10px;
      border-radius: 12px;
      border: 1px solid #11a2f3;
      opacity: 0px;
    }
    .ant-btn-primary {
      border-radius: 12px;
      background-color: #11a2f3;
    }
  }
}
