import {MPConfirmPhonePatient, MPModalConfirmPhone, OkModal} from '@medpro-libs/libs'
import React from "react";

//TODO Tạm thời để typescript "any" sau khi optimize xong nhớ xóa dòng này
interface RecommendedPatientRecordsProps {
  data: any
  selectedPatient: any
  setSelectedPatient: any
  onSelectPatient: (p: any) => void
  confirm: (params: any) => void
  mapPatientForm: (formData: any) => any
  confirmPhone: (formData: any) => void
}

export const RecommendedPatientRecords = ({
  data,
  confirm,
  selectedPatient,
  setSelectedPatient,
  mapPatientForm,
  onSelectPatient,
                                            confirmPhone
}: RecommendedPatientRecordsProps) => {
  return (
    <div>
      <MPConfirmPhonePatient
        data={[
          mapPatientForm(data.patientForm),
          ...data.recommendedPatientList
        ]}
        onSubmitPhonePatient={onSelectPatient}
      />
      <OkModal
        title={'Xác nhận hồ sơ'}
        open={selectedPatient?.isCreateNew}
        onOk={() => {
          confirm(selectedPatient)
          setSelectedPatient(undefined)
        }}
        onCancel={() => setSelectedPatient(undefined)}
        okText={'Xác nhận'}
        cancelText={'Đóng'}
      >
        <div
          dangerouslySetInnerHTML={{
            __html: '<span>Bạn muốn sử dụng tạo mới hồ sơ này?</span>'
          }}
        ></div>
      </OkModal>
      <MPModalConfirmPhone
        visible={!!selectedPatient && !selectedPatient?.isCreateNew}
        onOk={confirmPhone}
        onCancel={() => setSelectedPatient(undefined)}
      />
    </div>
  )
}
