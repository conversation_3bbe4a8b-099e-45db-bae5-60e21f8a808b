import React from 'react'
import DefautDrawer from '../../DefaultDrawer'
import styles from './styles.module.less'
import { sexData } from '../../../common/constant'
import cx from 'classnames'

export interface Props {
  isOpen: boolean
  toggleDrawer: () => void
  form: any
  handleFormChange: () => void
}

const SexSelectDrawer = ({
  isOpen,
  toggleDrawer,
  form,
  handleFormChange
}: Props) => {
  return (
    <DefautDrawer
      onClose={toggleDrawer}
      open={isOpen}
      title='Chọn giới tính'
      height='262px'
      className={styles['sexSelectDrawer']}
    >
      {sexData?.map((item, index) => (
        <div
          key={index}
          className={cx(
            styles['sexItem'],
            item.value === form.getFieldValue('sex') && styles['itemSelected']
          )}
          onClick={() => {
            form.setFieldValue('sex', item.value)
            handleFormChange()
            toggleDrawer()
          }}
        >
          {item.title}
        </div>
      ))}
    </DefautDrawer>
  )
}

export default SexSelectDrawer
