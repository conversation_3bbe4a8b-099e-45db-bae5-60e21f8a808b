import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  getF<PERSON>at<PERSON><PERSON>,
  getReplaceUTF8,
  getTranformPrice
} from '@medpro-libs/libs'
import {
  Button,
  Checkbox,
  Collapse,
  Form,
  Input,
  Modal,
  notification
} from 'antd'
import cx from 'classnames'
import format from 'libs/medpro-component-libs/src/lib/common/format'
import { get, size } from 'lodash'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import { AiOutlineExclamationCircle } from 'react-icons/ai'
import { BiLeftArrowAlt } from 'react-icons/bi'
import { BsCalendar2DateFill } from 'react-icons/bs'
import {
  FaHandHoldingHeart,
  FaPhoneAlt,
  FaStethoscope,
  FaUser,
  FaUserMd,
  FaWallet
} from 'react-icons/fa'
import { HiCheckBadge } from 'react-icons/hi2'
import { IoIosInformationCircleOutline, IoMdClose } from 'react-icons/io'
import { IoCheckmarkCircle } from 'react-icons/io5'
import { TiLocation } from 'react-icons/ti'
import { useDispatch } from 'react-redux'
import client from '../../config/medproSdk'
import {
  handleRedirectPayment,
  repayment
} from '../../src/components/pages/Booking/func'
import DefautDrawer from '../../src/page-content/bookingApp/DefautDrawer'
import { PaymentAppSkeleton } from '../../src/page-content/bookingApp/PaymentBookingApp/PaymentAppSkeleton'
import {
  selectConfirmBookingData,
  selectPaymentFeeInfo,
  selectPaymentMethodQuery,
  selectPaymentMethods,
  selectRepaymentData,
  selectSelectedPaymentMethod,
  selectSelectedPaymentType,
  selectTreeId
} from '../../store/booking/selector'
import {
  bookingActions,
  resetPayment,
  setSelectedPaymentMethod,
  setSelectedPaymentType
} from '../../store/booking/slice'
import { useAppSelector } from '../../store/hooks'
import { hospitalActions } from '../../store/hospital/hospitalSlice'
import {
  selectAppId,
  selectExtraConfig,
  selectPartnerId,
  selectPartnerInfo
} from '../../store/hospital/selector'
import { patientActions } from '../../store/patient/patientSlice'
import styles from './styles.module.less'
import { LIST_APP_ID_MINI_APP } from 'apps/medpro-ssr/utils/utils.contants'
import { openNotification } from 'apps/medpro-ssr/utils/utils.notification'
const valid = new Valid()

const { Panel } = Collapse

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }

interface DrawerContentItem {
  data: any
  extraContent?: any
  title: string
  open: boolean
}

const RePaymentApp = () => {
  const router = useRouter()
  const dispatch = useDispatch()
  const appId = useAppSelector(selectAppId)
  const partnerInfo = useAppSelector(selectPartnerInfo)
  // Open module card
  const [openCardPay, setOpenCardPay] = useState(false)
  const treeId = useAppSelector(selectTreeId)
  const confirmData = useAppSelector(selectConfirmBookingData)
  const selectedPatient = useAppSelector(
    (state) => state.patient.selectedPatient
  )
  const detailPayment = useAppSelector((state) => state.booking.paymentInfo)
  const partnerId = useAppSelector(selectPartnerId)
  const paymentMethods = useAppSelector(selectPaymentMethods)
  const paymentMethodQuery = useAppSelector(selectPaymentMethodQuery)
  const selectedPaymentType = useAppSelector(selectSelectedPaymentType)
  const selectedPaymentMethod = useAppSelector(selectSelectedPaymentMethod)
  const repaymentData = useAppSelector(selectRepaymentData)
  const paymentFeeInfo = useAppSelector(selectPaymentFeeInfo)
  const loading = useAppSelector((s) => s.total.loading)
  const [modalNote, setModalNote] = useState(false)
  const [isReserving, setIsReserving] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const isLoading = size(paymentMethods) <= 0
  const [medproCareSupport, setMedproCareSupport] = useState<{
    status: boolean
    content?: string
    description_following?: string
  }>({ status: false, content: '', description_following: '' })
  const [openDetailMethod, setOpenDetailMethod] = useState<any>({
    open: false,
    data: {}
  })

  const [updateVisa, setUpdateVisa] = useState({
    status: false,
    initValue: {}
  })
  const [isCheckedVisa, setIsCheckedVisa] = useState(false)
  const [ipAddress, setIPAddress] = useState('')
  const [enteredVisa, setVisaEntered] = useState(false)
  const { schedulesSelected } = confirmData
  const extraConfig = useAppSelector(selectExtraConfig)
  const refundNote = get(extraConfig, 'refundNote', '')
  const displayDetail = schedulesSelected?.[0]?.service?.displayDetail
  const paymentDate = detailPayment?.data[0]
  const [form] = Form.useForm()

  const patientInfo = selectedPatient
  const fullName =
    patientInfo?.fullname || `${patientInfo?.surname} ${patientInfo?.name}`
  const PaymentInfo = schedulesSelected
  const infoLine2 = PaymentInfo[0]?.service?.infoLine2
  const codePaymentType = selectedPaymentType?.code
  const care247 = PaymentInfo?.[0]?.service?.care247
  const advanced = PaymentInfo?.[0]?.service.advanced
  const priceService = paymentFeeInfo.reduce((p, c) => {
    p += c?.service?.price
    return p
  }, 0)
  let sumGrandTotal = 0
  const tempGrandTotal = priceService + (care247?.addonServices[0]?.price || 0)

  if (care247?.partner2bill) {
    sumGrandTotal = selectedPaymentType
      ? selectedPaymentType?.grandTotal
      : priceService
  } else {
    sumGrandTotal = selectedPaymentType
      ? selectedPaymentType?.grandTotal
      : tempGrandTotal
  }

  useEffect(() => {
    dispatch(patientActions.getPatientDetail())
    dispatch(hospitalActions.getPartnerInfo())
    dispatch(resetPayment())
    getIPAddress()
  }, [])
  useEffect(() => {
    if (partnerId) {
      dispatch(bookingActions.getPaymentMethods())
    }
  }, [paymentMethodQuery, partnerId])

  const getIPAddress = () => {
    fetch('https://api.ipify.org?format=json')
      .then((response) => response.json())
      .then((data) => {
        setIPAddress(data.ip)
      })
      .catch((error) => {
        console.error('Error fetching IP address:', error)
      })
  }

  const handleFilterByKeyword = (value: any, data: any, key: any) => {
    return data.filter((item: any) => {
      const regex = new RegExp(getReplaceUTF8(value), 'i')
      return getReplaceUTF8(get(item, key)).match(regex)
    })
  }

  const renderLabelPayment = () => {
    switch (true) {
      case treeId !== 'CSKH' && partnerId === 'bvsingapore':
        return 'Phí tư vấn trực tuyến với bệnh viện SGH'
      case codePaymentType === 'fundiin':
        return 'Phí tiện ích + Phí thanh toán hộ qua Fundiin'
      default:
        return 'Phí nền tảng + Phí TGTT'
    }
  }

  const handlePayment = async () => {
    try {
      setIsReserving(true)
      await onOkReserveBooking()
    } finally {
      setIsReserving(false)
      window.dataLayer.push({
        event: 'Xác Nhận Phương Thức Thanh Toán',
        Action: 'Click',
        Category: 'Button-Action',
        Label: 'Đồng ý',
        Event: 'Xác Nhận Phương Thức Thanh Toán',
        PartnerId: partnerId,
        UserId: ''
      })
    }
  }

  const selectPaymentMethod = async ({ item }: any) => {
    const { paymentType, paymentMethod } = item
    await dispatch(setSelectedPaymentMethod(paymentMethod))
    await dispatch(setSelectedPaymentType(paymentType))
    if (paymentType) {
      actionScroll()
    }
  }
  useEffect(() => {
    if (!isLoading && LIST_APP_ID_MINI_APP.includes(appId)) {
      let uniquePaymentMethod
      if (paymentMethods.length === 1) {
        uniquePaymentMethod = paymentMethods[0]
      } else if (paymentMethods.length > 1) {
        uniquePaymentMethod = paymentMethods.find((d) =>
          d.methodId?.includes(appId)
        )
      } else {
        openNotification('error', {
          message: 'Không tìm thấy phương thức thanh toán!'
        })
        return
      }
      selectPaymentMethod({
        item: {
          paymentType: uniquePaymentMethod.paymentTypes[0],
          paymentMethod: uniquePaymentMethod
        }
      })
    }
  }, [isLoading, paymentMethods, appId])

  const actionScroll = () => {
    const payment = document.getElementById('payment') as HTMLElement
    payment?.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
      inline: 'nearest'
    })
  }
  const handleNoteOpen = () => {
    setModalNote(true)
  }
  const onOkReserveBooking = async () => {
    let paramsVisa
    if (selectedPaymentType?.checkEmail) {
      paramsVisa = {
        customerIpAddress: ipAddress,
        browserScreenHeight: window.innerHeight,
        browserScreenWidth: window.innerWidth
      }
    }
    await repayment({
      handleRedirectPayment: (data: any) =>
        handleRedirectPayment({
          data,
          router,
          dispatch,
          appId
        }),
      selectedPaymentMethod,
      selectedPaymentType,
      ...repaymentData,
      ...paramsVisa
    })
  }
  const handleOpenCardPay = () => {
    if (size(paymentMethods) > 1) {
      setOpenCardPay(true)
    }
  }

  const handleRequireInput = (label: string, require: boolean) => {
    if (require) {
      return (
        <>
          {label}
          <sup className={styles['requireInput']}>*</sup>
        </>
      )
    }
    return <span>{label}</span>
  }

  const toggleVisa = () => {
    if (!enteredVisa) {
      setUpdateVisa((preState) => ({
        ...preState,
        status: !updateVisa.status
      }))
    }
  }

  const handleFinish = async () => {
    const { fullName, userName, email } = form.getFieldsValue()
    await client.patient
      .updateUserInfo({
        fullname: fullName,
        username: userName,
        email: email,
        isSaveEmail: true
      })
      .then(() => {
        notification.success({ message: 'Cập nhật thông tin thành công !!!' })
        setVisaEntered(true)
        toggleVisa()
      })
      .catch(() => {
        notification.error({ message: 'Cập nhật thông tin thất bại !!!' })
      })
  }

  const handleCheckboxChange = (e) => {
    setIsCheckedVisa(e.target.checked)
  }

  const handleCancelVisa = () => {
    setIsCheckedVisa(false)
    toggleVisa()
  }

  const toggleClose = () => {
    setModalNote(false)
  }

  return (
    <>
      <div className={styles['headerTitle']}>
        <BiLeftArrowAlt size={24} onClick={() => router.back()} />
        <div className={styles['title']}>Thanh toán phiếu khám</div>
      </div>
      {loading ? (
        <PaymentAppSkeleton />
      ) : (
        <>
          <div className={styles['ConfirmPaymentBookingApp']}>
            <div className={styles['hospitalTitle']}>
              <h1>
                {partnerInfo?.name}
                {partnerInfo?.listingPackagePaid && (
                  <HiCheckBadge color='#0097FF' size={14} />
                )}
              </h1>
              <p>{partnerInfo?.address}</p>
            </div>
            <div className={styles['patientInfo']}>
              <h3>Thông tin bệnh nhân</h3>
              <Collapse
                ghost
                className={styles['collapse']}
                expandIconPosition='end'
              >
                <Panel
                  header={
                    <span className={styles['collapseHeader']}>
                      <FaUser size={18} color='#11A2F3' />
                      {fullName}
                    </span>
                  }
                  key='1'
                >
                  <p>
                    <FaPhoneAlt size={18} color='#11A2F3' />
                    {format.concerPhone(patientInfo?.mobile)}
                  </p>
                  <p>
                    <BsCalendar2DateFill size={18} color='#11A2F3' />
                    {patientInfo?.birthdate || patientInfo?.birthyear}
                  </p>
                  <p style={{ alignItems: 'flex-start' }}>
                    <TiLocation
                      size={20}
                      color='#11A2F3'
                      style={{ scale: '1.2' }}
                    />
                    {patientInfo?.fullAddress}
                  </p>
                </Panel>
              </Collapse>
            </div>
            <div className={styles['informationBooking']}>
              <h3>Thông tin đặt khám</h3>
              <div className={styles['bookingInfo']}>
                {PaymentInfo?.map((item: any, index) => {
                  return (
                    <>
                      <div key={index} className={styles['bookingInfoItem']}>
                        {item?.doctor?.name && (
                          <p>
                            <FaUserMd size={18} color='#11A2F3' />
                            {`${item?.doctor?.role} ${item?.doctor?.name}` ||
                              'Đang cập nhật'}
                          </p>
                        )}
                        {item?.subject?.name && (
                          <p>
                            <FaStethoscope size={18} color='#11A2F3' />
                            {`${item?.subject?.name || 'Đang cập nhật'}`}
                          </p>
                        )}
                        {item?.service?.name && (
                          <p style={{ alignItems: 'flex-start' }}>
                            <FaHandHoldingHeart size={18} color='#11A2F3' />
                            {`${item?.service?.name || 'Đang cập nhật'}`}
                          </p>
                        )}

                        <p>
                          <BsCalendar2DateFill size={18} color='#11A2F3' />
                          {item?.date ? (
                            <>
                              {moment(item?.date).format('DD/MM/YYYY')} (
                              {item?.timeslot.startTime} -{' '}
                              {item?.timeslot.endTime})
                            </>
                          ) : (
                            'Chờ cập nhật'
                          )}
                        </p>
                        {size(PaymentInfo) > 0 && (
                          <p>
                            <FaWallet size={18} color='#11A2F3' />
                            <span className={styles['priceBooking']}>
                              {item.service?.displayDetail &&
                              item.service?.displayDetail !== ''
                                ? item.service?.displayDetail
                                : getFormatMoney(item.service?.price) + ' đ'}
                            </span>
                          </p>
                        )}
                      </div>

                      {!!get(
                        item,
                        'schedulesSelected[0].service.advanced',
                        0
                      ) && (
                        <div className={styles['specialistItem']}>
                          <div className={styles['itemTitle']}>Tạm ứng </div>
                          <div className={styles['content']}>
                            {getFormatMoney(
                              get(
                                item,
                                'schedulesSelected[0].service.advanced',
                                0
                              )
                            ) + ' đ'}
                          </div>
                        </div>
                      )}
                    </>
                  )
                })}
                {infoLine2 && partnerId === 'bvsingapore' && (
                  <div className={styles['bookingInfoItem']}>
                    <p>
                      <FaStethoscope size={18} color='#11A2F3' />
                      {infoLine2.roomName}
                    </p>
                    <p>
                      <FaHandHoldingHeart size={18} color='#11A2F3' />
                      {infoLine2.serviceName}
                    </p>
                    <p>
                      <FaWallet size={18} color='#11A2F3' />
                      {getFormatMoney(infoLine2?.price)}
                    </p>
                    <p>
                      <BsCalendar2DateFill size={18} color='#11A2F3' />
                      {moment(PaymentInfo?.[0]?.date).format('DD/MM/YYYY')}
                      {PaymentInfo[0]?.time?.startTime && (
                        <span>
                          ({PaymentInfo[0]?.time?.startTime} -{' '}
                          {PaymentInfo[0]?.time?.endTime})
                        </span>
                      )}
                    </p>
                  </div>
                )}
              </div>
            </div>
            {size(care247?.addonServices) > 0 && (
              <div className={styles['informationBooking']}>
                <div dangerouslySetInnerHTML={{ __html: care247.htmlTitle }} />
                <div className={cx(styles['bookingInfo'])}>
                  {care247?.addonServices?.map((item: any) => {
                    return (
                      <div key={item?.id} className={styles['medproCareItem']}>
                        <p className={styles['careItemName']}>{item.name}</p>
                        <div className={styles.Detail}>
                          <p className={styles['careItemPrice']}>
                            Giá:{' '}
                            <span className={styles['price']}>
                              {getFormatMoney(item?.price)}
                              {item.currency} / {item.duration}
                            </span>
                            {!!item?.originalPrice &&
                              item?.originalPrice !== item?.price && (
                                <span className={styles['originalPrice']}>
                                  {getFormatMoney(item?.originalPrice)}
                                  {item.currency}
                                </span>
                              )}
                          </p>
                          <div
                            className={styles['careItemDetail']}
                            onClick={(e) => {
                              e.preventDefault()
                              setMedproCareSupport({
                                status: true,
                                content: item?.description
                              })
                            }}
                          >
                            Chi tiết
                          </div>
                        </div>
                      </div>
                    )
                  })}
                  <p className={styles['noteCare247']}>
                    *Dịch vụ này được cung cấp bởi Công ty Care247, không phải
                    CSYT cung cấp, không bắt buộc.
                  </p>
                </div>
              </div>
            )}
            {size(selectedPaymentType) > 0 && (
              <>
                <div className={styles['customTotalPrice']}>
                  <div className={styles['customExamination']}>
                    <p>
                      Tiền khám
                      <IoIosInformationCircleOutline
                        width={12}
                        height={12}
                        color='#11a2f3'
                        onClick={handleNoteOpen}
                      />
                    </p>
                    <div className={styles['text']}>
                      {getTranformPrice({
                        displayDetail: displayDetail,
                        price:
                          getFormatMoney(
                            selectedPaymentType.subTotal - advanced
                          ) + ' đ'
                      })}
                    </div>
                  </div>
                  {!!advanced && (
                    <div className={styles['customExamination']}>
                      <p>Tạm ứng </p>
                      <div className={styles['text']}>
                        {getFormatMoney(advanced) + ' đ'}
                      </div>
                    </div>
                  )}
                  {selectedPaymentType?.medproCareFee &&
                    !care247?.partner2bill && (
                      <div className={styles['customExamination']}>
                        <p>
                          Dịch vụ đặt thêm
                          {selectedPaymentType?.medproCareFee &&
                            care247?.medproCareNote && (
                              <IoIosInformationCircleOutline
                                width={12}
                                height={12}
                                color='#11a2f3'
                                onClick={handleNoteOpen}
                              />
                            )}
                        </p>
                        <div className={styles['text']}>
                          {getFormatMoney(selectedPaymentType?.medproCareFee) +
                            ' đ'}
                        </div>
                      </div>
                    )}
                  <div className={styles['customFee']}>
                    <div className={styles['group_fee']}>
                      <p>
                        {renderLabelPayment()}
                        <IoIosInformationCircleOutline
                          width={12}
                          height={12}
                          color='#11a2f3'
                          onClick={handleNoteOpen}
                        />
                      </p>
                      <div className={styles['text']}>
                        {`${selectedPaymentType?.totalFee?.toLocaleString(
                          'vi-VN'
                        )} đ`}
                      </div>
                    </div>
                    {extraConfig?.discountUMCGroup.includes(
                      (router.query.partnerId as string) || partnerId
                    ) && (
                      <sup className={styles['note_discount_fee_umc']}>
                        Giảm 2.000đ, ưu đãi từ Medpro & UMC
                      </sup>
                    )}
                  </div>
                  <div className={styles['customTotal']}>
                    <h3>Tổng tiền</h3>
                    <div className={styles['text']}>
                      {getFormatMoney(sumGrandTotal)} đ
                    </div>
                  </div>
                </div>
                {selectedPaymentMethod?.agreement && (
                  <div className={styles.acceptPaymentLabel}>
                    <IoCheckmarkCircle width={24} height={24} color='#52C41A' />
                    <div className={styles.checkboxText}>
                      {selectedPaymentMethod?.agreement}
                    </div>
                  </div>
                )}
                <div id='payment' />
              </>
            )}
            {refundNote && !selectedPaymentType && (
              <div
                className={styles['attentionConfirmPayment']}
                style={{ backgroundColor: refundNote.box.backgroundColor }}
              >
                <AiOutlineExclamationCircle
                  color={refundNote.text.color}
                  className={styles['icon']}
                />
                <p
                  className={styles['content']}
                  style={{ color: refundNote.text.color }}
                >
                  {refundNote.title}
                </p>
              </div>
            )}
          </div>
        </>
      )}

      <div className={styles['customButtonNext']}>
        {size(selectedPaymentType) > 0 ? (
          <MPButton
            htmlType='submit'
            className={styles['mpButton']}
            onClick={handlePayment}
            loading={isReserving}
          >
            Thanh toán
          </MPButton>
        ) : (
          <>
            <div className={styles['customButtonNextInfo']}>
              {size(care247?.addonServices) > 0 && (
                <>
                  <div className={styles['infoPayment']}>
                    <span>Tiền khám</span>
                    <span className={styles['textMedproCare']}>
                      {getFormatMoney(priceService) + ' đ'}
                    </span>
                  </div>
                  {!care247?.partner2bill && (
                    <div className={styles['infoPayment']}>
                      <span>Dịch vụ đặt thêm</span>
                      <span className={styles['textMedproCare']}>
                        {getFormatMoney(care247?.addonServices[0]?.price) +
                          ' đ'}
                      </span>
                    </div>
                  )}
                </>
              )}
              <div className={styles['infoPayment']}>
                <h3>Thanh toán tạm tính</h3>
                <div className={styles['text']}>
                  {getFormatMoney(sumGrandTotal) + ' đ'}
                </div>
              </div>
            </div>
            <div id='payment' className={styles['groupButton']}>
              <MPButton
                htmlType='submit'
                className={styles['mpButton']}
                onClick={handleOpenCardPay}
              >
                Tiến hành thanh toán
              </MPButton>
            </div>
          </>
        )}
      </div>
      {medproCareSupport.status && (
        <DefautDrawer
          title='Chi tiết dịch vụ'
          open={medproCareSupport.status}
          onClose={() =>
            setMedproCareSupport({
              status: false
            })
          }
          className={styles['drawer']}
          height='fitContent'
        >
          <div className={styles['description']}>
            <p className={styles['description_following']}>
              {medproCareSupport.description_following}
            </p>
            <div
              dangerouslySetInnerHTML={{
                __html: medproCareSupport?.content
              }}
            />
          </div>
          {/* <div className={styles['hotline']}>
            <div className={styles['icon']}>
              <FaPhoneAlt size={16} color=' #11A2F3' />
            </div>
            <p>
              <span className={styles['title']}>Gọi 1900 2115</span>
              <span className={styles['subTitle']}>
                để được hỗ trợ trực tiếp
              </span>
            </p>
          </div> */}
        </DefautDrawer>
      )}
      {/* ===== Popup Ghi chú ======*/}
      {modalNote && (
        <div style={{ borderRadius: '16px' }}>
          <Modal
            centered
            open={modalNote}
            // onOk={handleNoteOpen}16
            className={styles['customModalNoteModule']}
            bodyStyle={{ padding: '12px' }}
            destroyOnClose={false}
            closable={false}
            footer={null}
            onCancel={toggleClose}
          >
            <div className={styles['customModalNote']}>
              <div className={styles['customModalHeader']}>
                <span>Ghi chú</span>
                <IoMdClose
                  className={styles['iconCloseModule']}
                  onClick={toggleClose}
                />
              </div>
              <div className={styles['customModalBody']}>
                {selectedPaymentType?.subTotalNote && (
                  <>
                    <p>Tiền khám</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedPaymentType?.subTotalNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
                {care247?.medproCareNote && (
                  <>
                    <p>Dịch vụ đặt thêm</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: care247?.medproCareNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
                {selectedPaymentType?.totalFeeNote && (
                  <>
                    <p>{renderLabelPayment()}</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedPaymentType?.totalFeeNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
              </div>
            </div>
          </Modal>
        </div>
      )}
      {/* ===== Popup chi tiết thanh toán ======*/}
      {openDetailMethod.open && (
        <Modal
          title={''}
          open={openDetailMethod.open}
          centered
          className={styles['modalConfirmPayment']}
          okText='Đóng'
          onOk={() => {
            setOpenDetailMethod((preState) => ({
              ...preState,
              open: false
            }))
          }}
          cancelButtonProps={{ hidden: true }}
        >
          <div>
            <div className={styles['customModalHeader']}>
              <span>{openDetailMethod?.data?.labelPopup}</span>
            </div>
            <div
              className={styles['descriptionDetail']}
              dangerouslySetInnerHTML={{
                __html: openDetailMethod?.data?.contentPopup
              }}
            />
          </div>
        </Modal>
      )}
      {modalVisible && (
        <Modal
          title={null}
          open={modalVisible}
          centered
          onOk={async (e) => {
            try {
              setIsReserving(true)
              await onOkReserveBooking()
            } finally {
              setIsReserving(false)
              window.dataLayer.push({
                event: 'Xác Nhận Phương Thức Thanh Toán',
                Action: 'Click',
                Category: 'Button-Action',
                Label: 'Đồng ý',
                Event: 'Xác Nhận Phương Thức Thanh Toán',
                PartnerId: partnerId,
                UserId: ''
              })
            }
          }}
          okText={isReserving ? 'Đang xử lý...' : 'Đồng ý'}
          cancelButtonProps={{ style: { display: 'none' } }}
          okButtonProps={{
            loading: isReserving
          }}
          className={styles['modalConfirmPayment']}
        >
          <div>
            <div className={styles['customModalHeader']}>
              <span>Thông báo</span>
              <IoMdClose
                className={styles['iconCloseModule']}
                onClick={() => setModalVisible(false)}
              />
            </div>
            <div className={styles['title']}>
              {/* {selectedPaymentType?.paymentIcon?.path && (
                <img src={selectedPaymentType?.paymentIcon?.path} alt='' />
              )} */}
              Bạn đang thực hiện <b> {selectedPaymentType?.name}</b> với số tiền{' '}
              <b> {getFormatMoney(selectedPaymentType?.grandTotal)} đ</b>{' '}
            </div>

            {/* <div className={styles['description']}>
              Bạn sẽ nhận được phiếu khám bệnh ngay khi{' '}
              <b>thanh toán thành công.</b> Trường hợp không nhận được phiếu
              khám bệnh, vui lòng liên hệ <b>19002115.</b>
            </div> */}
          </div>
        </Modal>
      )}
      {/* ===== Popup bổ sung thông tin Visa ======*/}
      {updateVisa.status && (
        <DefautDrawer
          title={'Thông báo'}
          open={updateVisa.status}
          onClose={() => {}}
          height={'calc(100% - 150px)'}
          className={styles['drawer']}
          closeIcon={false}
        >
          <div>
            <p className={styles.description}>
              Theo quy định của Tổ chức thẻ Quốc tế, vui lòng điền đầy đủ thông
              tin người thanh toán
            </p>
            <Form
              form={form}
              layout='vertical'
              onFinish={handleFinish}
              className={styles['form']}
            >
              <Form.Item
                label={handleRequireInput('Họ & tên', true)}
                name='fullName'
                rules={[{ validator: valid.required }]}
              >
                <Input placeholder='Nhập họ và tên ghi trên thẻ Visa' />
              </Form.Item>
              <Form.Item
                label={handleRequireInput('Số điện thoại', true)}
                name='userName'
                rules={[{ validator: valid.mobile }]}
              >
                <Input placeholder='Nhập số điện thoại' />
              </Form.Item>
              <Form.Item
                label={handleRequireInput('Email', true)}
                name='email'
                rules={[{ validator: valid.email }]}
              >
                <Input placeholder='Nhập email' />
              </Form.Item>
              <Form.Item name='confirm'>
                <Checkbox
                  onChange={handleCheckboxChange}
                  children={
                    <p className={styles.contentConfirm}>
                      Tôi xác nhận thông tin trên là chính xác và hoàn toàn chịu
                      trách nhiệm về thông tin này.
                    </p>
                  }
                />
              </Form.Item>
              <div className={styles.ButtonControl}>
                <Button
                  type='ghost'
                  className={styles['cancelBtn']}
                  onClick={handleCancelVisa}
                >
                  Trở lại
                </Button>
                <Button
                  htmlType='submit'
                  className={styles['confirmBtn']}
                  disabled={!isCheckedVisa}
                  type='primary'
                >
                  Xác nhận
                </Button>
              </div>
            </Form>
          </div>
        </DefautDrawer>
      )}
    </>
  )
}
RePaymentApp.breadcrumb = [{ title: 'Thanh toán lại phiếu khám' }]
export default RePaymentApp
