import { Modal } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import { MdOutlineDoubleArrow } from 'react-icons/md'
import MPButton from '../../MPButton'
import styles from './styles.module.less'
import { AiFillStar } from 'react-icons/ai'
import { FaUser } from 'react-icons/fa'
import imgSubject from '../images/subject.svg'
import imgPin from '../images/pin.svg'
import calendar from '../images/calendar.svg'
import pay from '../images/pay.svg'
import { getTranformPrice } from '../../../common/func'

const DoctorCard = ({ data, handleBooking, handleViewMore }: any) => {
  const [overflowStates, setOverflowStates] = useState<boolean>()
  const [openModal, setOpenModal] = useState<boolean>()
  const [openBookingTelemed, setOpenBookingTelemed] = useState<any>(false)
  const [warningBooking, setWarningBooking] = useState<boolean>()
  const isDetail =
    data?.description?.isDetailVisible ||
    data?.doctorDescription?.isDetailVisible
  const price = getTranformPrice(data)
  useEffect(() => {
    const getElementFullWidth = (text: string): number => {
      const para = document.createElement('p')
      const node = document.createTextNode('Chuyên trị: ' + text)
      para.appendChild(node)
      para.style.width = 'fit-content'
      document.body.appendChild(para)
      const elementWidth = para.offsetWidth
      document.body.removeChild(para)
      return elementWidth
    }
    if (data?.treatments) {
      const handleOverflowCheck = () => {
        const lineClampContainers: any = document.querySelector(
          `.${styles['treatment']}`
        )
        const lineClampDesc = getElementFullWidth(data?.treatments)
        const hasOverflow = lineClampDesc > lineClampContainers.clientWidth - 23
        setOverflowStates(hasOverflow)
      }
      handleOverflowCheck()
      window.addEventListener('resize', handleOverflowCheck)
      return () => {
        window.removeEventListener('resize', handleOverflowCheck)
      }
    }
  }, [data])

  return (
    <div className={styles['content']}>
      <div className={styles['content_title']}>
        <div className={styles['images']}>
          <Image
            src={data?.imageUrl}
            alt={data?.role + ' ' + data?.title}
            layout='fill'
            style={{ borderRadius: '16px' }}
            objectFit='contain'
            onClick={(e) => {
              e.stopPropagation()
              if (isDetail) {
                handleViewMore(data)
              }
            }}
          />
          {isDetail && (
            <MPButton
              className={styles['btnView']}
              onClick={(e) => {
                e.stopPropagation()
                handleViewMore(data)
              }}
            >
              Xem chi tiết
            </MPButton>
          )}
          <div className={styles['rating']}>
            <div className={styles['rate']}>
              <span>{data?.description?.rating?.rate}</span>
              <p>
                <AiFillStar color='#FFB54A' size={16} />
              </p>
            </div>
            <div className={styles['totalRate']}>
              <span>{data?.description?.bookingTotal || 100}</span>
              <p>
                <FaUser color='#FFB54A' size={14} />
              </p>
            </div>
          </div>
        </div>

        <div className={styles['content_title_int']} id='contentLeft'>
          <div className={styles['groupTitle']}>
            <span className={styles['img']}>
              <Image
                src={data?.imageUrl}
                alt={data?.role + ' ' + data?.title}
                width={60}
                height={60}
                style={{ borderRadius: '8px' }}
                layout='fixed'
                objectFit='contain'
                onClick={(e) => {
                  e.stopPropagation()
                  if (isDetail) {
                    handleViewMore(data)
                  }
                }}
              />
              {isDetail && (
                <MPButton
                  onClick={(e) => {
                    e.stopPropagation()
                    handleViewMore(data)
                  }}
                  className={styles['btnViewMobile']}
                >
                  Chi tiết
                </MPButton>
              )}
            </span>

            <h3 className={styles['title']}>
              {data?.role + ' '}
              <strong>{data?.title}</strong>
            </h3>
          </div>

          <div className={styles['time']}>
            <span>
              <Image
                src={imgSubject}
                alt='Icon Subject'
                width={14}
                height={14}
                layout='responsive'
              />
            </span>

            <div className={styles['address']}>
              <p>
                Chuyên khoa:{' '}
                {data?.subjects.map((item: any) => item.name)?.join(', ')}
              </p>
            </div>
          </div>

          <div className={styles['treatment']}>
            <span>
              <Image
                src={imgPin}
                alt='Icon Pin'
                layout='responsive'
                width={15}
                height={15}
              />
            </span>

            <div className={styles['treatmentDesc']}>
              <p
                className={cx(
                  styles['treatmentContent'],
                  overflowStates && styles['setContent']
                )}
              >
                Chuyên trị: {data?.treatments || 'Đang cập nhật...'}
              </p>
              {overflowStates && (
                <div
                  className={styles['arrow']}
                  onClick={() => setOpenModal(true)}
                >
                  <MdOutlineDoubleArrow
                    color='#00e0ff'
                    style={{ marginTop: '4px' }}
                  />
                </div>
              )}
            </div>
          </div>

          <div className={styles['time']}>
            <span>
              <Image
                src={calendar}
                alt='Icon Calendar'
                layout='responsive'
                width={14}
                height={14}
              />
            </span>

            <div className={styles['address']}>
              <p>Lịch khám: {data?.days}</p>
            </div>
          </div>
          <div className={styles['time']}>
            <span>
              <Image
                src={pay}
                alt='Icon Pay'
                layout='responsive'
                width={14}
                height={14}
              />
            </span>

            <div className={styles['address']}>
              <p>Giá khám: {price}</p>
            </div>
          </div>
          <div className={styles['desktop']}>
            <div className={styles['btnControl']}>
              <div className={styles['rating']}>
                <div className={styles['rate']}>
                  <span>{data?.description?.rating?.rate}</span>
                  <p>
                    <AiFillStar color='#FFB54A' size={16} />
                  </p>
                </div>
                <div className={styles['totalRate']}>
                  <span>{data?.description?.bookingTotal || 100}</span>
                  <p>
                    <FaUser color='#FFB54A' size={14} />
                  </p>
                </div>
              </div>
              <MPButton
                onClick={(e) => {
                  e.stopPropagation()
                  if (data.description.disabled) {
                    setWarningBooking(true)
                    return
                  }
                  if (data?.description?.notiBookingTelemed) {
                    setOpenBookingTelemed(true)
                    return
                  } else {
                    handleBooking(data)
                  }
                }}
                className={cx(styles['btnBooking'])}
              >
                Đặt khám ngay
              </MPButton>
            </div>
          </div>
        </div>
      </div>
      {openModal && (
        <Modal
          title={'Chuyên trị'}
          open={openModal}
          footer={null}
          centered
          onCancel={() => setOpenModal(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.treatments
            }}
          />
        </Modal>
      )}
      {openBookingTelemed && (
        <Modal
          title={'Thông báo'}
          open={openBookingTelemed}
          okText='Đồng ý'
          onOk={()=>handleBooking(data)}
          centered
          onCancel={() => setOpenBookingTelemed(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.description?.notiBookingTelemed
            }}
          />
        </Modal>
      )}
      {warningBooking && (
        <Modal
          title={'Thông báo'}
          open={warningBooking}
          footer={null}
          centered
          onCancel={() => setWarningBooking(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.description.message
            }}
          />
        </Modal>
      )}
    </div>
  )
}

export default DoctorCard
