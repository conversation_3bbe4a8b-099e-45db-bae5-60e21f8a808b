import React from 'react'
import styles from './styles.module.less'
import { MPHospitalBillInfo } from '@medpro-libs/libs'
import cx from 'classnames'
import MPBillCodeInfo from 'libs/medpro-component-libs/src/lib/ui/MPBillCodeCard/common/MPBillCodeInfo'
import { getTimezoneBooking } from 'libs/medpro-component-libs/src/lib/ui/MPBookingInfoCard/common/func'
import { size } from 'lodash'
export const ExamPrint = ({ bookingBill }) => {
  const { bookingInfo, checkInRoom } = bookingBill
  const [DATE, TIME] = getTimezoneBooking({
    date: bookingInfo?.date,
    time: bookingInfo?.timeStr,
    awaitMessage: bookingInfo?.awaitMessage,
    waitingConfirmDate: bookingInfo?.waitingConfirmDate
  })
  const isRenderDateTime = ![-2, 0, 6].includes(bookingInfo?.status)
  const titleBookingTime =
    bookingInfo?.partnerId === 'trungvuong'
      ? 'G<PERSON><PERSON> tiếp nhận dự kiến'
      : '<PERSON><PERSON><PERSON> khám dự kiến'
  const isRenderPhiTamUng = bookingInfo?.service?.advanced
    ? 'Phí tạm ứng:'
    : 'Phí khám:'
  const onShowBox = (bookingInfo: any) => {
    return [
      bookingInfo?.room?.name,
      bookingInfo?.subject?.name,
      bookingInfo?.room?.sectionName,
      bookingInfo?.checkInRoom?.sectionName
    ].some((item) => !!item)
  }
  return (
    <>
      <div className={styles['printBill_Print']}>
        <div className={styles['printBillTitle']}>
          <MPHospitalBillInfo data={bookingBill} />
          {/* Mã Phiếu khám bệnh */}
          <div className={styles['printBillCode']}>
            <div
              className={cx(
                styles['segmentCode'],
                !bookingInfo.displayCodeBooking.value && styles['center']
              )}
            >
              {bookingInfo.displayCodeBooking.value && (
                <MPBillCodeInfo
                  treeId={bookingInfo.treeId}
                  status={bookingInfo.bookingStatus}
                  code={bookingInfo.displayCodeBooking}
                />
              )}

              <div className={styles['segmentInfo']}>
                {bookingInfo.awaitMessage ? (
                  <div className={styles['awaitMessage']}>
                    <p>{titleBookingTime}</p>
                    <b>{bookingInfo.awaitMessage}</b>
                  </div>
                ) : (
                  <div className={styles['boxSegmentInfo']}>
                    <div className={styles['segmentInfoTime']}>
                      <p>{titleBookingTime}</p> <b>{TIME}</b>
                    </div>
                    {bookingInfo?.sequenceNumber && (
                      <div className={styles['segmentInfoSTT']}>
                        <p>Số thứ tự tiếp nhận</p>{' '}
                        <b>{bookingInfo?.sequenceNumber}</b>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {bookingInfo.bookingDescription}
          {bookingInfo?.totalPaymentMessage && (
            <div className={styles['segmentTotalFeeMessage']}>
              <p className={styles['totalPaymentMessage']}>
                {bookingInfo?.totalPaymentMessage}
              </p>
              <p className={styles['totalMessageExtra']}>
                {bookingInfo?.totalMessageExtra}
              </p>
            </div>
          )}
          {bookingInfo.cancelMessage && (
            <div
              className={styles['cancelMessage']}
              dangerouslySetInnerHTML={{
                __html: bookingInfo.cancelMessage
              }}
            />
          )}
          {bookingInfo.shortDescription &&
            bookingInfo.isRenderShortDescription && (
              <div
                className={styles['cancelMessage']}
                dangerouslySetInnerHTML={{
                  __html: bookingInfo.shortDescription
                }}
              />
            )}
          <div className={cx(styles['modalContentBill'])}>
            <ul>
              {onShowBox(bookingInfo) && (
                <div className={cx(styles['customerLabelHeader'])}>
                  {bookingInfo?.room?.name && (
                    <li>
                      <span className={styles['label']}>Phòng khám:</span>
                      <b className={styles['value']}>
                        {bookingInfo?.room?.name}
                      </b>
                    </li>
                  )}
                  {bookingInfo?.room?.sectionName ? (
                    <li>
                      <span className={styles['label']}>Khu vực:</span>
                      <b className={styles['value']}>
                        {bookingInfo?.room?.sectionName}
                      </b>
                    </li>
                  ) : (
                    bookingInfo?.checkInRoom?.sectionName && (
                      <li>
                        <span className={styles['label']}>Khu vực:</span>
                        <b className={styles['value']}>
                          {bookingInfo?.checkInRoom?.sectionName}
                        </b>
                      </li>
                    )
                  )}
                  {bookingInfo?.subject?.name && (
                    <li>
                      <span className={styles['label']}>Chuyên khoa:</span>
                      <article>{bookingInfo?.subject?.name}</article>
                    </li>
                  )}
                </div>
              )}
              <div className={styles['customerLabelBody']}>
                <li>
                  <span className={styles['label']}>Mã phiếu:</span>
                  <b>{bookingInfo?.bookingCode}</b>
                </li>
                {bookingInfo?.doctor?.name && (
                  <li>
                    <span className={styles['label']}>Bác sĩ:</span>
                    <article>{bookingInfo?.doctor?.name}</article>
                  </li>
                )}
                {bookingInfo?.service?.name &&
                  !['bvmathcm', 'dalieuhcm'].includes(
                    bookingInfo?.partnerId
                  ) && (
                    <li>
                      <span className={styles['label']}>Dịch vụ:</span>
                      <b>{bookingInfo?.service?.name}</b>
                    </li>
                  )}

                {bookingInfo?.service?.name && (
                  <li>
                    <span className={styles['label']}>Hình thức khám:</span>
                    <article>
                      {['bvmathcm'].includes(bookingInfo?.partnerId) ? (
                        <b> {bookingInfo?.service?.name}</b>
                      ) : (
                        <b>
                          {bookingInfo?.insuranceCode
                            ? ' Có BHYT'
                            : ' Không có BHYT'}
                        </b>
                      )}
                    </article>
                  </li>
                )}
                {isRenderDateTime && (
                  <>
                    <li>
                      <span className={styles['label']}>Thời gian khám:</span>
                      <article className={styles['greenText']}>
                        {TIME} - {DATE}
                      </article>
                    </li>
                  </>
                )}
                <li>
                  <span className={styles['label']}>{isRenderPhiTamUng}</span>
                  <article>{bookingInfo?.serviceInfo?.priceText}</article>
                </li>
                <li>
                  <span className={styles['label']}>Bệnh nhân:</span>
                  <article className={styles['greenText']}>
                    {`${bookingInfo?.patient?.surname} ${bookingInfo?.patient?.name}`}
                  </article>
                </li>
                <li>
                  <span className={styles['label']}>Ngày sinh:</span>
                  <article className={styles['greenText']}>
                    {bookingInfo?.patient?.birthdate ||
                      bookingInfo?.patient?.birthyear}
                  </article>
                </li>
                <li>
                  <span className={styles['label']}>Mã bệnh nhân:</span>
                  <article className={styles['greenText']}>
                    {bookingInfo?.patient?.patientCode}
                  </article>
                </li>
              </div>

              {size(bookingInfo?.addonServices) > 0 &&
                bookingInfo?.partnerId === 'bvsingapore' && (
                  <>
                    <div className={styles['line']}>Các dịch vụ khác :</div>
                    {bookingInfo?.addonServices.map(
                      (item: any, index: number) => {
                        return (
                          <li key={index}>
                            <span className={styles['label']}>{item.name}</span>
                            <article>{item.priceText}</article>
                          </li>
                        )
                      }
                    )}
                  </>
                )}
            </ul>
          </div>

          <div className={cx(styles['printBillFooter'])}>
            {[-2, 2, 0, 6].includes(bookingInfo?.status) ? null : (
              <div className={styles['noteBillContent']}>
                {checkInRoom?.description && (
                  <div className={styles['note']}>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: checkInRoom?.description
                      }}
                    />
                  </div>
                )}
                {bookingInfo?.bookingNote && (
                  <div
                    className={styles['note']}
                    dangerouslySetInnerHTML={{
                      __html: bookingInfo?.bookingNote
                    }}
                  />
                )}
              </div>
            )}
            <div className={cx(styles['copyRight'])}>
              <span>Bản quyền thuộc MEDPRO</span>
            </div>

            <div className={styles['introMedpro']}>
              Đặt lịch khám tại Bệnh viện - Phòng khám hàng đầu Việt Nam
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
