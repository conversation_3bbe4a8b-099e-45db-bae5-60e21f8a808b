import { MPCompleteProfileAppCompoment } from '../../component/MPCompleteProfileAppCompoment'
import MPCompleteProfileAppCard from '../../ui/MPCompleteProfileAppCard'

interface Props {
  onSubmit: (values: any) => void
  data: any
  onChangeAddress: (type: string, id: string) => void
  phoneLocale: any[]
  district: any[]
  ward: any[]
  partnerId?: any
}
export const MPCompleteProfileApp = ({
  onSubmit,
  data,
  onChangeAddress,
  phoneLocale,
  district,
  ward,
  partnerId
}: Props) => {
  return (
    <MPCompleteProfileAppCompoment
      onSubmit={onSubmit}
      onChangeAddress={onChangeAddress}
      render={(handleSubmit: any, handleChangeAddress: any) => (
        <MPCompleteProfileAppCard
          handleSubmit={handleSubmit}
          data={data}
          handleChangeAddress={handleChangeAddress}
          phoneLocale={phoneLocale}
          district={district}
          ward={ward}
          partnerId={partnerId}
        />
      )}
    />
  )
}
