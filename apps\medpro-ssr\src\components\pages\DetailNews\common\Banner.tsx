import React, { useEffect } from 'react'
import styles from './../styles.module.less'
import cx from 'classnames'
import Image from 'next/image'
import Link from 'next/link'
import { customLoader } from '@medpro-libs/libs'
export const Banner = ({ isMobile, data }) => {
  const imageDefault =
    'https://cdn.medpro.vn/prod-partner/99dfda1a-76b4-4c23-87a4-e51f140b7486-default-skeleton.png'

  const [imageUrl, setImageUrl] = React.useState('')
  useEffect(() => {
    setImageUrl(
      (isMobile ? data?.mobile?.imageUrl : data?.desktop?.imageUrl) ?? ''
    )
  }, [data])
  return (
    <div className={cx(styles['BannerTop'])}>
      <Link href={data?.cta?.url ?? '#'}>
        <a target={data?.cta?.target ?? '_self'}>
          <Image
            loader={customLoader}
            src={isMobile ? data?.imageMobileUrl : data?.imageDesktopUrl}
            alt='Banner News'
            width={isMobile ? 396 : 1180}
            height={isMobile ? 100 : 250}
            layout='fixed'
            loading='lazy'
            placeholder='blur'
            blurDataURL={imageDefault}
            onError={() => {
              setImageUrl(imageDefault)
            }}
          />
        </a>
      </Link>
    </div>
  )
}
