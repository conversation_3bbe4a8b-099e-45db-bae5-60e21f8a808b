import { Modal, Steps } from 'antd'
import cx from 'classnames'
import { size } from 'lodash'
import { useRouter } from 'next/router'
import { useState } from 'react'
import styles from './styles.module.less'

interface BookingAppProps {
  steps: any
  current: number
  router: any
}
const MPBookingAppCard = (props: BookingAppProps) => {
  const router = useRouter()
  // const addSubject = router.query.addSubject as string
  const { Step } = Steps
  const [warning3CK, setWarning] = useState(false)
  return (
    <>
      <div className={styles['stepTitle']}></div>
      <Steps
        current={props.current}
        direction='horizontal'
        className={styles['stepsNav']}
        percent={100}
      >
        {props?.steps.map((item: any, index: number) => (
          <Step
            key={item.key}
            icon={item.icon}
            className={cx(
              index === size(props?.steps) - 1 ? styles['lastStep'] : '',
              index === 0 ? styles['firstStep'] : ''
            )}
          />
        ))}
      </Steps>

      <div className='steps-content'>
        {props?.steps[props.current]?.content}
      </div>
      <Modal
        title='Lưu ý'
        open={warning3CK}
        centered
        onCancel={() => setWarning(false)}
        onOk={() =>
          router.replace({
            pathname: '/dich-vu-y-te/dat-kham-tai-co-so'
          })
        }
        cancelText='Đóng'
        okText='Đồng ý'
        className={styles['modal']}
      >
        <p>
          Khi quay lại bạn sẽ mất các thông tin đã chọn, bạn có muốn quay lại
          không?
        </p>
      </Modal>
    </>
  )
}

export default MPBookingAppCard
