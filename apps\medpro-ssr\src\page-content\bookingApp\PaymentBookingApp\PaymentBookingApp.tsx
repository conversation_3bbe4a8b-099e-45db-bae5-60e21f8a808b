/* eslint-disable react/no-unescaped-entities */
import {
  getFormatMoney,
  getTranformPrice,
  isChoosePatientStepOne,
  MPButton,
  PageRoutes,
  Valid
} from '@medpro-libs/libs'
import { reserveBooking } from '@medpro-libs/medpro-booking'
import { <PERSON>lap<PERSON>, Modal } from 'antd'
import cx from 'classnames'
import format from 'libs/medpro-component-libs/src/lib/common/format'
import { first, get, identity, pickBy, size } from 'lodash'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { BsCalendar2DateFill } from 'react-icons/bs'
import { CgTrash } from 'react-icons/cg'
import {
  FaFileMedical,
  FaHandHoldingHeart,
  FaPhoneAlt,
  FaStethoscope,
  FaUser,
  FaUserMd,
  FaWallet
} from 'react-icons/fa'
import { GiAlarmClock } from 'react-icons/gi'
import { HiCheckBadge } from 'react-icons/hi2'
import { IoIosInformationCircleOutline, IoMdClose } from 'react-icons/io'
import { IoCheckmarkCircle } from 'react-icons/io5'
import { TiLocation } from 'react-icons/ti'
import { useDispatch } from 'react-redux'
import client from '../../../../config/medproSdk'
import {
  selectConfirmBookingData,
  selectFirstSchedule,
  selectMedproCare,
  selectPaymentFeeInfo,
  selectPaymentMethodQuery,
  selectPaymentMethods,
  selectSelectedMedproCare,
  selectSelectedPaymentMethod,
  selectSelectedPaymentType,
  selectTransactionId,
  selectTreeId
} from '../../../../store/booking/selector'
import {
  bookingActions,
  resetPayment,
  setSelectedPaymentMethod,
  setSelectedPaymentType
} from '../../../../store/booking/slice'
import { useAppSelector } from '../../../../store/hooks'
import { hospitalActions } from '../../../../store/hospital/hospitalSlice'
import {
  selectAppId,
  selectExtraConfig,
  selectPartnerId,
  selectPartnerInfo
} from '../../../../store/hospital/selector'
import {
  selectInsurance,
  selectPatient
} from '../../../../store/patient/patientSelector'
import { patientActions } from '../../../../store/patient/patientSlice'
import { showErrorNotification } from '../../../../utils/utils.error'
import { handleRedirectPayment } from '../../../components/pages/Booking/func'
import { PaymentAppSkeleton } from './PaymentAppSkeleton'
import styles from './style.module.less'

const valid = new Valid()
interface PaymentBookingAppProps {
  next?: () => void
  current: number
}

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }

interface DrawerContentItem {
  data: any
  extraContent?: any
  title: string
  open: boolean
}

export const ConfirmPaymentBookingApp = ({
  next,
  current
}: PaymentBookingAppProps) => {
  const router = useRouter()
  const findExtra = router.query.findExtra as string
  const step = router.query.step as string
  const dispatch = useDispatch()
  const appId = useAppSelector(selectAppId)
  const partnerInfo = useAppSelector(selectPartnerInfo)
  const { Panel } = Collapse
  // Open module card
  const [openCardPay, setOpenCardPay] = useState(false)
  const treeId = useAppSelector(selectTreeId)
  const multiSchedules = useAppSelector((state) => state.booking.multiSchedules)
  const schedule = useAppSelector(selectFirstSchedule)
  const confirmData = useAppSelector(selectConfirmBookingData)
  const selectedPatient = useAppSelector(
    (state) => state.patient.selectedPatient
  )
  const bhytInstructionFormData = useAppSelector(
    (s) => s.filterCheck.bhytInstructionFormData
  )
  const partnerId = useAppSelector(selectPartnerId)
  const paymentMethods = useAppSelector(selectPaymentMethods)
  const paymentMethodQuery = useAppSelector(selectPaymentMethodQuery)
  const selectedPaymentType = useAppSelector(selectSelectedPaymentType)
  const selectedPaymentMethod = useAppSelector(selectSelectedPaymentMethod)
  const patient = useAppSelector(selectPatient)
  const insurance = useAppSelector(selectInsurance)
  const paymentFeeInfo = useAppSelector(selectPaymentFeeInfo)
  const listMedproCare = useAppSelector(selectMedproCare)
  const selectedMedproCare = useAppSelector(selectSelectedMedproCare)
  const loading = useAppSelector((s) => s.total.loading)
  const isLoading = size(paymentMethods) <= 0
  const [dataSearch, setDataSearch] = useState<any>([])
  const [activeContent, setActiveContent] = useState<any>(false)
  const [referralCode, setReferralCode] = useState(undefined)
  const reExam = useAppSelector((s) => s.reexam.data?.reExam)
  const transactionId = useAppSelector(selectTransactionId)
  // eslint-disable-next-line @typescript-eslint/no-var-requires

  const filterCheckData = useAppSelector((s) => s.filterCheck.nextStepData)
  const [modalNote, setModalNote] = useState(false)
  const [isReserving, setIsReserving] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [selectedDeleteItem, setSelectedDeleteItem] = useState<any>()
  const [openDetailMethod, setOpenDetailMethod] = useState<any>({
    open: false,
    data: {}
  })
  const [ipAddress, setIPAddress] = useState('')
  const { schedulesSelected } = confirmData
  const extraConfig = useAppSelector(selectExtraConfig)
  const displayDetail = schedulesSelected?.[0]?.service?.displayDetail
  const priceService = paymentFeeInfo.reduce((p, c) => {
    p += c?.service?.price
    return p
  }, 0)
  const tempGrandTotal =
    priceService + (selectedMedproCare?.addonServices[0]?.price || 0)

  let sumGrandTotal = 0
  if (selectedMedproCare?.partner2bill) {
    sumGrandTotal = selectedPaymentType
      ? selectedPaymentType?.grandTotal
      : priceService
  } else {
    sumGrandTotal = selectedPaymentType
      ? selectedPaymentType?.grandTotal
      : tempGrandTotal
  }

  const patientInfo = selectedPatient
  const fullName =
    patientInfo?.fullname || `${patientInfo?.surname} ${patientInfo?.name}`
  const PaymentInfo = schedulesSelected
  const advance = get(confirmData, 'schedulesSelected[0].service.advanced', 0)
  const limitBookingByTreeId = get(
    extraConfig,
    `treeId.${treeId?.toLowerCase()}.bookingLimit`
  )

  const infoLine2 = PaymentInfo[0]?.service?.infoLine2

  const codePaymentType = selectedPaymentType?.code

  useEffect(() => {
    if (size(paymentMethods) > 0) {
      selectPaymentMethod({
        item: {
          paymentType: first(paymentMethods).paymentTypes[0],
          paymentMethod: first(paymentMethods)
        }
      })
    }
  }, [paymentMethods])

  useEffect(() => {
    if (transactionId) {
      router.push(
        `/${PageRoutes.booking.detail.path}?mpTransaction=${transactionId}`
      )
    }
    dispatch(patientActions.getPatientDetail())
    dispatch(hospitalActions.getPartnerInfo())
  }, [])

  useEffect(() => {
    if (partnerId) {
      dispatch(bookingActions.getPaymentMethods())
    }
  }, [paymentMethodQuery, partnerId])

  useEffect(() => {
    if (step === 'xac-nhan-thong-tin') {
      let params = {}
      if (schedule) {
        const { serviceId, subjectId, doctorId, roomId, date, timeslot } =
          schedule || {}
        const dateStr = `${moment(date).format('YYYY-MM-DD')} ${
          timeslot?.startTime
        }`
        const bookingDate = moment(dateStr, 'YYYY-MM-DD HH:mm').toISOString()
        params = { serviceId, subjectId, doctorId, roomId, bookingDate, treeId }
      }
      dispatch(bookingActions.getMedproCare(params))
    }
  }, [step, schedule])

  useEffect(() => {
    if (findExtra) {
      delete router.query.findExtra
    }
  }, [router.query.step])

  // useEffect(() => {
  //   if ([current].includes(STEP_BOOKING.XAC_NHAN_THONG_TIN)) {
  //     dispatch(resetPayment())
  //   }
  // }, [current])

  useEffect(() => {
    if (partnerId) {
      dispatch(bookingActions.getPaymentMethods())
    }
  }, [paymentMethodQuery, partnerId])

  const isMulti = multiSchedules.length > 1

  const handleOpenNoti = () => {
    setModalVisible(true)
  }
  const actionScroll = () => {
    const payment = document.getElementById('payment') as HTMLElement
    payment?.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
      inline: 'nearest'
    })
  }

  const getIPAddress = () => {
    fetch('https://api.ipify.org?format=json')
      .then((response) => response.json())
      .then((data) => {
        setIPAddress(data.ip)
      })
      .catch((error) => {
        console.error('Error fetching IP address:', error)
      })
  }

  const renderLabelPayment = () => {
    switch (true) {
      case treeId !== 'CSKH' && partnerId === 'bvsingapore':
        return 'Phí tư vấn trực tuyến với bệnh viện SGH'
      case codePaymentType === 'fundiin':
        return 'Phí tiện ích + Phí thanh toán hộ qua Fundiin'
      default:
        return 'Phí nền tảng + Phí TGTT'
    }
  }

  const selectPaymentMethod = async ({ item }: any) => {
    const { paymentType, paymentMethod } = item
    await dispatch(setSelectedPaymentMethod(paymentMethod))
    await dispatch(setSelectedPaymentType(paymentType))
    if (paymentType) {
      actionScroll()
    }
  }
  const toggleClose = () => {
    setModalNote(false)
  }
  const handleNoteOpen = () => {
    setModalNote(true)
  }
  const onOkReserveBooking = async () => {
    let paramsVisa
    if (selectedPaymentType?.checkEmail) {
      paramsVisa = {
        customerIpAddress: ipAddress,
        browserScreenHeight: window.innerHeight,
        browserScreenWidth: window.innerWidth
      }
    }
    try {
      await reserveBooking({
        partnerId,
        isMulti,
        handleRedirectPayment: (data: any) =>
          handleRedirectPayment({
            data,
            router,
            dispatch,
            appId
          }),
        selectedPaymentMethod,
        selectedPaymentType,
        multiSchedules,
        patient,
        treeId,
        insurance,
        reExam,
        redirectUrl: `${window.location.origin}${PageRoutes.booking.detail.path}`,
        client,
        bhytInstructionFormData,
        filterCheckData,
        medproCare: selectedMedproCare,
        ...paramsVisa
        // referralCode
      })
    } catch (err) {
      showErrorNotification(err)
    }
  }
  const onDeleteBooking = (data: any) => {
    setSelectedDeleteItem(data)
  }
  const onConfirmDelete = () => {
    // khi chỉ có 1 CK thì reset patient để chọn lại.
    if (multiSchedules.length === 1 && !isChoosePatientStepOne(extraConfig)) {
      dispatch(patientActions.resetSelectPatient())
      dispatch(bookingActions.resetBookingFlow({}))
      router.replace(
        {
          pathname: router.pathname,
          query: pickBy(
            {
              feature: router?.query?.feature,
              treeId: router?.query?.treeId,
              partnerId: router?.query?.partnerId,
              step: 'chon-thong-tin-kham'
            },
            identity
          )
        },
        undefined,
        { shallow: true }
      )
    }
    dispatch(bookingActions.removeSchedule(selectedDeleteItem))
    setSelectedDeleteItem(undefined)
    dispatch(resetPayment())
  }
  const handeDataInfoDelete = (data) => {
    const propsIcon = {
      size: '16',
      color: '#1da1f2'
    }

    return [
      {
        status: data?.subject?.name,
        title: 'Chuyên khoa',
        value: data?.subject?.name,
        icon: <FaStethoscope {...propsIcon} />
      },
      {
        status: data?.service?.name,
        title: 'Dịch vụ',
        value: data?.service?.name,
        icon: <FaFileMedical {...propsIcon} />
      },
      {
        status: !!data?.time,
        title: 'Ngày khám',
        value: `${moment(data?.date).format('DD/MM')}  (${
          data?.time?.startTime
        } - ${data?.time?.endTime})`,
        icon: <GiAlarmClock {...propsIcon} />
      }
    ]
  }

  const handlePayment = async () => {
    try {
      setIsReserving(true)
      await onOkReserveBooking()
    } finally {
      setIsReserving(false)
      window.dataLayer.push({
        event: 'Xác Nhận Phương Thức Thanh Toán',
        Action: 'Click',
        Category: 'Button-Action',
        Label: 'Đồng ý',
        Event: 'Xác Nhận Phương Thức Thanh Toán',
        PartnerId: partnerId,
        AppId: appId,
        UserId: ''
      })
    }
  }

  return (
    <>
      {loading ? (
        <PaymentAppSkeleton />
      ) : (
        <>
          <div className={styles['ConfirmPaymentBookingApp']}>
            <div className={styles['BoxContentBooking']}>
              <div className={styles['hospitalTitle']}>
                <h1>
                  {partnerInfo?.name}
                  {partnerInfo?.listingPackagePaid && (
                    <HiCheckBadge color='#0097FF' size={14} />
                  )}
                </h1>
                <p>{partnerInfo?.address}</p>
              </div>

              <div className={styles['patientInfo']}>
                <h3>Thông tin bệnh nhân</h3>
                <Collapse
                  ghost
                  className={styles['collapse']}
                  expandIconPosition='end'
                >
                  <Panel
                    header={
                      <span className={styles['collapseHeader']}>
                        <FaUser size={18} color='#11A2F3' />
                        {fullName}
                      </span>
                    }
                    key='1'
                  >
                    <p>
                      <FaPhoneAlt size={18} color='#11A2F3' />
                      {format.concerPhone(patientInfo?.mobile)}
                    </p>
                    <p>
                      <BsCalendar2DateFill size={18} color='#11A2F3' />
                      {patientInfo?.birthdate || patientInfo?.birthyear}
                    </p>
                    <p style={{ alignItems: 'flex-start' }}>
                      <TiLocation
                        size={20}
                        color='#11A2F3'
                        style={{ scale: '1.2' }}
                      />
                      {patientInfo?.fullAddress}
                    </p>
                  </Panel>
                </Collapse>
              </div>
              <div className={styles['informationBooking']}>
                <h3>Thông tin đặt khám</h3>
                <div className={styles['bookingInfo']}>
                  {PaymentInfo?.map((item: any, index) => {
                    return (
                      <div key={index} className={styles['bookingInfoItem']}>
                        {item?.doctor?.name && (
                          <p>
                            <FaUserMd size={18} color='#11A2F3' />
                            {`${item?.doctor?.role} ${item?.doctor?.name}` ||
                              'Đang cập nhật'}
                          </p>
                        )}
                        {item?.subject?.name && (
                          <p>
                            <FaStethoscope size={18} color='#11A2F3' />
                            {`${item?.subject?.name || 'Đang cập nhật'}`}
                          </p>
                        )}
                        {item?.service?.name && (
                          <p>
                            <FaHandHoldingHeart size={18} color='#11A2F3' />
                            {`${item?.service?.name || 'Đang cập nhật'}`}
                          </p>
                        )}
                        {item?.date ? (
                          <p>
                            <BsCalendar2DateFill size={18} color='#11A2F3' />
                            {moment(item?.date).format('DD/MM/YYYY')}
                            {item?.time?.startTime && (
                              <span>
                                ({item?.time?.startTime} - {item?.time?.endTime}
                                )
                              </span>
                            )}
                          </p>
                        ) : (
                          <p>
                            <BsCalendar2DateFill size={18} color='#11A2F3' />
                            Chờ cập nhật
                          </p>
                        )}
                        {item?.service && (
                          <p>
                            <FaWallet size={18} color='#11A2F3' />
                            <span
                              className={cx(
                                styles['priceBooking'],
                                displayDetail && styles['displayDetail']
                              )}
                            >
                              {displayDetail ||
                                getFormatMoney(item?.service?.price) + ' đ'}
                            </span>
                          </p>
                        )}

                        {limitBookingByTreeId > 1 && (
                          <div className={cx(styles['specialistItem'])}>
                            <div className={styles['buttonDelete']}>
                              <MPButton
                                type='default'
                                className={styles['button']}
                                onClick={() => onDeleteBooking({ item, index })}
                              >
                                <span style={{ margin: 0 }}>
                                  <CgTrash color='#FF3B30' size={14} />
                                  &nbsp;
                                </span>
                                <span>Xóa</span>
                              </MPButton>
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  })}
                  {infoLine2 && partnerId === 'bvsingapore' && (
                    <div className={styles['bookingInfoItem']}>
                      <p>
                        <FaStethoscope size={18} color='#11A2F3' />
                        {infoLine2.roomName}
                      </p>
                      <p>
                        <FaHandHoldingHeart size={18} color='#11A2F3' />
                        {infoLine2.serviceName}
                      </p>
                      <p>
                        <FaWallet size={18} color='#11A2F3' />
                        {getFormatMoney(infoLine2?.price)}
                      </p>
                      <p>
                        <BsCalendar2DateFill size={18} color='#11A2F3' />
                        {moment(PaymentInfo?.[0]?.date).format('DD/MM/YYYY')}
                        {PaymentInfo[0]?.time?.startTime && (
                          <span>
                            ({PaymentInfo[0]?.time?.startTime} -{' '}
                            {PaymentInfo[0]?.time?.endTime})
                          </span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {size(selectedPaymentType) > 0 && (
              <>
                <div className={styles['customTotalPrice']}>
                  <div className={styles['customExamination']}>
                    <p>
                      Tiền khám
                      <IoIosInformationCircleOutline
                        width={12}
                        height={12}
                        color='#11a2f3'
                        onClick={handleNoteOpen}
                      />
                    </p>

                    <div className={styles['text']}>
                      {getTranformPrice({
                        displayDetail: displayDetail,
                        price:
                          getFormatMoney(
                            selectedPaymentType.subTotal - advance
                          ) + ' đ'
                      })}
                    </div>
                  </div>
                  {!!advance && (
                    <div className={styles['customExamination']}>
                      <p>Tạm ứng </p>
                      <div className={styles['text']}>
                        {getFormatMoney(advance) + ' đ'}
                      </div>
                    </div>
                  )}
                  {selectedPaymentType?.medproCareFee &&
                  !selectedMedproCare?.partner2bill ? (
                    <div className={styles['customExamination']}>
                      <p>
                        Dịch vụ đặt thêm
                        {selectedPaymentType?.medproCareFee &&
                          listMedproCare?.medproCareNote && (
                            <IoIosInformationCircleOutline
                              width={12}
                              height={12}
                              color='#11a2f3'
                              onClick={handleNoteOpen}
                            />
                          )}
                      </p>

                      <div className={styles['text']}>
                        {getFormatMoney(selectedPaymentType?.medproCareFee) +
                          ' đ'}
                      </div>
                    </div>
                  ) : null}
                  <div className={styles['customFee']}>
                    <div className={styles['group_fee']}>
                      <p>
                        {renderLabelPayment()}
                        <IoIosInformationCircleOutline
                          width={12}
                          height={12}
                          color='#11a2f3'
                          style={{ minWidth: '16px' }}
                          onClick={handleNoteOpen}
                        />
                      </p>
                      <div className={styles['text']}>
                        {`${selectedPaymentType?.totalFee?.toLocaleString(
                          'vi-VN'
                        )} đ`}
                      </div>
                    </div>
                    {extraConfig?.discountUMCGroup.includes(
                      (router.query.partnerId as string) || partnerId
                    ) && (
                      <sup className={styles['note_discount_fee_umc']}>
                        Giảm 2.000đ, ưu đãi từ Medpro & UMC
                      </sup>
                    )}
                  </div>
                  <div className={styles['customTotal']}>
                    <p className={styles['label']}>Tổng tiền</p>
                    <div className={styles['text']}>
                      {getFormatMoney(sumGrandTotal)} đ
                    </div>
                  </div>
                </div>
                {selectedPaymentMethod?.agreement && (
                  <div className={styles.acceptPaymentLabel}>
                    <IoCheckmarkCircle width={24} height={24} color='#52C41A' />
                    <div className={styles.checkboxText}>
                      {selectedPaymentMethod?.agreement}
                    </div>
                  </div>
                )}
                <div id='payment' />
              </>
            )}
          </div>
        </>
      )}

      <div className={styles['customButtonNext']}>
        <MPButton
          htmlType='submit'
          className={styles['mpButton']}
          onClick={handlePayment}
          loading={isReserving}
        >
          Thanh toán
        </MPButton>
      </div>

      {/* ===== Popup Ghi chú ======*/}
      {modalNote && (
        <div style={{ borderRadius: '16px' }}>
          <Modal
            centered
            open={modalNote}
            className={styles['customModalNoteModule']}
            bodyStyle={{ padding: '12px' }}
            destroyOnClose={false}
            closable={false}
            footer={null}
            onCancel={toggleClose}
          >
            <div className={styles['customModalNote']}>
              <div className={styles['customModalHeader']}>
                <span>Ghi chú</span>
                <IoMdClose
                  className={styles['iconCloseModule']}
                  onClick={toggleClose}
                />
              </div>
              <div className={styles['customModalBody']}>
                {selectedPaymentType?.subTotalNote && (
                  <>
                    <p>Tiền khám</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedPaymentType?.subTotalNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
                {selectedPaymentType?.medproCareFee &&
                  listMedproCare?.medproCareNote && (
                    <>
                      <p>Dịch vụ đặt thêm</p>
                      <div
                        dangerouslySetInnerHTML={{
                          __html: listMedproCare?.medproCareNote
                        }}
                        className={styles['customModalBody_Content']}
                      />
                    </>
                  )}
                {selectedPaymentType?.totalFeeNote && (
                  <>
                    <p>{renderLabelPayment()}</p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedPaymentType?.totalFeeNote
                      }}
                      className={styles['customModalBody_Content']}
                    />
                  </>
                )}
              </div>
            </div>
          </Modal>
        </div>
      )}
      {/* ===== Popup Xác nhận thanh toán ======*/}
      {modalVisible && (
        <Modal
          title={''}
          open={modalVisible}
          centered
          onOk={handlePayment}
          okText={isReserving ? 'Đang xử lý...' : 'Đồng ý'}
          cancelButtonProps={{ style: { display: 'none' } }}
          okButtonProps={{
            loading: isReserving
          }}
          className={styles['modalConfirmPayment']}
        >
          <div>
            <div className={styles['customModalHeader']}>
              <span>Thông báo</span>
              <IoMdClose
                className={styles['iconCloseModule']}
                onClick={() => setModalVisible(false)}
              />
            </div>
            <div className={styles['title']}>
              Bạn đang thực hiện <b> {selectedPaymentType?.name}</b> với số tiền{' '}
              <b> {getFormatMoney(selectedPaymentType?.grandTotal)} đ</b>{' '}
            </div>
          </div>
        </Modal>
      )}
      {/* ===== Popup chi tiết thanh toán ======*/}
      {openDetailMethod.open && (
        <Modal
          title={''}
          open={openDetailMethod.open}
          centered
          className={styles['modalConfirmPayment']}
          okText='Đóng'
          onOk={() => {
            setOpenDetailMethod((preState) => ({
              ...preState,
              open: false
            }))
          }}
          cancelButtonProps={{ hidden: true }}
        >
          <div>
            <div className={styles['customModalHeader']}>
              <span>{openDetailMethod?.data?.labelPopup}</span>
            </div>
            <div
              className={styles['descriptionDetail']}
              dangerouslySetInnerHTML={{
                __html: openDetailMethod?.data?.contentPopup
              }}
            />
          </div>
        </Modal>
      )}
      {/* ===== Popup xác nhận thanh toán ======*/}
      <Modal
        title={'Bạn có muốn xoá thông tin đặt khám ?'}
        open={!!selectedDeleteItem}
        onOk={() => onConfirmDelete()}
        onCancel={() => setSelectedDeleteItem(undefined)}
        className={styles['modal']}
        centered
        cancelText='Hủy'
        okText='Xác nhận'
      >
        <ul className={styles['listInfoSchedule']}>
          {selectedDeleteItem &&
            handeDataInfoDelete(selectedDeleteItem?.item).map(
              (v: any, i = 0) => {
                return (
                  v.status && (
                    <li className={styles['item']} key={i}>
                      <span className={styles['key']}>
                        {v?.icon && (
                          <span className={styles['icon']}>{v?.icon}</span>
                        )}
                        {v?.title}
                      </span>
                      <span className={styles['value']}>{v?.value}</span>
                    </li>
                  )
                )
              }
            )}
        </ul>
      </Modal>
    </>
  )
}
