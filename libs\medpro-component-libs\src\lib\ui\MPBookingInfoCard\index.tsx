import React from 'react'
import styles from './styles.module.less'
import cx from 'classnames'
// import BookingTime from './common/BookingTime'
import { getTimezoneBooking } from './common/func'
import { size } from 'lodash'
import Link from 'next/link'
import { IoMdOpen } from 'react-icons/io'
import { BOOKING_STATUS } from '../../common/constant'

const MPBookingInfoCard = ({ data, isCSKHApp, viewBooking }: any) => {
  const [DATE, TIME] = getTimezoneBooking({
    date: data?.bookingInfo?.date,
    time: data?.bookingInfo?.timeStr,
    awaitMessage: data?.bookingInfo?.awaitMessage,
    waitingConfirmDate: data?.bookingInfo?.waitingConfirmDate
  })
  const isRenderDateTime =
    ![-2, 0, 6].includes(data?.bookingInfo?.status) || isCSKHApp

  const isRenderPhiTamUng = data?.bookingInfo?.service?.advanced
    ? '<PERSON><PERSON> tạm ứng:'
    : 'Phí khám:'

  const serviceObj =
    data?.bookingInfo?.serviceInfo || data?.bookingInfo?.service
  // const titleBookingTime =
  //   data?.bookingInfo?.partnerId === 'trungvuong'
  //     ? 'Giờ tiếp nhận dự kiến'
  //     : 'Giờ khám dự kiến'
  const onShowBox = (bookingInfo: any) => {
    return [
      bookingInfo?.room?.name,
      bookingInfo?.subject?.name,
      bookingInfo?.room?.sectionName,
      bookingInfo?.checkInRoom?.sectionName
    ].some((item) => !!item)
  }
  return (
    <>
      <div className={cx(styles['modalContentBill'])}>
        {data?.bookingInfo?.hasResults && (
          <div className={styles['linkResult']}>
            <Link
              href={`/tra-ket-qua-kham-benh?patientId=${data?.bookingInfo?.patientId}&userId=${data?.bookingInfo.userId}&partnerId=${data?.bookingInfo.partnerId}&bookingId=${data?.bookingInfo.idBooking}`}
            >
              <a>Kết quả khám</a>
            </Link>
            <IoMdOpen color='#1da1f2' size={15} />
          </div>
        )}
        <ul>
          {onShowBox(data?.bookingInfo) && (
            <div
              className={cx(
                styles['customerLabelHeader'],
                ![
                  BOOKING_STATUS.CHUA_THANH_TOAN,
                  BOOKING_STATUS.THANH_TOAN_HO
                ].includes(data?.bookingInfo?.status) &&
                  styles['BoxCustomerLabelHeader'],
                viewBooking && styles['BoxCustomerLabelHeader']
              )}
            >
              {data?.bookingInfo?.room?.name && (
                <li>
                  <span className={styles['label']}>Phòng khám:</span>
                  <b className={styles['value']}>
                    {data?.bookingInfo?.room?.name}
                  </b>
                </li>
              )}
              {data?.bookingInfo?.room?.sectionName ? (
                <li>
                  <span className={styles['label']}>Khu vực:</span>
                  <b className={styles['value']}>
                    {data?.bookingInfo?.room?.sectionName}
                  </b>
                </li>
              ) : (
                data?.bookingInfo?.checkInRoom?.sectionName && (
                  <li>
                    <span className={styles['label']}>Khu vực:</span>
                    <b className={styles['value']}>
                      {data?.bookingInfo?.checkInRoom?.sectionName}
                    </b>
                  </li>
                )
              )}
              {data?.bookingInfo?.subject?.name && (
                <li>
                  <span className={styles['label']}>Chuyên khoa:</span>
                  <article>{data?.bookingInfo?.subject?.name}</article>
                </li>
              )}
            </div>
          )}
          <div className={styles['customerLabelBody']}>
            <li>
              <span className={styles['label']}>Mã phiếu:</span>
              <b>{data?.bookingInfo?.bookingCode}</b>
            </li>
            {data?.bookingInfo?.doctor?.name && (
              <li>
                <span className={styles['label']}>Bác sĩ:</span>
                <article>{data?.bookingInfo?.doctor?.name}</article>
              </li>
            )}
            {data?.bookingInfo?.service?.name &&
              !['bvmathcm', 'dalieuhcm'].includes(
                data?.bookingInfo?.partnerId
              ) && (
                <li>
                  <span className={styles['label']}>Dịch vụ:</span>
                  <b>{data?.bookingInfo?.service?.name}</b>
                </li>
              )}

            {data?.bookingInfo?.service?.name && (
              <li>
                <span className={styles['label']}>Hình thức khám:</span>
                <article>
                  {['bvmathcm'].includes(data?.bookingInfo?.partnerId) ? (
                    <b> {data?.bookingInfo?.service?.name}</b>
                  ) : (
                    <b>
                      {data?.bookingInfo?.insuranceCode
                        ? ' Có BHYT'
                        : ' Không có BHYT'}
                    </b>
                  )}
                </article>
              </li>
            )}

            {isRenderDateTime && (
              <>
                <li>
                  <span className={styles['label']}>Thời gian khám:</span>
                  <article className={styles['greenText']}>
                    {TIME} - {DATE}
                  </article>
                </li>
              </>
            )}
            <li>
              <span className={styles['label']}>{isRenderPhiTamUng}</span>
              <article>{serviceObj?.priceText}</article>
            </li>
            <li>
              <span className={styles['label']}>Bệnh nhân:</span>
              <article className={styles['greenText']}>
                {`${data?.bookingInfo?.patient?.surname} ${data?.bookingInfo?.patient?.name}`}
              </article>
            </li>
            <li>
              <span className={styles['label']}>Ngày sinh:</span>
              <article className={styles['greenText']}>
                {data?.bookingInfo?.patient?.birthdate ||
                  data?.bookingInfo?.patient?.birthyear}
              </article>
            </li>
            <li>
              <span className={styles['label']}>Mã bệnh nhân:</span>
              <article className={styles['greenText']}>
                {data?.bookingInfo?.patient?.patientCode}
              </article>
            </li>
          </div>

          {size(data?.bookingInfo?.addonServices) > 0 &&
            data?.bookingInfo?.partnerId === 'bvsingapore' && (
              <>
                <div className={styles['line']}>Các dịch vụ khác :</div>
                {data?.bookingInfo?.addonServices.map(
                  (item: any, index: number) => {
                    return (
                      <li key={index}>
                        <span className={styles['label']}>{item.name}</span>
                        <article>{item.priceText}</article>
                      </li>
                    )
                  }
                )}
              </>
            )}
        </ul>
      </div>
    </>
  )
}

export default MPBookingInfoCard
