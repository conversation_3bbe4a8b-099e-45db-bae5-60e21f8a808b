.text(@fontsize: 0, @fontweight: 0, @lineheight: 0, @textalign: left, @color: #003553) {
  font-family: Roboto !important;
  font-size: @fontsize;
  font-weight: @fontweight;
  line-height: @lineheight;
  text-align: @textalign;
  color: @color;
}
.btnPrev,
.btnNext {
  @media (max-width: 576px) {
    width: 24px !important;
    height: 24px !important;
    svg {
      width: 16px !important;
      height: 16px !important;
    }
  }
  position: absolute;
  top: 50% !important;
  transform: translateY(-50%);
  width: 35px !important;
  height: 35px !important;
  border-radius: 50%;
  background: #fff !important;
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.04),
    0px 2px 6px 0px rgba(0, 0, 0, 0.04), 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(5px);
  display: flex !important;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  &:hover {
    background: #ffffff;
  }
}
.btnNext {
  @media (max-width: 1024px) {
    right: -27px !important;
  }
  @media (max-width: 768px) {
    right: -18px !important;
  }
  @media (max-width: 576px) {
    right: -12px !important;
  }

  right: -37.5px !important;
}

.btnPrev {
  @media (max-width: 1024px) {
    left: -27px !important;
  }
  @media (max-width: 768px) {
    left: -18px !important;
  }
  @media (max-width: 576px) {
    left: -12px !important;
    // top: 74px;
  }

  left: -37.5px !important;
}
.NewDoctorTelemed {
  margin-bottom: 50px;
  @media (max-width: 576px) {
    margin-bottom: 26px;
  }
  .DoctorTelemedHeader {
    margin-bottom: 8px;
    @media (max-width: 567px) {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .viewAll {
        margin-bottom: 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 18.75px;
      }
    }
    .DoctorTelemedHeaderTitleText {
      h2 {
        text-transform: uppercase;
        margin-bottom: 0;
        .text(28px, 700, 32.81px, center);
        @media (max-width: 576px) {
          .text(18px, 700, 21px, left);
        }
      }
      p {
        text-transform: lowercase;
        font-size: 16px;
        font-weight: 400;
        line-height: 18.75px;
        text-align: left;
        margin-top: 4px;
        margin-bottom: 0;
      }
    }
  }
  .BodyCardDoctorTelemed {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    @media (max-width: 768px) {
      overflow-x: scroll;
    }
    @media (max-width: 576px) {
      padding: 0 0 24px;
      &::-webkit-scrollbar {
        display: none;
      }
    }
    :global {
      .ant-carousel {
        width: 100%;
        .slick-slider {
          .slick-list {
            .slick-track {
              padding: 24px 0;
              height: max-content;
              .slick-slide {
                display: flex;
                justify-content: center;
                @media (max-width: 576px) {
                  width: 100% !important;
                }
              }
            }
          }
          .slick-arrow {
            width: 32px;
            height: 32px;
            background-color: #ffffff;
            border-radius: 50%;

            &:before {
              display: none;
            }
          }
          .slick-disabled {
            cursor: no-drop;
          }
          .slick-next {
            right: -45px;
          }
          .slick-prev {
            left: -45px;
          }
        }
      }
    }
  }
  .NextButton {
    margin: 8px auto 0;
    display: flex;
    gap: 8px;
    &:hover {
      border-radius: 8px;
      border-color: #00b5f1;
      background-color: transparent;
    }
    p {
      .text(20px, 400, 23px, center,#00b5f1);
      margin-bottom: 0;
    }
  }
}
.NextButtonMobile {
  display: flex;
  gap: 8px;
  margin: 0;
  padding: 0 !important;
  &:hover {
    background-color: transparent;
  }
  p {
    .text(16px, 400, 18.75px, left,#00b5f1);
    margin-bottom: 0;
  }
}
