import { Col, Pagination, Row } from 'antd'
import cx from 'classnames'
import { useScroll } from 'framer-motion'
import { debounce, size } from 'lodash'
import router from 'next/router'
import { useEffect, useState } from 'react'
import MPHeaderHospitals from '../../shapes/MPHeaderHospitals'
import MPNewMedicalIntroduce from '../../shapes/MPNewMedicalIntroduce'
import MPContainer from '../MPContainer'
import { PackageSkeleton } from '../common/selekon/packageSkeleton'
import MedicalPackageItem from './common/MedicalPackageItem'
import styles from './styles.module.less'
import Image from 'next/image'
import derectionUp from '../../common/images/derectionUp.svg'
import PackageMobileItem from './common/PackageMobileItem'

export interface NewMedicalPackageProps {
  typeSearch: string[]
  keySearch: string
  provinces: any[]
  filteredPackages: any[]
  handleSearch: (item: any) => void
  handleSearchDebounce: (item: any) => void
  titlePage: string
  isHiddenFilter: boolean
  searchKeyWords: any[]
  setTypeSearch: any
  setSearching: any
  page: number
  searching: any
  selectedPackage: any
  setCount: any
  count: any
  loading: any
  handleSelect: (item: any) => void
  handleBooking: (item: any) => void
  handleViewMore: (item: any) => void
  pageIndex: number
  setPageIndex: any
  totalRows: number
  appInfo: any
  cityId: string
  isMobile: boolean
  handleSearchPackage: (item: any) => void
}

const PAGE_SIZE = 10
// let timeout: any
const MPNewMedicalPackageCard = (props: NewMedicalPackageProps) => {
  const { scrollY } = useScroll()
  const [hidden, setHidden] = useState(false)
  const [isHidden, setIsHidden] = useState(false)
  const update = () => {
    if (scrollY.get() > scrollY.getPrevious()) {
      setHidden(true)
    } else if (scrollY.get() < scrollY.getPrevious()) {
      setHidden(false)
    }
  }

  useEffect(() => {
    return scrollY.onChange(() => update())
  })
  const handleScroll = debounce(() => {
    const scrollTop = window.scrollY
    const idHospital: any = document.getElementById('top')
    const width = idHospital.getBoundingClientRect()

    if (scrollTop >= width.top) {
      setIsHidden(true)
    } else {
      setIsHidden(false)
    }
  }, 5)
  useEffect(() => {
    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])
  const renderDetailPackage = ({ device }: { device: string }) => {
    return (
      size(props.filteredPackages) > 0 && (
        <Col
          span={24}
          lg={{ span: 9, order: 2 }}
          className={cx(
            styles[`${device}`],
            hidden ? styles[`detailBottom`] : styles[`detailTop`]
          )}
        >
          <MPNewMedicalIntroduce
            selectedPackage={props.selectedPackage}
            downloadApp={props.appInfo}
          />
        </Col>
      )
    )
  }

  // const handleButton = (t: any) => {
  //   props.setTypeSearch((p: any) => {
  //     clearTimeout(timeout)
  //     if (p.includes(t)) {
  //       const newSet = new Set(p)
  //       newSet.delete(t)
  //       const newP = Array.from(newSet)
  //       props.setSearching(true)

  //       timeout = setTimeout(() => {
  //         void props.handleSearchPackage({
  //           kw: props.keySearch,
  //           tags: newP,
  //           pageIndex: props.page
  //         })
  //       }, 500)

  //       return newP
  //     } else {
  //       const newP = Array.from(new Set(p).add(t))
  //       props.setSearching(true)
  //       timeout = setTimeout(() => {
  //         void props.handleSearchPackage({
  //           kw: props.keySearch,
  //           tags: newP,
  //           pageIndex: props.page
  //         })
  //       }, 500)
  //       return newP
  //     }
  //   })
  // }

  const handleChangePagination = (p: number) => {
    props.setSearching(true)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
    props.setPageIndex(p)
    void props.handleSearchPackage({
      kw: props.keySearch,
      tags: props.typeSearch,
      pageIndex: p,
      cityId: props.cityId
    })

    router.replace(
      {
        pathname: router.pathname,
        query: { ...router.query, page: p }
      },
      undefined,
      { shallow: true }
    )
  }
  return (
    <div>
      <div className={styles['headerHospitals']}>
        <div id='top'></div>
        <MPHeaderHospitals
          hospital={props.keySearch}
          province={props.provinces}
          handleSearch={props.handleSearch}
          onSearch={props.handleSearchDebounce}
          title={props.titlePage}
          titleSearch='Tìm kiếm gói khám'
          subTitle='Gói khám sức khỏe đa dạng tại các Cơ sở Y tế uy tín đáp ứng mọi nhu cầu người dân'
          isHiddenFilter={props.isHiddenFilter}
          isHidden={isHidden}
        />
        {/* {size(props.searchKeyWords) > 0 ? (
          <ul className={cx(styles['tag'])}>
            {props.searchKeyWords.map((t, index) => {
              return (
                <li key={index}>
                  <MPButton
                    type='primary'
                    className={cx({
                      [styles['tagItem']]: true,
                      [styles['active']]: props.typeSearch.includes(t)
                    })}
                    onClick={() => handleButton(t)}
                  >
                    <h2>{t}</h2>
                  </MPButton>
                </li>
              )
            })}
          </ul>
        ) : (
          <></>
        )} */}
      </div>

      <MPContainer partner>
        <div className={styles['listHospital']}>
          {props.searching ? (
            <PackageSkeleton />
          ) : size(props.filteredPackages) > 0 ? (
            <>
              <Row gutter={26}>
                <Col span={24} lg={{ span: 15, order: 1 }}>
                  {size(props?.filteredPackages) > 0 ? (
                    <>
                      {props.filteredPackages?.map(
                        (item: any, index: number) => {
                          const isSelect = item.id === props.selectedPackage?.id
                          return (
                            <>
                              {!props.isMobile ? (
                                <MedicalPackageItem
                                  key={index}
                                  data={item}
                                  onSelect={() => {
                                    props.handleSelect(item)
                                  }}
                                  isSelect={isSelect}
                                  handleBooking={props.handleBooking}
                                  handleViewMore={props.handleViewMore}
                                />
                              ) : (
                                <PackageMobileItem
                                  key={index}
                                  data={item}
                                  onSelect={() => {
                                    props.handleSelect(item)
                                  }}
                                  index={index}
                                  isSelect={isSelect}
                                  handleBooking={props.handleBooking}
                                  handleViewMore={props.handleViewMore}
                                />
                              )}
                            </>
                          )
                        }
                      )}
                    </>
                  ) : (
                    <div className={styles['error']}>
                      Không có bệnh viện trả về
                    </div>
                  )}
                </Col>

                {renderDetailPackage({ device: 'desktop' })}
              </Row>
              <Row>
                {size(props.filteredPackages) > 0 && (
                  <Col
                    span={24}
                    lg={{ span: 24, order: 3 }}
                    style={{ textAlign: 'center' }}
                  >
                    <div className={styles['pagination']}>
                      <Pagination
                        pageSize={PAGE_SIZE}
                        current={props.pageIndex}
                        onChange={(p) => handleChangePagination(p)}
                        total={props.totalRows}
                        showSizeChanger={false}
                      />
                    </div>
                    {props.totalRows > props.count &&
                      (props.loading ? (
                        <div className={styles['loadingRing']}>
                          <div></div>
                          <div></div>
                          <div></div>
                          <div></div>
                        </div>
                      ) : (
                        <div
                          onClick={() =>
                            props.setCount((prev: any) => prev + PAGE_SIZE)
                          }
                          className={styles['viewMove']}
                        >
                          <p>Xem tiếp</p>
                          <Image
                            src={derectionUp}
                            width={13}
                            height={13}
                            objectFit='contain'
                            alt='Derection Up'
                            layout='fixed'
                          />
                        </div>
                      ))}
                  </Col>
                )}
              </Row>
            </>
          ) : (
            <div className={styles['error']}>
              <p>
                {size(props.keySearch) ||
                size(props.cityId) ||
                size(props.typeSearch)
                  ? 'Không tìm thấy gói khám cần tìm'
                  : 'Danh sách sẽ cập nhật trong thời gian tới'}
              </p>
              <img
                src={'/images/EmptyList.png'}
                alt='EmptyList'
                width={200}
                height={200}
              />
            </div>
          )}
        </div>
      </MPContainer>
    </div>
  )
}

export default MPNewMedicalPackageCard
