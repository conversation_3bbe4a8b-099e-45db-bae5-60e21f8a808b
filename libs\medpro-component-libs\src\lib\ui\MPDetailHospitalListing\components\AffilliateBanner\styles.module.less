.affiliateBannerWrapper {
  margin-top: 16px;
  width: 100%;
  display: none;

  @media only screen and (max-width: 1024px) {
    margin-top: 0;
  }

  &.right {
    @media only screen and (min-width: 1024px) {
      display: block;
    }
  }

  &.body {
    background: #ffffff;
    padding-top: 16px;
    padding-bottom: 12px;

    @media only screen and (max-width: 1024px) {
      display: block;
    }
    @media only screen and (max-width: 576px) {
      padding: 16px 0 0;
    }

    .title {
      h3 {
        font-weight: 500;
        font-size: 24px;
        line-height: 100%;
        color: #003553;
        @media (max-width: 576px) {
          font-size: 20px;
        }
      }
    }

    .content {
      margin-bottom: 16px;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
      @media only screen and (max-width: 576px) {
        margin-bottom: 0;
      }
    }

    .scrollPadding {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      scroll-behavior: smooth;
      -ms-overflow-style: none;
      height: 100%;

      &::-webkit-scrollbar {
        display: none;
      }
      .bannerSingle {
        span {
          width: 100%;
          min-width: 100% !important;
          height: 100%;
          min-height: 100% !important;
        }
        &::after {
          right: 0 !important;
        }
      }
      .banner {
        display: flex;
        flex: 1 0 auto;
        width: 352px;
        height: fit-content;
        aspect-ratio: 352/168;
        cursor: pointer;
        position: relative;

        &::after {
          content: 'Ads';
          position: absolute;
          right: 46px;
          background: rgba(203, 210, 217, 0.6);
          backdrop-filter: blur(12px);
          padding: 4px 8px;
          border-radius: 0 12px 0 12px;
          color: rgba(255, 255, 255, 1);
          font-size: 14px;
          font-weight: 400;
          line-height: 16px;
        }
        img {
          border-radius: 12px;
        }
      }
    }
  }

  .carousel {
    width: 100%;

    :global {
      .slick-dots {
        bottom: -16px;
        margin-bottom: 0;
        width: auto;

        li {
          width: 6px;
          height: 6px;

          button {
            background: #d9d9d9;
            width: 6px;
            height: 6px;
            border-radius: 100%;
            opacity: 1;

            &:before {
              content: '';
            }
          }

          &.slick-active {
            width: 6px;

            button {
              background: #00b5f1;
              width: 6px;
              height: 6px;
              border-radius: 100%;
            }
          }
        }
      }
    }

    .banner {
      padding-left: 1px;
      padding-right: 1px;
      max-height: 165px;
      cursor: pointer;
      position: relative;

      &::after {
        content: 'Ads';
        position: absolute;
        right: 0;
        background: rgba(203, 210, 217, 0.6);
        backdrop-filter: blur(12px);
        padding: 4px 8px;
        border-radius: 0 12px 0 12px;
        color: rgba(255, 255, 255, 1);
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
      }

      img {
        border-radius: 12px;
        min-height: 165px;
      }
    }
  }
}
