import {
  getPathnameAndSearch,
  getRoutePartnerIdBHYT,
  getRoutePartnerIdQueryParams,
  isChoosePatientStepOne,
  MPLoading,
  PageRoutesV2,
  RedirectHandler
} from '@medpro-libs/libs'
import { ListPatient } from '@medpro-libs/medpro-booking'
import { every, get } from 'lodash'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import client from '../../../config/medproSdk'
import withAuth from '../../../layout/Private/withAuth'
import actionStore from '../../../store/actionStore'
import {
  selectFirstSchedule,
  selectTreeId,
  selectBookingTreeState
} from '../../../store/booking/selector'
import { bookingActions, setTreeId } from '../../../store/booking/slice'
import { filterCheckActions } from '../../../store/filter-check/filterCheckSlice'
import { useAppSelector } from '../../../store/hooks'
import {
  selectExtraConfig,
  selectPartnerId
} from '../../../store/hospital/selector'
import { patientActions } from '../../../store/patient/patientSlice'
import { setBreadcrumb } from '../../../store/total/slice'
import { PageRoutes } from '../../../utils/PageRoutes'
import { showErrorNotification } from '../../../utils/utils.error'
import { selectAppFeatureSlug } from '../../../store/feature/selector'
import { reExamActions } from '../../../store/reexam/reexamSlice'
import { selectReExamPatient } from '../../../store/reexam/selector'
import { SSG_REVALIDATE_SECOND } from '../../../utils/utils.contants'
import { getError } from '../../../utils/utils.error'
import { fetchPartnerInfoBySlugServer } from '../../../utils/utils.query-server'
import { GetServerSidePropsContext } from 'next'
export interface ChoosePatientPageProps {
  partnerId: string
}

const ChoosePatient = (props: ChoosePatientPageProps) => {
  const router = useRouter()
  const slug = router.query.partner_slug as string
  const isDetail = router.query.isDetail as string
  const featureSlug = router.query.feature_slug as string
  const featureType = router.query.feature as string
  const packageSlug = router.query.packageSlug as string
  const doctorSlug = router.query.doctor_slug as string
  const bookingPage = router.query.bookingPage as string
  const bookingSubject = router.query.bookingSubject as string
  const dispatch = useDispatch()

  const treeId = useAppSelector(selectTreeId)
  const schedule = useAppSelector(selectFirstSchedule)
  const extraConfig = useAppSelector(selectExtraConfig)
  const appFeatureSlug = useAppSelector(selectAppFeatureSlug)
  const selectedHospital = useAppSelector((s) => s.hospital.selectedHospital)
  const bookingTreeState = useAppSelector(selectBookingTreeState)

  const loading = useAppSelector((state) => state.patient.loading)
  const errorLoadPatient = useAppSelector((state) => state.patient.error)
  const dataPatient = useAppSelector((state) => state.patient.listPatient)
  const reExamPatient = useAppSelector(selectReExamPatient)
  const partnerId = useAppSelector(selectPartnerId)
  const [checkingBhyt, setCheckingBhyt] = useState<any>(false) // Loading khi gọi API check BHYT
  const [formBhytVisible, setFormBhytVisible] = useState(false)
  const [formBhytData, setFormBhytData] = useState<any>()
  const [selectedPatient, setSelectedPatient] = useState<any>()
  const [modalData, setModalData] = useState({
    isDelete: false,
    id: ''
  })
  const [warningMessage, setWarningMessage] = useState<string>()

  useEffect(() => {
    dispatch(setBreadcrumb([{ title: 'Chọn hồ sơ bệnh nhân' }]))
  }, [])

  useEffect(() => {
    if (featureType && featureType.startsWith('booking.')) {
      const treeId = featureType.split('.')[1].trim().toUpperCase()
      dispatch(setTreeId(treeId))
    }
  }, [featureType])

  useEffect(() => {
    if (partnerId) {
      dispatch(patientActions.getPatientByUserId())
    }
  }, [dispatch, partnerId])

  useEffect(() => {
    if (extraConfig) {
      if (isChoosePatientStepOne(extraConfig, bookingPage)) {
        dispatch(bookingActions.resetMultiSchedules())
        dispatch(bookingActions.resetBookingTreeState())
      } else if (!schedule) {
        router.push(
          // getRoutePartnerIdQueryParams(PageRoutes.booking.path, partnerId)
          PageRoutesV2.booking({
            featureSlug,
            packageSlug,
            doctorSlug,
            query: { feature: featureType, partnerId }
          })
        )
      }
    }
  }, [extraConfig])

  useEffect(() => {
    if (dataPatient?.length > 0 && reExamPatient?.id) {
      const findPatient = dataPatient?.find((p) => p.id === reExamPatient?.id)
      dispatch(reExamActions.setIsAutoSelectedPatient(true))
      onSelect({ item: findPatient })
    }
  }, [dataPatient, reExamPatient?.id])

  const onWarning = (item: any) => {
    setWarningMessage(item?.message)
  }

  const onSelect = async (data: any) => {
    const { item } = data
    if (item?.id) {
      // get filter check
      const { service } = schedule || {}
      // isBhyt defalut là true nếu đi luồng có bảo hiểm y tế
      // isBhyt false khi click vào "tôi không có bhyt"
      const { birthdate, fullname, insuranceCode, isBhyt = true } = item

      // Click "tôi không có bhyt" sẽ back về chon-ho-so
      if (!isBhyt) {
        if (service.serviceType === 'INSURANCE_ONLY') {
          if (bookingTreeState) {
            dispatch(bookingActions.resetBookingFlowToStep('service'))
            router.push(
              getRoutePartnerIdBHYT(
                PageRoutes.booking.path,
                partnerId,
                {
                  feature: featureType,
                  isDetail,
                  bookingPage
                },
                featureSlug,
                packageSlug,
                doctorSlug,
                slug
              )
            )
          } else {
            await dispatch(
              bookingActions.getBookingBackLoginSaga({
                ...router.query,
                serviceId: undefined,
                stepName: 'service',
                step: 'chon-thong-tin-kham'
              })
            )
          }
        } else if (
          service.serviceType === 'BOTH' &&
          service.extraData?.optionBHYT === 1
        ) {
          dispatch(patientActions.setSelectedPatient(item))
          dispatch(bookingActions.updateOptionBHYT(0))
          router.push(
            getRoutePartnerIdBHYT(
              PageRoutes.bookingConfirm.path,
              partnerId,
              {
                feature: featureType,
                isDetail,
                bookingPage
              },
              featureSlug,
              packageSlug,
              doctorSlug,
              slug
            )
          )
        }

        return
      }

      if (
        (service && service.serviceType === 'INSURANCE_ONLY') || // type chỉ BHYT
        (service &&
          service.serviceType === 'BOTH' &&
          service.extraData?.optionBHYT === 1) // type cả 2 và có chọn option BHYT
      ) {
        if (service.requiredCheckInsurance) {
          if (!every([birthdate, fullname, insuranceCode], Boolean)) {
            // Open modal input
            setFormBhytData({ birthdate, fullname, insuranceCode })
            setSelectedPatient(item)
            setFormBhytVisible(true)
            return
          }

          const birthdayFormat = moment(item.birthdate, 'DD/MM/YYYY').format(
            'YYYY-MM-DD'
          )

          if (service.requiredCheckInsurance === true) {
            // có required thì mới check thông tin BHYT hợp lệ hay không?
            const postData = {
              fullName: `${item.surname} ${item.name}`,
              insuranceId: item.insuranceCode,
              birthday: birthdayFormat,
              bookingDate: moment(schedule.date).format('YYYY-MM-DD'),
              patientId: item.id
            }

            try {
              setCheckingBhyt(true)
              const { data: insuranceData } =
                await client.patient.getInsuranceDate(postData)

              const isExpiredInsurance = get(insuranceData, 'expired', false)
              const message = get(insuranceData, 'message', '')
              if (isExpiredInsurance) {
                showErrorNotification({
                  data: { message: message || 'Bảo hiểm đã hết hạn!' }
                })
              } else {
                setFormBhytVisible(false)
                dispatch(
                  patientActions.setInsurance({ code: item.insuranceCode })
                )
              }
            } catch (err) {
              showErrorNotification(err)
              // check BHYT fail => return
              return
            } finally {
              setCheckingBhyt(false)
            }
          }
        }

        // Check BHYT pass thi check filterCheck
        const objCheckFilter = {
          patientId: item.id || '',
          treeId,
          stepId: 'CheckBHYT',
          serviceId: service?.id || '',
          template: 'INSURANCE',
          hasInsurance: true,
          isValid: true,
          bookingDate: schedule.date ? moment(schedule.date).toISOString() : ''
        }

        let checkFilter
        try {
          const res = await client.filterCheck.checkFilter(objCheckFilter)
          checkFilter = res.data
        } catch (err) {
          showErrorNotification(err)
          return
        }

        if (checkFilter.requiredFilterCheck) {
          let routeRedirect
          if (['leloi'].includes(partnerId)) {
            checkFilter.type = 'TAI_KHAM'
            routeRedirect = `${PageRoutes.bhytInstruction.path}?partnerId=${partnerId}`
          } else if (['dalieuhcm', 'bvmathcm'].includes(partnerId)) {
            routeRedirect = `${PageRoutes.bhytInstruction.path}?partnerId=${partnerId}`
            checkFilter.type = 'CHUYEN_TUYEN'
          } else {
            routeRedirect = RedirectHandler.redirectAfterSelectPatient({
              partnerId,
              extraConfig,
              slug,
              featureSlug,
              doctorSlug
            })
          }

          dispatch(patientActions.setSelectedPatient(item))
          dispatch(filterCheckActions.setFilterCheckBHYT(checkFilter))
          dispatch(
            filterCheckActions.setRedirectPath(
              `${PageRoutes.bookingConfirm.path}?partnerId=${partnerId}`
            )
          )
          router.push(routeRedirect)
        } else {
          // !requiredFilterCheck
          // trường hợp đúng tuyến
          dispatch(patientActions.setSelectedPatient(item))
          dispatch(filterCheckActions.setFilterCheckBHYT(checkFilter))

          router.push(
            RedirectHandler.redirectAfterSelectPatient({
              partnerId,
              extraConfig,
              options: {
                featureType,
                bookingPage
              },
              slug,
              featureSlug,
              packageSlug,
              doctorSlug
            })
          )
        }
      } else {
        // service not require insurance
        dispatch(patientActions.setSelectedPatient(item))
        router.push(
          RedirectHandler.redirectAfterSelectPatient({
            partnerId,
            extraConfig,
            options: {
              featureType,
              bookingPage
            },
            slug,
            featureSlug,
            packageSlug,
            doctorSlug,
            isDetail
          })
        )
      }
    }
  }

  const onDelete = (item: any) => {
    if (item?.id) {
      setModalData({ ...modalData, isDelete: true, id: item.id })
    }
  }

  const onUpdate = (item: any) => {
    dispatch(patientActions.setUpdatePatientId(item?.id))
    router.push(
      getRoutePartnerIdQueryParams(PageRoutes.patient.update.path, partnerId, {
        id: item?.id
      })
    )
  }

  const handleOk = () => {
    dispatch(actionStore.unlinkPatient({ id: modalData.id }))
    setModalData({ ...modalData, isDelete: false, id: '' })
  }

  const handleCancel = () => {
    setModalData({ ...modalData, isDelete: false, id: '' })
  }

  //Xu ly nut quay lai
  const handleBackButton = () => {
    if (packageSlug || doctorSlug || bookingPage) {
      return router.back()
    }

    const route = RedirectHandler.backFromChoosePatient({
      slug,
      featureSlug,
      extraConfig: { ...extraConfig },
      appFeatureSlug,
      appPartnerSlug: selectedHospital?.slug,
      packageSlug,
      doctorSlug,
      bookingPage,
      bookingSubject,
      partnerId,
      isDetail,
      featureType
    })

    router.push(route)
  }

  //Them ho so moi
  const handleAddRecord = () => {
    dispatch(patientActions.setCreatePatientRedirectUrl(getPathnameAndSearch()))
    router.push(
      getRoutePartnerIdQueryParams(PageRoutes.patient.create.path, partnerId)
    )
  }

  if (!loading && errorLoadPatient) {
    return (router.query.debug as string) === 'true' ? (
      <div style={{ textAlign: 'center', padding: 10 }}>
        {JSON.stringify(errorLoadPatient)}
      </div>
    ) : (
      <div style={{ textAlign: 'center', padding: 10 }}>
        Hệ thống chưa xử lý được thao tác này, vui lòng thử lại sau!
      </div>
    )
  }

  if (loading || !extraConfig) {
    return (
      <MPLoading
        loading={{
          key: 'booking',
          status: true,
          description: loading
            ? 'Đang lấy danh sách hồ sơ'
            : !extraConfig
            ? 'Đang lấy thông tin cấu hình [ExtraConfig]'
            : ''
        }}
        title={''}
      />
    )
  }

  return (
    <>
      <ListPatient
        formBhytData={formBhytData}
        formBhytVisible={formBhytVisible}
        handleAddRecord={handleAddRecord}
        handleBackButton={handleBackButton}
        handleCancelModal={handleCancel}
        handleOkModal={handleOk}
        patientSelectedByCSKH={undefined}
        modalData={modalData}
        onDelete={onDelete}
        onSelect={onSelect}
        onUpdate={onUpdate}
        onWarning={onWarning}
        patientData={dataPatient}
        selectedPatient={selectedPatient}
        setFormBhytVisible={setFormBhytVisible}
        setWarningMessage={setWarningMessage}
        warningMessage={warningMessage}
      />
    </>
  )
}

ChoosePatient.displayName = 'ChoosePatientPage'
export default withAuth()(ChoosePatient)
