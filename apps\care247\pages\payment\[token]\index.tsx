import { Col, message, Row, Spin } from 'antd'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import client from '../../../config/medproSdk'
import usePayment from '../../../hooks/usePayment'
import { useTransactionStatus } from '../../../hooks/useTransactionStatus'
import {
  PaymentError,
  PaymentExpired,
  PaymentSuccess
} from '../../../src/components/pages/thanh-toan'
import LeftPanel from '../../../src/components/pages/thanh-toan/LeftPanel'
import PaymentCard from '../../../src/components/pages/thanh-toan/PaymentCard'
import { GENERATE_QRCODE_LINK, useWindowResize } from '../../../utils/constants'
import styles from './styles.module.less'

export function ThanhToanPage() {
  const router = useRouter()
  const token = router.query.token as string
  // State for UI components
  const [doMore, setDoMore] = useState(true)
  const [now, setNow] = useState(Date.now())
  const [openSuccessModal, setOpenSuccessModal] = useState(false)
  const isMobile = useWindowResize(576)
  const {
    paymentInfo,
    loading,
    translateData,
    translateLoading,
    getPaymentInfo,
    getTranslate,
    resetState,
    error
  } = usePayment()

  const paymentParams = useMemo(() => {
    if (!paymentInfo) return null

    return {
      ...paymentInfo,
      feeCode: paymentInfo.payment?.transactionId,
      customerName: paymentInfo.user?.fullname,
      amount:
        paymentInfo.payment?.amount ||
        paymentInfo.care247?.addonServices?.[0]?.price ||
        0,
      bankCode: paymentInfo.bankInfo?.bankCode,
      bankName: paymentInfo.bankInfo?.bankName,
      accountName: paymentInfo.bankInfo?.accountName,
      accountNo: paymentInfo.bankInfo?.accountNo,
      transferCode: paymentInfo.booking?.bookingCode || paymentInfo.feeCode,
      subTotal: paymentInfo.payment?.subTotal || paymentInfo.subTotal || 0,
      totalFee: paymentInfo.payment?.totalFee || paymentInfo.totalFee || 0,
      transferFee:
        paymentInfo.payment?.transferFee || paymentInfo.transferFee || 0,
      partnerImage: paymentInfo.partnerImage,
      expiredTime: paymentInfo.bankInfo?.expiredTime,
      qrCodeUrl: `${GENERATE_QRCODE_LINK}/${paymentInfo.bankInfo?.transactionId}/${paymentInfo.bankInfo?.extendData}`,
      fullname: paymentInfo?.patient?.surname + " " + paymentInfo?.patient?.name,
      serviceName: paymentInfo?.care247?.name
    }
  }, [paymentInfo])
  // Use transaction status hook
  const { transactionStatus, isCheckingStatus, isSuccess, stopChecking } =
    useTransactionStatus({
      transactionId: paymentInfo?.payment?.transactionId || '',
      params: paymentParams,
      enabled: !!(
        paymentInfo?.payment?.transactionId &&
        paymentParams &&
        doMore && paymentInfo?.isValid
      ),
      onSuccess: (status) => {
        try {
          // Check if status is 2 or 3 (success)
          if (status?.status === 2 || status?.status === 3) {
            setOpenSuccessModal(true)
          }
        } catch (error) {
          message.error('Có lỗi xảy ra khi xử lý kết quả thanh toán')
        }
      },
      onError: (error) => {
        try {
          message.error(`Lỗi kiểm tra trạng thái: ${error}`)
        } catch (err) {
          message.error('Có lỗi xảy ra khi kiểm tra trạng thái thanh toán')
        }
      }
    })

  useEffect(() => {
    const timer = setInterval(() => setNow(Date.now()), 1000)
    return () => clearInterval(timer)
  }, [])

  const handleResetTransaction = async () => {
    try {
      const { data } = await client.booking.getCare247CreateNewTransaction({
        token
      })
      if (data) {
        getPaymentInfo({
          token: data?.token
        })
       router.replace(
        {
          pathname: data?.link
        },
        undefined,
        { shallow: true }
      )
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi tạo giao dịch mới!')
    }
  }

  // Check if payment is expired
  const isExpired = useMemo(() => {
    if (!paymentInfo?.bankInfo?.expiredTime) return false
    const expiredTime = new Date(paymentInfo.bankInfo.expiredTime)
    const currentTime = new Date(now)
    return currentTime > expiredTime 
  }, [paymentInfo?.bankInfo?.expiredTime, now])

  const isPaymentSuccess = useMemo(() => {
    return transactionStatus?.status === 2 || transactionStatus?.status === 3
  }, [transactionStatus?.status])

  const renderPaymentContent = () => {
    if (error) {
      return <PaymentError error={error} />
    }

    if (!paymentInfo || loading || translateLoading) {
      return (
        <div className={styles.loadingFallback}>
          <Spin size='large' />
          <p className={styles.loadingText}>Đang tải thông tin thanh toán...</p>
        </div>
      )
    }

    if (isExpired || paymentInfo?.isValid === false) {
      return (
        <PaymentExpired
          payment={paymentParams}
          selectedTitle={translateData}
          createNewTransaction={handleResetTransaction}
          token={token}
        />
      )
    }

    if (isPaymentSuccess || openSuccessModal) {
      return (
        <PaymentSuccess payment={paymentParams} selectedTitle={translateData} />
      )
    }

    if (paymentParams && Object.keys(paymentParams).length > 0) {
      return (
        <PaymentCard
          payment={paymentParams}
          selectedTitle={translateData}
          isMobile={isMobile}
          selectLanguage={null}
          setDoMore={setDoMore}
          openSuccessModal={openSuccessModal}
          setOpenSuccessModal={setOpenSuccessModal}
        />
      )
    }

    return (
      <div className={styles.loadingFallback}>
        <Spin size='large' />
        <p className={styles.loadingText}>Đang xử lý...</p>
      </div>
    )
  }

  useEffect(() => {
    const checkExpirationInterval = setInterval(() => {
      if (paymentInfo?.bankInfo?.expiredTime) {
        const expiredTime = new Date(paymentInfo.bankInfo.expiredTime)
        const currentTime = new Date()
        if (currentTime > expiredTime && !isExpired) {
          setDoMore(false)
          stopChecking()
          setOpenSuccessModal(false)
        }
      }
    }, 10000)

    return () => clearInterval(checkExpirationInterval)
  }, [paymentInfo?.bankInfo?.expiredTime, isExpired, stopChecking])

  useEffect(() => {
    getTranslate({ language: 'vi' })
  }, [getTranslate])

  useEffect(() => {
    if(token){
      getPaymentInfo({
        token: token
      })
    }
  }, [token, getPaymentInfo])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      resetState()
      stopChecking()
    }
  }, [resetState, stopChecking])

  return (
    <div className={styles.paymentPage}>
      {/* Main Content */}
      <div className={styles.care247Image}>
        <Image
          src='https://cdn.medpro.vn/prod-partner/cb7e19ae-37bf-4b95-8d6e-a894f447f7b9-frame_1000003180.png'
          alt='Patient Care'
          width={128}
          priority
          objectFit='contain'
          layout='fixed'
          height={47}
          className={styles.imageElement}
        />
      </div>
      <div className={styles.mainContent}>
        <Row gutter={[56, 56]} align='stretch' className={styles.rowContent}>
          <Col xs={24} lg={0} xl={12}>
            <LeftPanel />
          </Col>
          <Col xs={24} lg={24} xl={12} flex={1}>
            {renderPaymentContent()}
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ThanhToanPage
