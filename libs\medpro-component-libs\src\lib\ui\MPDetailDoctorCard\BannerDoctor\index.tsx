import { Col, Modal, Row, Typography } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import { useState } from 'react'
// import { AiFillStar } from 'react-icons/ai'
// import { FaUser } from 'react-icons/fa'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import MPButton from '../../MPButton'
import styles from './styles.module.less'
import { capitalizeWords } from '../../MPSuggestCard/common/function'
import { customLoader, useWindowResize } from '../../../common/func'
import { HiCheckBadge } from 'react-icons/hi2'

const { Paragraph } = Typography

export const BannerDoctor = ({ data, handleBooking }: any) => {
  const isMobile = useWindowResize(576)
  const [warningBooking, setWarningBooking] = useState<boolean>()
  const [openBookingTelemed, setOpenBookingTelemed] = useState<any>(false)
  const [ellipsis, setEllipsis] = useState(true)
  return (
    <div className={styles['cardDoctor']}>
      <Row>
        <Col xs={24} sm={24} lg={24} className={styles['leftGroup']}>
          <div className={styles['logoImg']}>
            <Image
              loader={customLoader}
              src={data.imageUrl}
              alt={`${data.role} ${data?.title}`}
              title={`${data.role} ${data?.title}`}
              layout='fill'
              objectFit='cover'
              objectPosition='top'
              style={{ borderRadius: '8px' }}
            />
          </div>

          <div className={styles['groupInfo']}>
            <h1>
              <strong>
                {data?.role} {data?.title}
              </strong>
            </h1>
            <div className={styles['info_General']}>
              <label>Chuyên khoa</label>
              <span>
                {capitalizeWords(
                  data?.tags.map((item: any) => item.name).join(' - ')
                )}
              </span>
            </div>
            {data?.treatments && (
              <div className={styles['info_General']}>
                <label>Chuyên trị</label>
                <div>
                  <Paragraph
                    ellipsis={
                      ellipsis
                        ? {
                            rows: 2,
                            expandable: true,
                            symbol: <></>
                          }
                        : false
                    }
                  >
                    {data?.treatments}
                  </Paragraph>
                  {isMobile && (
                    <span
                      className={styles['read']}
                      onClick={() => setEllipsis((prev) => !prev)}
                    >
                      {ellipsis ? 'Xem thêm' : 'Thu gọn'}
                    </span>
                  )}
                </div>
              </div>
            )}
            <div className={cx(styles['info_General'])}>
              <label>Giá khám</label>
              <span>{data.price}</span>
            </div>
            <div className={cx(styles['info_General'])}>
              <label>Lịch khám</label>
              <span>{data.days}</span>
            </div>
            {/* <div className={styles['treatment']}>
              {!isMobile ? (
                <>
                  <b>Chuyên trị:</b>
                  <span>&nbsp;{data?.treatments}</span>
                </>
              ) : (
                <>
                  <span
                    className={cx(
                      styles['treatmentData'],
                      openViewMore
                        ? styles['treatmentReadMore']
                        : styles['treatmentReadLess']
                    )}
                  >
                    <strong>Chuyên trị:</strong> &nbsp;{data?.treatments}
                  </span>
                  {overflowStates && (
                    <p className={styles['read']} onClick={handleClick}>
                      {openViewMore ? 'Thu gọn' : 'Xem thêm'}
                    </p>
                  )}
                </>
              )}
            </div>
            <div className={styles['ratingDesktop']}>
              <div className={styles['rateDesktop']}>
                <p>
                  <AiFillStar color='#FFB54A' size={20} />
                </p>
                <span>{data?.doctorDescription?.rating?.rate}</span>
              </div>
              <div className={styles['totalRateDesktop']}>
                <p>
                  <FaUser color='#FFB54A' size={18} />
                </p>
                <span>{data?.doctorDescription?.bookingTotal || 100}</span>
              </div>
            </div> */}
          </div>
        </Col>
      </Row>
      <div className={styles['frame']}>
        <div className={styles['bottomLeft']}>
          <HiOutlineLocationMarker className={styles['linear-location']} />
          <div className={styles['groupAddress']}>
            <p className={styles['hopital']}>
              {data.partner.name || 'Đang cập nhật'}
              {data.partner?.listingPackagePaid && (
                <HiCheckBadge color='#0097FF' size={16} />
              )}
            </p>
            <p className={styles['address']}>
              {data.hospitalAddress || 'Đang cập nhật'}
            </p>
          </div>
        </div>
        <div className={styles['bottomRight']}>
          <MPButton
            onClick={(e) => {
              e.stopPropagation()
              if (data.doctorDescription.disabled) {
                setWarningBooking(true)
                return
              }
              if (data?.description?.notiBookingTelemed) {
                setOpenBookingTelemed(true)
                return
              } else {
                handleBooking(data)
              }
            }}
            className={styles['btnBooking']}
          >
            Đặt khám ngay
          </MPButton>
        </div>
      </div>
      {openBookingTelemed && (
        <Modal
          title={'Thông báo'}
          open={openBookingTelemed}
          okText='Đồng ý'
          onOk={() => handleBooking(data)}
          centered
          onCancel={() => setOpenBookingTelemed(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.description?.notiBookingTelemed
            }}
          />
        </Modal>
      )}
      {warningBooking && (
        <Modal
          title={'Thông báo'}
          open={warningBooking}
          footer={null}
          centered
          onCancel={() => setWarningBooking(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.doctorDescription.message
            }}
          />
        </Modal>
      )}
    </div>
  )
}
