.chooseBookingInfo {
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0px 4px 30px 0px rgba(116, 157, 206, 0.2);
  overflow: hidden;
  margin-bottom: 24px;
}
.panelsHeader {
  background: #1da1f2;
  color: #ffffff;
  padding: 12px 16px;
  background: linear-gradient(36deg, #00b5f1 0%, #00e0ff 100%);
  font-size: 20px;
  font-weight: 500;
  line-height: normal;
  // @media only screen and (max-width: 991px) {
  //   padding: 5px 7px;
  //   color: #000000;
  //   background: none;
  //   text-align: center;
  //   font-weight: 600;
  //   margin-bottom: 10px;
  //   text-transform: uppercase;
  // }
}

.cardBody {
  width: 100%;
  padding: 2px 16px 0px 16px;

  .specialist {
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    &:last-child {
      border-bottom: none;
    }
    @media (min-width: 992px) {
      display: none;
    }
  }
  .specialist .specialistItem {
    align-items: flex-start;
    align-self: stretch;
    display: flex;
    flex: 0 0 auto;
    gap: 8px;
    position: relative;
    width: 100%;
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0px;
      justify-content: right;
    }
  }

  .specialist .itemTitle {
    color: var(--primary-body-text, #003553);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: -1px;
    position: relative;
    min-width: 122px;
  }

  .specialist .content {
    width: 100%;
    flex: 1;
    color: var(--primary-body-text, #003553) !important;
    font-size: 16px !important;
    font-style: normal;
    font-weight: 500 !important;
    line-height: normal;
    margin-top: -1px;
    position: relative;
    word-wrap: break-word;
  }
  .button {
    display: flex;
    width: 78px;
    height: 40px;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    background: #fee;
    color: var(--error, #ff3b30);
    border: 1px solid transparent;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    span {
      margin-left: -4px;
    }
    &:hover {
      color: var(--error, #ff3b30);
      border: 1px solid #ff3b30;
      background: #fee;
    }
  }
  @media only screen and (max-width: 991px) {
    padding: 0;
  }
  .serviceDetail {
    vertical-align: initial;
  }
  .serviceTable {
    width: 100%;
  }
  .chooseServiceButton {
    background: rgba(216, 233, 250, 0.481);
    color: #0352cc;
    font-weight: 600;
    border: none;
  }
}

.serviceTable {
  margin-bottom: 1rem;
  @media (max-width: 991px) {
    display: none;
  }
  tbody {
    tr {
      border-top: 1px solid #e0e0e0;
    }
  }
  .serciceDetailt {
    cursor: pointer;
  }
  td,
  th {
    padding: 20px 5px;
  }
  .stt {
    min-width: 50px;
    text-align: center;
  }
  .td_sub {
    width: fit-content;
    max-width: 200px;
    p {
      white-space: nowrap;
      margin-bottom: 0;
    }
    @media (max-width: 768px) {
      white-space: break-spaces;
    }
    //
  }
  .th_title {
    width: fit-content;
    text-align: left;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  .money {
    width: fit-content !important;
    white-space: nowrap;
    text-align: right;
  }
  .time {
    text-align: left !important;
  }
  .btnDelete {
    padding: 6px 12px;
    background-color: transparent;
    border: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3 ease;
    svg {
      fill: #5a5a5a;
      font-size: 13px;
    }
    &:hover {
      background-color: #e74c3c;
      border: #e74c3c;
      svg {
        color: #fff;
        fill: #fff;
      }
    }
  }
  .action {
    text-align: center;
  }
}

.checkBHYT {
  width: 100%;
  .inputRadio {
    font-weight: 600;
  }
}

// @media only screen and (max-width: 576px) {
//   .chooseBookingInfo {
//     border-left: none;
//     border-right: none;
//     border-top: none;
//     .cardBody {
//       white-space: nowrap;
//     }
//   }
// }
