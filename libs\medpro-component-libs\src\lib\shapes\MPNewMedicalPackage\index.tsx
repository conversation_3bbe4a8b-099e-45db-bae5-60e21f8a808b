import React from 'react'
import MPNewMedicalPackageComponent from '../../component/MPNewMedicalPackageComponent'
import MPNewMedicalPackageCard from '../../ui/MPNewMedicalPackageCard'

interface IMPNewMedicalPackage {
  data: any
  service: any
  searchService: () => Promise<any>
  getPackages: (item: any) => Promise<any>
  handleBooking: (item: any) => void
  titlePage: string
  isHiddenFilter: boolean
  appInfo: any
  showError: (err: any) => void
}

export const MPNewMedicalPackage = (props: IMPNewMedicalPackage) => {
  return (
    <MPNewMedicalPackageComponent
      data={props.data}
      service={props.service}
      searchService={props.searchService}
      getPackages={props.getPackages}
      showError={props.showError}
      renderItem={({
        typeSearch,
        keySearch,
        filteredPackages,
        handleSearch,
        handleSearchDebounce,
        page,
        selectedPackage,
        handleSelect,
        handleViewMore,
        cityId,
        searching,
        searchKeyWords,
        loading,
        setCount,
        count,
        setTypeSearch,
        totalRows,
        setSearching,
        pageIndex,
        setPageIndex,
        isMobile,
        handleSearchPackage,
        provinces
      }) => {
        return (
          <MPNewMedicalPackageCard
            typeSearch={typeSearch}
            keySearch={keySearch}
            provinces={provinces}
            filteredPackages={filteredPackages}
            handleSearch={handleSearch}
            handleSearchDebounce={handleSearchDebounce}
            titlePage={props.titlePage}
            isHiddenFilter={props.isHiddenFilter}
            searchKeyWords={searchKeyWords}
            setTypeSearch={setTypeSearch}
            setSearching={setSearching}
            page={page}
            searching={searching}
            selectedPackage={selectedPackage}
            handleSelect={handleSelect}
            handleBooking={props.handleBooking}
            handleViewMore={handleViewMore}
            pageIndex={pageIndex}
            setCount={setCount}
            count={count}
            loading={loading}
            setPageIndex={setPageIndex}
            totalRows={totalRows}
            appInfo={props.appInfo}
            cityId={cityId}
            isMobile={isMobile}
            handleSearchPackage={handleSearchPackage}
          />
        )
      }}
    />
  )
}

export default MPNewMedicalPackage
