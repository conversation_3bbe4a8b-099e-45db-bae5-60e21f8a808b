import * as React from 'react'
import styles from '../styles.module.less'
import Image from 'next/image'
import userIcon from './user.svg'
import phoneIcon from './phone.svg'
import birthDateIcon from './birthdate.svg'
import addressIcon from './address.svg'
import { Card } from 'antd'
import MPButton from '../../MPButton'
import { useRouter } from 'next/router'

export interface ICardInfoProps {
  item: any
  handleSelect?: (item: any) => void
}

export default function CardInfo({ item, handleSelect }: ICardInfoProps) {
  const router = useRouter()
  const findExtra = router.query.findExtra as string
  const handleAddress = (item: any) => {
    let address
    if (
      item?.country_code === 'VIE' ||
      item?.country_code === 'medpro_VIE' ||
      item?.country_id === 'VIE' ||
      item?.country_id === 'medpro_VIE'
    ) {
      const list = [
        item?.address,
        item?.ward?.name,
        item?.district?.name,
        item?.city?.name
      ]
      address = list?.filter(Boolean).join(', ')
    } else {
      address = item?.address
    }
    return address ? address : 'Chưa cập nhật'
  }

  const handleContinue = () => {
    router.push({
      pathname: '/chon-lich-kham',
      query: {
        ...router.query,
        step: 'chon-ho-so'
      }
    })
  }

  return (
    <Card
      title={`${item?.patientCode || 'Chưa cập nhật'}`}
      className={styles['cardItem']}
      onClick={() => handleSelect && handleSelect(item)}
    >
      <div className={styles['infoItem']}>
        <div className={styles['icon']}>
          <Image src={userIcon} alt='search' />
        </div>
        <span className={styles['info']}>
          {item?.surname} {item?.name}
        </span>
      </div>
      <div className={styles['infoItem']}>
        <div className={styles['icon']}>
          <Image src={phoneIcon} alt='search' />
        </div>
        <span className={styles['info']}>
          {item?.mobile || 'Chưa cập nhật'}
        </span>
      </div>
      <div className={styles['infoItem']}>
        <div className={styles['icon']}>
          <Image src={birthDateIcon} alt='search' />
        </div>
        <span className={styles['info']}>
          {(handleSelect ? item?.birthyear : item?.birthdate) ||
            'Chưa cập nhật'}
        </span>
      </div>
      {findExtra && (
        <div className={styles['infoItem']}>
          <div className={styles['icon']}>
            <Image
              src={addressIcon}
              alt='search'
              className={styles['address']}
            />
          </div>
          <span className={styles['info']}>{handleAddress(item)}</span>
        </div>
      )}
      {!handleSelect && (
        <div className={styles['footerBtn']}>
          <MPButton
            className={styles['btnSubmit']}
            type='primary'
            // htmlType='submit'
            full='true'
            onClick={handleContinue}
          >
            Tiếp tục
          </MPButton>
        </div>
      )}
    </Card>
  )
}
