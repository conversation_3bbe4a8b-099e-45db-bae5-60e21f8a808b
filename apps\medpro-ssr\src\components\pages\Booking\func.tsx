import { BookingStepType } from '@medpro-libs/medpro-helper'
import {
  MP<PERSON>nimation,
  MPChooseDoctor,
  MPChooseRoom,
  MPChooseService,
  MPChooseSubject,
  MPIcon,
  PageRoutesV2
} from '@medpro-libs/libs'
import { RedirectAfterPaymentParams, RenderHeaderProps } from './type'
import { Space } from 'antd'
import { DateAndTimeComponent } from './component/DateAndTime'
import client from '../../../../config/medproSdk'
import { showErrorNotification } from '../../../../utils/utils.error'
import { PageRoutes } from '../../../../utils/PageRoutes'
import { setTransactionId } from '../../../../store/booking/slice'
import { LIST_APP_ID_MINI_APP } from '../../../../utils/utils.contants'
import styles from './styles.module.less'
export const getStepItem = (
  step: BookingStepType,
  partnerId?: any
): any | null => {
  switch (step) {
    case 'subject':
      return {
        key: 'subject',
        breadcrumb: 'Chọn chuyên khoa',
        title: (
          <MPAnimation
            duration={0}
            delay={0}
            fillMode='forwards'
            transLate='Top'
            className={styles['infoBookingTitle']}
          >
            <span>Vui lòng chọn chuyên khoa</span>
          </MPAnimation>
        ),
        icon: ({ isActive }: any) => (
          <MPIcon
            name='ChuyenKhoa'
            size={24}
            fill={isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) => {
          return props.type === 'subject' ? (
            <MPChooseSubject handleSubject={props.processNextStep} {...props} />
          ) : null
        },
        after: {
          icon: <MPIcon size={24} name='Search' />,
          place: 'Tìm nhanh chuyên khoa',
          input: true
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'service':
      return {
        key: 'service',
        breadcrumb: 'Chọn dịch vụ',
        title: (
          <MPAnimation
            duration={0}
            delay={0}
            fillMode='forwards'
            transLate='Top'
            className={styles['infoBookingTitle']}
          >
            <span>Vui lòng chọn dịch vụ</span>
          </MPAnimation>
        ),
        icon: (props: any) => (
          <MPIcon
            size={24}
            name='DichVu'
            fill={props.isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) => {
          return props.type === 'service' ? (
            <MPChooseService
              handleService={(data: any) => {
                console.log('handleServic edata: ', data)
                const { service, ...rest } = data
                props.processNextStep({ ...service, ...rest })
              }}
              serviceProps={{}}
              showPopupBHYT={props.showPopupBHYT}
              handleShowPopupBHYT={props.handleShowPopupBHYT}
              {...props}
            />
          ) : null
        },
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'doctor':
      return {
        key: 'doctor',
        breadcrumb: 'Chọn Bác sĩ',
        title: (
          <MPAnimation
            duration={0}
            delay={0}
            fillMode='forwards'
            transLate='Top'
            className={styles['infoBookingTitle']}
          >
            <span>Vui lòng chọn Bác sĩ</span>
          </MPAnimation>
        ),
        icon: (props: any) => (
          <MPIcon
            size={24}
            name='BacSi'
            fill={props.isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) =>
          props.type === 'doctor' ? (
            <MPChooseDoctor handleDoctor={props.processNextStep} {...props} />
          ) : null,
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'room':
      return {
        key: 'room',
        breadcrumb: 'Chọn phòng khám',
        title: 'Vui lòng chọn phòng khám',
        icon: (props: any) => (
          <MPIcon
            size={24}
            name='ChuyenKhoa'
            fill={props.isActive ? '#00b5f1' : '#7B8794'}
          />
        ),
        content: (props: any) =>
          props.type === 'room' ? (
            <MPChooseRoom handleRoom={props.processNextStep} {...props} />
          ) : null,
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    case 'time':
    case 'date':
      return {
        key: 'date',
        breadcrumb:
          partnerId === 'bvsingapore' ? 'Chọn ngày tư vấn' : 'Chọn ngày khám',
        title:
          partnerId === 'bvsingapore'
            ? 'Vui lòng chọn ngày tư vấn'
            : 'Vui lòng chọn ngày khám',

        icon: ({ isActive }: any) => (
          <MPIcon
            name={isActive ? 'NgayKham_active' : 'NgayKham_default'}
            size={24}
          />
        ),
        content: (props: any) => {
          return <DateAndTimeComponent {...props} />
        },
        after: {
          icon: <MPIcon name='Search' />
        },
        open: true,
        data: [],
        selected: {},
        other: {}
      }
    default:
      return null
  }
}

export const renderHeader = (props: RenderHeaderProps): any => {
  const { type, selectedItem } = props
  return (
    <div>
      <Space>
        <h3>{type}</h3>
        <h3>
          {selectedItem?.name ||
            selectedItem?.date ||
            selectedItem?.startTime ||
            'Chưa chọn'}
        </h3>
      </Space>
    </div>
  )
}

export const repayment = async ({
  selectedPaymentMethod,
  selectedPaymentType,
  bookingId,
  handleRedirectPayment,
  customerIpAddress,
  browserScreenHeight,
  browserScreenWidth
}: any) => {
  const { methodId } = selectedPaymentMethod
  const { code } = selectedPaymentType
  try {
    const { data } = await client.booking.rePayment({
      methodId,
      paymentTypeDetail: code,
      redirectUrl: `${window.location.origin}/${PageRoutes.booking.detail.path}`,
      groupId: 1,
      id: bookingId,
      customerIpAddress,
      browserScreenHeight,
      browserScreenWidth
    })

    handleRedirectPayment(data)
  } catch (err) {
    showErrorNotification(err)
  }
}

export const reserveSharePayment = async ({
  selectedPaymentMethod,
  selectedPaymentType,
  bookingId,
  handleRedirectPayment,
  partnerId,
  email,
  fullName,
  userName,
  customerIpAddress,
  browserScreenHeight,
  browserScreenWidth
}: any) => {
  const { methodId } = selectedPaymentMethod
  const { code } = selectedPaymentType
  try {
    const { data } = await client.booking.rePaymentShareToPay(
      {
        methodId,
        paymentTypeDetail: code,
        redirectUrl: `${window.location.origin}/${PageRoutes.booking.sharePaymentDetail.path}`,
        id: bookingId,
        email,
        fullname: fullName,
        username: userName,
        customerIpAddress,
        browserScreenHeight,
        browserScreenWidth
      },
      { partnerid: partnerId }
    )

    console.log({
      methodId,
      paymentTypeDetail: code,
      redirectUrl: `${window.location.origin}/${PageRoutes.booking.detail.path}`,
      id: bookingId
    })

    handleRedirectPayment(data)
  } catch (err) {
    showErrorNotification(err)
  }
}

export const handleRedirectPayment = ({
  data,
  router,
  appId,
  dispatch
}: RedirectAfterPaymentParams) => {
  const {
    qrCodeUrl,
    transactionId,
    deeplink,
    deeplinkWebInApp,
    deeplinkMiniApp,
    isGateway,
    redirectTransactionId
  } = data

  if (isGateway === 0) {
    // No gateway
    router.push(
      `/${PageRoutes.booking.detail.path}?mpTransaction=${transactionId}`
    )
    if (redirectTransactionId) {
      router.push(
        `/${PageRoutes.booking.detail.path}?mpTransaction=${redirectTransactionId}`
      )
    }
    return
  }
  if (transactionId) {
    dispatch(setTransactionId(transactionId))
  }
  if (LIST_APP_ID_MINI_APP.includes(appId)) {
    window.location.href = deeplinkMiniApp
  } else if (qrCodeUrl) {
    window.location.href = qrCodeUrl
  }
}

export const handleRedirectPaymentMethod = ({ router, partnerId }) => {
  const slug = router.query.partner_slug
  const featureSlug = router.query.feature_slug
  const packageSlug = router.query.packageSlug
  const doctorSlug = router.query.doctor_slug
  router.push(
    PageRoutesV2.paymentMethod({
      slug,
      featureSlug,
      packageSlug,
      doctorSlug,
      partnerId
    })
  )
}
export const handleRedirectPaymentNoAuth = ({
  data,
  router,
  appId,
  dispatch,
  smsCode,
  slug
}: RedirectAfterPaymentParams) => {
  const { qrCodeUrl, transactionId, deeplinkWebInApp, isGateway } = data
  if (isGateway === 0) {
    return router.replace(
      slug
        ? `/transactions/${smsCode}?isGateway=true`
        : `/booking/${smsCode}?isGateway=true`
    )
  }
  dispatch(setTransactionId(transactionId))
  if (LIST_APP_ID_MINI_APP.includes(appId)) {
    window.location.href = deeplinkWebInApp
  } else if (qrCodeUrl) {
    window.location.href = qrCodeUrl
  }
}

export const repaymentNoAuth = async ({
  selectedPaymentMethod,
  selectedPaymentType,
  bookingId,
  handleRedirectPayment,
  redirectUrl
}: any) => {
  const { methodId } = selectedPaymentMethod
  const { code } = selectedPaymentType
  try {
    const { data } = await client.booking.rePaymentNoAuth({
      methodId,
      paymentTypeDetail: code,
      redirectUrl: redirectUrl,
      groupId: 1,
      id: bookingId
    })

    handleRedirectPayment(data)
  } catch (err) {
    showErrorNotification(err)
  }
}
