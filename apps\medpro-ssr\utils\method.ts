import client, { server } from '../config/medproSdk'
import { AppInfo } from '../src/type'
import { PageRoutes } from './PageRoutes'
import { HeadersParams } from './utils.commonType'
import { getError } from './utils.error'
import { useEffect, useRef } from 'react'
import { SeoPageQuery } from 'medpro-sdk-v2'
import { renderCtaUrl } from './utils.function'

const DEFAULT_DOMAIN_CONFIG = {
  appId: 'momo',
  config: {
    domain: 'PKH',
    domainId: 8870
  }
}

export const getAppInfo = async ({ ctx }: any): Promise<AppInfo> => {
  return {
    appId: 'momo',
    partnerId: 'momo'
  }
}

export const useIsMount = () => {
  const isMountRef = useRef(true)
  useEffect(() => {
    isMountRef.current = false
  }, [])
  return isMountRef.current
}
export const getAppInfoSEO = async ({ ctx }: any): Promise<AppInfo> => {
  try {
     return {
        appId: 'momo'
      }
  } catch (error) {
    console.error(
      'Không có appid error :>> medpro',
      error.response?.data || error.message
    )
    return DEFAULT_DOMAIN_CONFIG
  }
}

export const checkFeatureEnable = async (type: string) => {
  try {
    const { data = [] } = await client.feature.getFeatureByPartner({
      version: '1',
      locale: 'vn'
    })
    const foundFeature = data.find((f) => f.type === type)
    return foundFeature && foundFeature.status && !foundFeature.disabled
  } catch (err) {
    return false
  }
}

export const getPopupServer = async (header: HeadersParams) => {
  try {
    const { data } = await server.partnerConfig.getPopup(header)
    return data
  } catch (err) {
    if (err.response?.status !== 404) {
      console.log('err get popup: ', err.toJSON ? err.toJSON() : err)
    }
    return {
      error: getError(err)
    }
  }
}

export const getHospitalExtraInfoServer = async (header: HeadersParams) => {
  try {
    const { data } = await server.partner.getExtraInfo(header)
    return data
  } catch (err) {
    return { error: JSON.stringify(getError(err)) }
  }
}
export const getTraffics = async (header: HeadersParams) => {
  try {
    const { data } = await server.partner.getTraffic(header)
    return data || []
  } catch (err) {
    return { error: getError(err) }
  }
}
export const getSeoPageServer = async (query: SeoPageQuery) => {
  try {
    query.path = query.path.split('?')[0]
    const { data } = await server.partner.getSeoPage(query)
    return data
  } catch (err) {
    return { error: JSON.stringify(getError(err)) }
  }
}

export const getEventTopicId = (item) => {
  const transactionId = item?.eventData?.transactionId
  const tab = item?.eventData?.tab
  const topicId = item?.topicId
  const userId = item?.userId
  const patientId = item?.eventData?.patientId
  const partnerId = item?.eventData?.partnerId
  const bookingId = item?.eventData?.bookingId
  const cta = item?.eventData?.cta
  let push_params
  console.log('topicId', topicId)
  switch (topicId) {
    case 'exam.result':
      push_params = {
        pathname: PageRoutes.booking.bookingsAndResults.path,
        query: {
          userId: userId,
          patientId: patientId,
          partnerId: partnerId,
          bookingId: bookingId
        }
      }
      break
    case 'bookings.tai-kham-nhanh':
      push_params = {
        pathname: `/reexam/${item?.eventData.urlId}`
      }
      break
    case 'messages.push-inform-notif':
      push_params = {
        pathname: item?.eventData?.url
      }
      break
    case 'care247_24hours_content':
      push_params = {
        pathname: PageRoutes.booking.detail.path,
        query: { transactionId: transactionId, tab: tab }
      }
      break
    case 'messages.cta.screen':
      push_params = {
        pathname: renderCtaUrl(cta)
      }
      break
    default:
      push_params = {
        pathname: PageRoutes.booking.detail.path,
        query: { transactionId: transactionId }
      }
      break
  }
  return push_params
}
