import { MPBookingApp } from '@medpro-libs/libs'
import { identity, pickBy, size } from 'lodash'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { FaStethoscope, FaUser } from 'react-icons/fa'
import { selectMultiSchedules } from '../../../store/booking/selector'
import { useAppSelector } from '../../../store/hooks'
import { showMessage } from '../../../utils/utils.notification'
import ConfirmComponent from './confirm'
import ConfirmPaymentBookingApp from './confirm/ConfirmPaymentBookingApp'
import SvgSubtract from './image/subtract'
import SvgWallet from './image/wallet'
import ChooseProfile from './recordProfile'
import styles from './styles.module.less'

export const BookingApp = (props: any) => {
  const [current, setCurrent] = useState(0)
  const router = useRouter()
  const getStep = router.query.step || 'chon-thong-tin-kham'
  const multiSchedules = useAppSelector(selectMultiSchedules)

  useEffect(() => {
    setCurrent(steps.findIndex((item) => item?.key === getStep))
  }, [getStep])

  const next = () => {
    setCurrent(current + 1)
    router.replace(
      {
        pathname: router.pathname,
        query: { ...router.query, step: steps[current + 1].key }
      },
      undefined,
      { shallow: true }
    )
  }

  const prev = (index) => {
    if (current < index || current === index) {
      return
    }
    if (size(multiSchedules) > 1 && index === 1) {
      showMessage('Thêm chuyên khoa không thể chọn lại hồ sơ', 'warning')
      return
    }
    router.replace(
      {
        pathname: router.pathname,
        query: pickBy(
          {
            ...router.query,
            step: steps[index].key,
            isBack: size(multiSchedules) > 0 && index === 0 // isBack = true khi xem lại Booking
          },
          identity
        )
      },
      undefined,
      { shallow: true }
    )
  }

  const steps = [
    {
      title: 'Chọn thông tin khám',
      key: 'chon-thong-tin-kham',
      content: <ConfirmComponent next={next} current={current} />,
      icon: (
        <div className={styles['stepIcon']} onClick={() => prev(0)}>
          <FaStethoscope size={18} />
        </div>
      )
    },
    {
      title: 'Chọn hồ sơ',
      key: 'chon-ho-so',
      content: <ChooseProfile next={next} />,
      icon: (
        <div className={styles['stepIcon']} onClick={() => prev(1)}>
          <FaUser size={18} />
        </div>
      )
    },
    {
      title: 'Xác nhận thông tin thanh toán',
      key: 'xac-nhan-thong-tin-thanh-toan',
      content: <ConfirmPaymentBookingApp current={current} />,
      icon: (
        <div className={styles['stepIcon']} onClick={() => prev(3)}>
          <SvgWallet width={18} height={18} />
        </div>
      )
    }
  ]

  return (
    <div className={styles['stepBookingApp']}>
      <MPBookingApp steps={steps} current={current} router={router} />
    </div>
  )
}

export default BookingApp
