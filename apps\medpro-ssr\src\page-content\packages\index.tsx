import {
  AlertModal,
  MPNewMedicalPackage,
  SEARCH_TAB,
  useWindowResize
} from '@medpro-libs/libs'
import { isArray, size } from 'lodash'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { AppInfo } from '../../type'
import { useAppSelector } from '../../../store/hooks'
import { PartnerInfo } from '../../../store/hospital/interface'
import { selectAppInfo } from '../../../store/hospital/selector'
import { totalDataActions } from '../../../store/total/slice'
import client from '../../../config/medproSdk'
import { showError } from '../../../utils/utils.notification'
import { bookingActions } from '../../../store/booking/slice'

export interface GoiKhamSucKhoePageProps {
  partnerId?: string
  token?: string
  appLoading?: boolean
  // Đây này là thông tin của partnerId chạy app. Ví dụ medpro.
  partnerInfoApp?: PartnerInfo
  isWebView?: boolean
  partnerInfo?: PartnerInfo
  hospitals: any[]
  packages: any[]
  type: string
  data: {
    total: any
    results: any[]
  }
  titlePage?: string
  service: any
  appInfo: AppInfo
  isHiddenFilter: boolean
}

const GoiKhamSucKhoePage = (props: GoiKhamSucKhoePageProps) => {
  const { service = {} } = props
  const isMobile = useWindowResize(576)
  const dispatch = useDispatch()
  const [messagePackage, setMessagePackage] = useState('')
  const appInfo = props.partnerInfoApp || useAppSelector(selectAppInfo)

  const searchService = async () => {
    return await client.searchService.searchKeyWords()
  }

  const getPackages = async ({
    pageIndex,
    pageSize,
    tags,
    kw,
    partnerId,
    cityId
  }) => {
    return await client.searchService.getPackages(
      {
        offset: Number(pageIndex || 1),
        limit: pageSize,
        tags: tags.join(','),
        search_key: kw,
        city_id: isArray(cityId) ? cityId?.join(',') : cityId
      },
      { appid: '', partnerid: partnerId }
    )
  }

  const handleBooking = async (item: any) => {
    if (size(item?.serviceDescription?.slug) === 0) {
      setMessagePackage('Gói khám đang được cập nhật')
    } else {
      setMessagePackage(undefined)
      if (!isMobile) {
        dispatch(
          bookingActions.handleBookingCta({ type: SEARCH_TAB.PACKAGE, item })
        )
      } else {
        dispatch(bookingActions.handleBookingCta({ type: 'bookingApp', item }))
      }
    }
  }

  return (
    <>
      <MPNewMedicalPackage
        data={props.data}
        service={service}
        searchService={searchService}
        getPackages={getPackages}
        handleBooking={handleBooking}
        titlePage={props.titlePage}
        isHiddenFilter={props.isHiddenFilter}
        appInfo={appInfo}
        showError={showError}
      />
      {messagePackage && (
        <AlertModal
          open={!!messagePackage}
          onOk={() => setMessagePackage('')}
          onCancel={() => setMessagePackage('')}
        >
          {messagePackage}
        </AlertModal>
      )}
    </>
  )
}

GoiKhamSucKhoePage.ssr = true
GoiKhamSucKhoePage.breadcrumb = [{ title: 'Gói khám' }]

export default GoiKhamSucKhoePage
