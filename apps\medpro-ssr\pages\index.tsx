/* eslint-disable react/no-children-prop */
import {
  MPCaro<PERSON>lBanner,
  MPCarouselBannerMulti,
  MPContainer,
  MPCooperated,
  MPDoctorTelemed,
  MPFeatureMobile,
  MPFeelPeople,
  MPModalPopup,
  MPNewAchievement,
  MPNewBannerHeader,
  MPNewBookingPackage,
  MPNewHomeDownload,
  MPNewNews,
  MPNewPackageMonth,
  MPNewServiceHeader,
  MPNewStatistic,
  MPNewSupportMethod,
  MPNewSupportMethodMobile,
  MPSpecialist,
  PageRoutesV2
} from '@medpro-libs/libs'
import { Feature } from '@medpro-libs/types'
import { Modal, Skeleton } from 'antd'
import cx from 'classnames'
import { useWindowDimensions } from 'libs/medpro-component-libs/src/lib/hooks/useWindowDimesion'
import { debounce, find, sortBy } from 'lodash'
import { GetServerSidePropsContext } from 'next'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useQuery } from 'react-query'
import { useDispatch } from 'react-redux'
import client from '../config/medproSdk'
import { newDefaultLayout } from '../layout'
import FeatureSeleketon from '../src/components/pages/Home/selekon/FeatureSeleketon'
import NewsLoading from '../src/components/pages/Home/selekon/NewsLoading'
import { QR, downloadApp } from '../src/components/pages/Home/staticData'
import { NextPageWithLayout, PageProps } from '../src/type'
import { bookingActions, selectFeature } from '../store/booking/slice'
import { featureActions } from '../store/feature/slice'
import { useAppSelector } from '../store/hooks'
import { IHospitalExInfo } from '../store/hospital/interface'
import { totalDataActions } from '../store/total/slice'
import { SEOPage } from '../utils/SEOPage'
import { deleteKeyCookie } from '../utils/cookies'
import {
  dataHeaderBg,
  dataVideo,
  defautTraffics
} from '../utils/fakeData/dataNewHome'
import { keyQueries } from '../utils/keyQueries'
import { getTraffics } from '../utils/method'
import { HomePageSectionItem } from '../utils/utils.commonType'
import { SSG_REVALIDATE_SECOND } from '../utils/utils.contants'
import { getError } from '../utils/utils.error'
import { handleUpdateLinkFromPartner } from '../utils/utils.function'
import { showError } from '../utils/utils.notification'
import styles from './index.module.less'
import { userActions } from '../store/user/userSlice'
interface NewHomeProps extends PageProps {
  features: Feature[]
  deliveredHospital: any[]
  news: any[]
  hospitalExtraInfo: IHospitalExInfo
  popupData: any
  meta: any
  clinicReport?: any
  traffics: any
}

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }

let timeout
const NewHome: NextPageWithLayout = (props: NewHomeProps) => {
  const {
    appInfo: { appId },
    partnerInfoApp: partnerInfo,
    traffics
  } = props

  const router = useRouter()
  const behavior = router.query?.behavior
  const token = router.query?.token as string
  const dispatch = useDispatch()
  const [isShowPopup, setIsShowPopup] = useState(true)
  const [searching, setSearching] = useState<any>(false)
  const [loading, setLoading] = useState<any>(true)
  const [searchData, setSearchData] = useState<any>({})
  const [homePageSection, setHomePageSection] =
    useState<HomePageSectionItem>(undefined)
  const [hospitalExtraInfo, setHospitalExtraInfo] = useState<IHospitalExInfo>()
  const [enableGetPopup, setEnableGetPopup] = useState(false)
  const [features, setFeatures] = useState<Feature[]>([])
  const [warningBooking, setWarningBooking] = useState({
    status: false,
    message: ''
  })
  const news = useAppSelector((s) => s.total.news)
  const meta = find(SEOPage, {
    key: 'trang-chu'
  })

  // Tạo ref cho vùng download section để redierect tới khi có query = downloadApp
  const isDownloadApp = router.asPath.split('/')[1]
  const downloadAppSectionRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (token) {
      createUserMomoByToken()
    }
  }, [token])
  const {
    data: _PopupQuery,
    isFetched: _PopupQuery_Fetched,
    isError: _PopupQuery_Error
  } = useQuery(
    [keyQueries.popup],
    async () => {
      const { data } = await client.partnerConfig.getPopup({
        platform: 'mobile'
      })
      return data
    },
    {
      retry: false,
      enabled: enableGetPopup
    }
  )
  const getDataHompage = async () => {
    try {
      const [
        { data: _listData },
        { data: _featureInApp },
        { data: _hospitalExtraInfo }
      ] = await Promise.all([
        client.homePageGet.getListHomePage({}),
        client.appId.getFeatureInApp({
          appid: 'momo'
        }),
        client.partner.getExtraInfo({ partnerid: 'medpro' })
      ])
      dispatch(featureActions.setFeatureListByPartner(_featureInApp))
      dispatch(totalDataActions.setExtraInfo(_hospitalExtraInfo))
      dispatch(totalDataActions.setCashBack({}))
      setHomePageSection(_listData)
      setFeatures(sortBy(_featureInApp, ['priority']))
      setHospitalExtraInfo(_hospitalExtraInfo)
    } catch (err) {
      setHomePageSection(undefined)
      dispatch(featureActions.setFeatureListByPartner([]))
      setHospitalExtraInfo({})
      showError(err)
    } finally {
      setSearching(false)
      setLoading(false)
    }
  }
  const createUserMomoByToken = async () => {
    try {
      const { data } = await client.user.createUserMomoByWalletId({
        token: token
      })
      client.setToken(data.token)
      dispatch(userActions.setToken(data.token))

      localStorage.setItem('token', data.token)
      router.replace(
        {
          pathname: '/',
          query: {}
        },
        undefined,
        { shallow: false }
      )
    } catch (err) {
      showError(err)
    }
  }

  useEffect(() => {
    getDataHompage()
    dispatch(totalDataActions.getNews({ appId }))
    setEnableGetPopup(sessionStorage.getItem('showModalDownloadApp') !== 'true')
  }, [])

  useEffect(() => {
    if (isDownloadApp === '#download') {
      setIsShowPopup(false)
      // run this function from an event handler or an effect to execute scroll
      downloadAppSectionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  })

  const onSearchDebounce = useCallback(
    async (event: any) => {
      clearTimeout(timeout)
      const kw = event.target.value
      setSearching(true)
      void onSearchHistory(kw)
    },
    [router.query]
  )
  const onSearchHistory = useCallback(
    debounce(async (kw) => {
      try {
        const { data } = await client.searchService.search(
          {
            search_key: kw,
            category: 'all',
            offset: 0,
            limit: 3
          },
          { appid: '', partnerid: '' }
        )
        const newData = data.reduce((c, i) => {
          c[i.category] = i.results
          return c
        }, {})
        return setSearchData(newData)
      } catch (err) {
        setSearchData({})
        showError(err)
      } finally {
        clearTimeout(timeout)
        setSearching(false)
      }
    }, 500),
    [setSearchData, setSearching]
  )

  const onSelectFeature = (data: any) => {
    dispatch(selectFeature(data?.feature))
    if (data?.feature?.customUrl) {
      window.open(data?.feature?.customUrl, '_blank')
    } else if (data?.feature?.slug === 'dat-kham-chuyen-khoa') {
      router.push(`/dich-vu-y-te/${data?.feature?.slug}`)
    } else {
      router.push(
        PageRoutesV2.hospitalList({
          appFeatureSlug: data?.feature?.slug || data?.feature?.id
        })
      )
    }
    window.dataLayer.push({
      event: 'Click Feature Đặt Khám',
      Action: 'Click',
      Category: 'Button-Action',
      Label: 'Đồng ý',
      Event: data?.feature?.name,
      PartnerId: 'medpro'
    })
  }

  // Check nếu có link trả về từ partnerInfo thì gán lại
  const downloadAppUponPartnerInfo = handleUpdateLinkFromPartner(
    downloadApp,
    partnerInfo
  )

  const handleBooking = async (type, item) => {
    await dispatch(
      bookingActions.handleBookingCta({ type: 'bookingApp', item })
    )
  }

  const handleDetail = (pathname, query) => {
    if (behavior === 'AnKhang') {
      return
    }
    router.push({
      pathname: pathname,
      query: query
    })
  }

  const handleViewAll = (pathname, query) => {
    router.push({
      pathname: pathname,
      query: {
        ...query,
        ...(behavior && { behavior })
      }
    })
  }

  const handleBookingSearch = async ({ type, item }: any) => {
    if (item.category === 'doctor' && item?.description?.disabled) {
      setWarningBooking((preState) => ({
        ...preState,
        status: true,
        message: item.description.message
      }))
    } else {
      await dispatch(
        bookingActions.handleBookingCta({ type: 'bookingApp', item })
      )
    }
  }
  console.log('features', features)
  const renderFeature = () => {
    return (
      <>
        <MPContainer medproSeo className={styles['mobileFeatures']}>
          <MPFeatureMobile
            data={features}
            handleClick={onSelectFeature}
            limit={6}
          />
        </MPContainer>
      </>
    )
  }

  const SkeletonLoader = () => {
    return (
      <div className={styles['skeleton']}>
        <Skeleton.Node
          children=''
          style={{ width: 168, height: 260, borderRadius: 8 }
          }
        />
        <Skeleton.Node
          children=''
          style={{ width: 168, height: 260, borderRadius: 8 }
          }
        />
        <Skeleton.Node
          children=''
          style={{ width: 168, height: 260, borderRadius: 8 }
          }
        />
        <Skeleton.Node
          children=''
          style={{ width: 168, height: 260, borderRadius: 8 }
          }
        />
      </div>
    )
  }

  return (
    <>
      <div className={cx(styles['main'], styles['start'])}>
        <div className={styles['header']}>
          <MPNewBannerHeader
            data={dataHeaderBg}
            onSearchDebounce={onSearchDebounce}
            handleBookingSearch={handleBookingSearch}
            searchData={searchData}
            searching={searching}
          />
          <div style={{ zIndex: 10 }}>
            {loading ? <FeatureSeleketon /> : renderFeature()}
          </div>
        </div>

        <MPContainer medproSeo className={styles['homeContainer']}>
          <MPCooperated
            data={homePageSection?.partner?.data}
            loading={loading}
            isMobile={true}
            hidden={homePageSection?.partner?.data?.length === 0}
          />
          <MPCarouselBanner
            isMobile={true}
            data={homePageSection?.banners?.data}
            hidden={homePageSection?.partner?.data?.length === 0}
          />
        </MPContainer>
      </div>
      <div className={styles['Rectangle3462']} />
      <div className={cx(styles['main'], styles['main-ecomerce'])}>
        <MPContainer
          medproSeo
          className={cx(styles['homeContainer'], styles['ecomerce'])}
        >
          <MPNewPackageMonth
            isMobile={true}
            data={homePageSection?.hospitals?.data}
            handleBooking={handleBooking}
            hidden={homePageSection?.hospitals?.data?.length === 0}
            skeleton={SkeletonLoader}
            handleDetail={handleDetail}
            handleViewAll={handleViewAll}
          />
        </MPContainer>
      </div>
      <div className={cx(styles['main'])}>
        <div className={styles['Rectangle4123']} />
        <MPContainer medproSeo className={styles['homeContainer']}>
          <MPCarouselBannerMulti
            isMobile={true}
            data={homePageSection?.bannersMulti?.data}
            hidden={homePageSection?.bannersMulti?.data?.length === 0}
          />
        </MPContainer>
        <div className={styles['Rectangle3462']} />
      </div>
      <div className={cx(styles['main'], styles['main-ecomerce'])}>
        <MPContainer
          medproSeo
          className={cx(styles['homeContainer'], styles['ecomerce'])}
        >
          <MPDoctorTelemed
            isMobile={true}
            data={homePageSection?.telemed_doctor_in_month?.data}
            handleBooking={handleBooking}
            hidden={
              homePageSection?.telemed_doctor_in_month?.data?.length === 0
            }
            skeleton={SkeletonLoader}
            handleDetail={handleDetail}
            handleViewAll={handleViewAll}
          />
          <MPNewBookingPackage
            isMobile={true}
            data={homePageSection?.service_in_month?.data}
            handleBooking={handleBooking}
            hidden={homePageSection?.service_in_month?.data?.length === 0}
            skeleton={SkeletonLoader}
            handleDetail={handleDetail}
            handleViewAll={handleViewAll}
          />
        </MPContainer>
      </div>
      <div className={styles['Rectangle4123']} />
      <div className={cx(styles['main'], styles['end'])}>
        {homePageSection && (
          <MPContainer medproSeo className={styles['homeContainer']}>
            <MPSpecialist
              isMobile={true}
              data={homePageSection?.subjects}
            />
          </MPContainer>
        )}
        {/*  Cảm nhận từ khách hàng  */}
        {homePageSection && (
          <MPContainer className={styles['homeContainer']}>
            <MPFeelPeople
              isMobile={true}
              data={homePageSection?.testimonials?.data}
              hidden={homePageSection?.testimonials?.data?.length === 0}
            />
          </MPContainer>
        )}
        <MPContainer className={styles['Achievement']}>
          <MPNewAchievement data={dataVideo} />
        </MPContainer>
        {/* Thống kê */}
        <div className={styles['Rectangle3462']}></div>
        <div style={{ background: '#E8F4FD' }}>
          <div className={styles['homeStatistic']}>
            <MPNewStatistic
              data={typeof traffics === 'object' ? traffics : defautTraffics}
            />
          </div>
          <div className={styles['homeContainer']}>
            <div className={styles['supportMP']}>
              <MPNewSupportMethodMobile />
            </div>
          </div>
        </div>

        {!_PopupQuery_Error ? (
          _PopupQuery_Fetched &&
          isShowPopup &&
          appId === 'medpro' && (
            <MPModalPopup {..._PopupQuery} handleBooking={handleBooking} />
          )
        ) : (
          <></>
        )}

        {warningBooking.status && (
          <Modal
            title={'Thông báo'}
            open={warningBooking.status}
            footer={null}
            centered
            onCancel={() =>
              setWarningBooking((preState) => ({
                ...preState,
                status: false,
                message: ''
              }))
            }
            className={styles['modal']}
          >
            <div
              className={styles['description']}
              dangerouslySetInnerHTML={{
                __html: warningBooking.message
              }}
            />
          </Modal>
        )}
      </div>
    </>
  )
}

export async function getStaticProps(context: GetServerSidePropsContext) {
  const startTime = new Date()
  let props = {}
  const appId = 'medpro'

  deleteKeyCookie('partnerId')

  try {
    const traffics = await getTraffics({})
    props = {
      traffics,
      executionTime: new Date().getTime() - startTime.getTime()
    }
  } catch (error) {
    console.log('error get server side props failed: ', error)
    props = {
      error: getError(error, {
        reason: 'get server side props failed!'
      }),
      news: [],
      features: [],
      executionTime: new Date().getTime() - startTime.getTime()
    }
  }

  return { props, revalidate: SSG_REVALIDATE_SECOND }
}

NewHome.displayName = 'HomePage'
NewHome.ssr = true
NewHome.getLayout = newDefaultLayout
NewHome.breadcrumb = []
export default NewHome
