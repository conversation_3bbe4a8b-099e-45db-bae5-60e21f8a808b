import {
  Button,
  Checkbox,
  Form,
  Input,
  Modal,
  Select,
  Steps,
  Result,
  Tooltip,
  Image,
  Row,
  Col,
  message,
  Tag
} from 'antd'
import cx from 'classnames'
import DefaultDrawer from 'libs/medpro-component-libs/src/lib/ui/DefaultDrawer'
import { AiTwotoneLike } from 'react-icons/ai'
import styles from './styles.module.less'
import { IoMdClose } from 'react-icons/io'
import { getFormatMoney, MPDrawerBooking } from '@medpro-libs/libs'
import { AiFillInfoCircle } from 'react-icons/ai'
import { renderRefundFields } from './handleDetails'
import moment from 'moment'
import { showError } from 'apps/medpro-ssr/utils/utils.notification'
import { useState, useEffect, useRef, useMemo } from 'react'
import { SearchOutlined } from '@ant-design/icons'
import { MdOutlineChevronRight } from 'react-icons/md'
import { FaArrowLeft } from 'react-icons/fa'
import { IoCloseSharp } from 'react-icons/io5'
import DefautDrawer from '../../page-content/bookingApp/DefautDrawer'

interface BookingModalProps {
  bookingInfo: any
  paymentInfo: any
  bankList: any[]
  isCancelBooking: boolean
  isCanceling: boolean
  isPaymentApotapay: boolean
  currentStepCancel: number
  cancelReasons: any[]
  selectedCancelReasons: string[]
  otherReason: string
  formCancelBooking: any
  isMobile: boolean
  extraConfig?: any
  onClose: () => void
  onCancel: () => void
  onSubmit: (values?: any, selectedBank?: any) => void
  onStepChange: (step: number) => void
  onReasonChange: (values: string[]) => void
  onOtherReasonChange: (value: string) => void
}

export const CancelBookingModal = ({
  bookingInfo,
  paymentInfo,
  bankList,
  isCancelBooking,
  isCanceling,
  isPaymentApotapay,
  currentStepCancel,
  cancelReasons,
  selectedCancelReasons,
  otherReason,
  formCancelBooking,
  isMobile,
  extraConfig,
  onClose,
  onCancel,
  onSubmit,
  onStepChange,
  onReasonChange,
  onOtherReasonChange
}: BookingModalProps) => {
  const [form] = Form.useForm()
  const [openBankDrawer, setOpenBankDrawer] = useState(false)
  const [selectedBank, setSelectedBank] = useState<any>(null)
  const [searchValue, setSearchValue] = useState('')
  const filteredBankList = searchValue
    ? bankList.filter(
        (bank) =>
          bank.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          bank.short_name.toLowerCase().includes(searchValue.toLowerCase()) ||
          bank.code.toLowerCase().includes(searchValue.toLowerCase())
      )
    : bankList
  useEffect(() => {
    if (window.visualViewport && isMobile) {
      const handleViewportChange = () => {
        const viewport = window.visualViewport
        const inputContainer = document.querySelector(
          '.otherReasonInput'
        ) as HTMLElement | null
        const modalBody = document.querySelector(
          '[class*="Body-reason"]'
        ) as HTMLElement | null

        if (inputContainer && modalBody) {
          // Tính toán chiều cao bàn phím ảo
          const keyboardHeight = window.innerHeight - viewport.height

          if (keyboardHeight > 100) {
            // Threshold để đảm bảo bàn phím thực sự mở
            modalBody.style.transition = 'transform 0.3s ease-in-out'
            // Đẩy toàn bộ modal body lên - phần này custom thêm height
            // modalBody.style.transform = `translateY(-${keyboardHeight * 0.4}px)`

            // Scroll đến textarea nếu cần
            setTimeout(() => {
              inputContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              })
            }, 100)
          } else {
            // Reset về vị trí ban đầu
            modalBody.style.transform = 'translateY(0)'
            modalBody.style.transition = 'transform 0.3s ease-in-out'
          }
        }
      }

      const handleInputFocus = () => {
        // Delay nhỏ để đảm bảo viewport đã thay đổi
        setTimeout(handleViewportChange, 100)
      }

      const handleInputBlur = () => {
        // Reset khi blur
        setTimeout(() => {
          const modalBody = document.querySelector(
            '[class*="Body-reason"]'
          ) as HTMLElement | null
          if (modalBody) {
            modalBody.style.transform = 'translateY(0)'
            modalBody.style.transition = 'transform 0.3s ease-in-out'
          }
        }, 100)
      }

      window.visualViewport.addEventListener('resize', handleViewportChange)

      // Thêm event listeners cho focus/blur
      const textareas = document.querySelectorAll('.otherReasonInput')
      textareas.forEach((textarea) => {
        textarea.addEventListener('focus', handleInputFocus)
        textarea.addEventListener('blur', handleInputBlur)
      })

      return () => {
        window.visualViewport.removeEventListener(
          'resize',
          handleViewportChange
        )
        textareas.forEach((textarea) => {
          textarea.removeEventListener('focus', handleInputFocus)
          textarea.removeEventListener('blur', handleInputBlur)
        })
      }
    }
  }, [])
  const { cancelNoteBooking, refundMoneyText, refundMoneyHelpText } = useMemo(
    () => ({
      cancelNoteBooking: extraConfig?.cancelNoteBooking,
      refundMoneyText: extraConfig?.refundMoneyText,
      refundMoneyHelpText: extraConfig?.refundMoneyHelpText
    }),
    [extraConfig]
  )
  const handleBankSelect = (bank: any) => {
    setSelectedBank(bank)
    formCancelBooking.setFieldValue('bankCode', bank.name)
    formCancelBooking.setFieldValue('bankName', bank.name)
    setOpenBankDrawer(false)
  }

  const renderContent = () => (
    <div className={cx(styles['Wrapper'])}>
      <div className={cx(styles['Header'])}>
        {!isMobile && (
          <button
            className={styles['closeButton']}
            onClick={onClose}
            aria-label='close'
          >
            <IoMdClose size={24} />
          </button>
        )}
        <div className={styles['Header-title']}>HỦY PHIẾU KHÁM</div>
      </div>

      <div className={styles['Body']}>
        {isPaymentApotapay && (
          <Steps
            current={currentStepCancel}
            className={styles['stepsCancel']}
            size='small'
            onChange={onStepChange}
          >
            <Steps.Step />
            <Steps.Step
              disabled={currentStepCancel < 1}
              onClick={() => {
                if (currentStepCancel < 1) {
                  message.warning('Bạn vui lòng chọn lý do hủy phiếu!!')
                }
              }}
            />
          </Steps>
        )}
        {(!isPaymentApotapay || currentStepCancel === 0) && (
          <>
            <p className={styles['Body-content']}>
              Medpro mong nhận được sự góp ý của bạn để có thể phục vụ tốt hơn
            </p>
            <div className={styles['Body-reason']}>
              <Checkbox.Group
                options={cancelReasons.map((r) => ({
                  label: r.label,
                  value: r.key
                }))}
                className={styles['radioGroup']}
                onChange={onReasonChange}
                value={selectedCancelReasons}
              />
              {selectedCancelReasons.includes('other') && (
                <Input.TextArea
                  placeholder='Điều khiến bạn muốn hủy phiếu khám'
                  required
                  value={otherReason}
                  rows={3}
                  className={styles['otherReasonInput']}
                  onChange={(e) => onOtherReasonChange(e.target.value)}
                  style={{ marginTop: 10, padding: 16, borderRadius: 12 }}
                />
              )}
            </div>
          </>
        )}

        {isPaymentApotapay && currentStepCancel === 1 && (
          <div className={styles['Body-refundInfo']}>
            {/*<div className={styles['Body-refundInfo-content']}>*/}
            {/*  <p className={styles['Body-refundInfo-content-title']}>*/}
            {/*    Số tiền được hoàn:{' '}*/}
            {/*    <strong>*/}
            {/*      {getFormatMoney(*/}
            {/*        (bookingInfo?.service || bookingInfo.serviceInfo).price*/}
            {/*      )}*/}
            {/*      đ*/}
            {/*    </strong>*/}
            {/*  </p>*/}
            {/*  {refundMoneyText && (*/}
            {/*    <p className={styles['Body-refundInfo-content-desc']}>*/}
            {/*      ({refundMoneyText}){' '}*/}
            {/*      {refundMoneyHelpText && (*/}
            {/*        <Tooltip*/}
            {/*          title={refundMoneyHelpText}*/}
            {/*          placement='top'*/}
            {/*          trigger={isMobile ? 'click' : 'hover'}*/}
            {/*        >*/}
            {/*          <AiFillInfoCircle size={18} color='#24313D' />*/}
            {/*        </Tooltip>*/}
            {/*      )}*/}
            {/*    </p>*/}
            {/*  )}*/}
            {/*</div>*/}
            <div className={styles['Body-refundInfo-info']}>
              <label>Thông tin phiếu khám</label>
              <Row
                gutter={[10, 8]}
                className={styles['Body-refundInfo-info-row']}
              >
                <Col span={24} sm={24} md={24}>
                  <div className={styles['row-item']}>
                    <span className={styles['label']}>Mã phiếu:</span>
                    <span className={styles['value']}>
                      {bookingInfo?.bookingCode}
                    </span>
                  </div>
                </Col>
                <Col span={24} sm={24} md={24}>
                  <div className={styles['row-item']}>
                    <span className={styles['label']}>Tên bệnh nhân:</span>
                    <span className={styles['value']}>
                      {bookingInfo?.patient.surname +
                        ' ' +
                        bookingInfo?.patient.name}
                    </span>
                  </div>
                </Col>
                <Col span={24} sm={24} md={24}>
                  <div className={styles['row-item']}>
                    <span className={styles['label']}>Ngày khám:</span>
                    <span className={styles['value']}>
                      {bookingInfo?.date ? (
                        moment(bookingInfo?.date).format('HH:mm, DD/MM/YYYY')
                      ) : (
                        <div>
                          {bookingInfo?.timeStr}, {bookingInfo?.dateStr}
                        </div>
                      )}
                    </span>
                  </div>
                </Col>
              </Row>
            </div>
            <Form
              form={formCancelBooking}
              layout='vertical'
              className={styles['formRefundInfo']}
            >
              <p className={styles['label']}>
                Nhập thông tin tài khoản ngân hàng để nhận tiền hoàn
              </p>
              <Row gutter={10}>
                {renderRefundFields({
                  form: formCancelBooking,
                  bankList,
                  isMobile,
                  openBankDrawer,
                  setOpenBankDrawer,
                  selectedBank,
                  setSelectedBank
                }).map((item, index) => {
                  return (
                    <Col
                      key={index}
                      span={24}
                      sm={24}
                      md={isMobile ? 24 : item.width}
                    >
                      <div className={styles['inputItem']}>
                        {item?.enter && item?.enter()}
                      </div>
                    </Col>
                  )
                })}
              </Row>
            </Form>
            {cancelNoteBooking && (
              <div
                className={styles['Content-Note']}
                dangerouslySetInnerHTML={{
                  __html: cancelNoteBooking || ''
                }}
              />
            )}
          </div>
        )}
      </div>

      <div className={cx(styles['Footer'])}>
        <div className={styles['Footer_btn']}>
          <Button
            type='ghost'
            className={styles['btnCancel']}
            onClick={onCancel}
          >
            Không hủy
          </Button>
          <Button
            type='primary'
            className={styles['btnOke']}
            loading={isCanceling}
            onClick={() => {
              onSubmit(formCancelBooking.getFieldsValue(), selectedBank)
            }}
            disabled={
              selectedCancelReasons.length === 0 ||
              (selectedCancelReasons.includes('other') && !otherReason.trim())
            }
          >
            {isPaymentApotapay && currentStepCancel === 0 ? 'Tiếp tục' : 'Gửi'}
          </Button>
        </div>
        <div className={styles['Footer_hotline']}>
          <span>
            Hotline hỗ trợ <a href={'tel:********'}>1900 2115</a>
          </span>
        </div>
      </div>
    </div>
  )

  const renderBankList = () => {
    return (
      <div className={cx(styles['bankSelectionDrawer'])}>
        <div className={styles['bankListContainer']}>
          {filteredBankList.map((bank, index) => (
            <Button
              key={index}
              className={styles['bankItem']}
              onClick={() => handleBankSelect(bank)}
            >
              <div className={styles['bankItemContent']}>
                <span className={styles['bankIcon']}>
                  <Image
                    src={bank.logo}
                    alt={bank.name}
                    width={35}
                    height={35}
                    preview={false}
                  />
                </span>
                <div className={styles['bankInfo']}>
                  <label>{bank.name}</label>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </div>
    )
  }

  if (!isCancelBooking) return null

  if (isMobile) {
    return (
      <>
        <DefautDrawer
          open={isCancelBooking}
          title={'HỦY PHIẾU KHÁM'}
          onClose={() => {
            if (openBankDrawer) {
              setOpenBankDrawer(false)
              form.resetFields()
              setSearchValue('')
            } else {
              onClose()
            }
          }}
          height={isPaymentApotapay ? '80%' : 'auto'}
          className={cx(styles['drawerCancelBooking'])}
          closeIcon={
            openBankDrawer ? (
              <FaArrowLeft />
            ) : (
              <IoCloseSharp size={22} style={{ marginTop: '2px' }} />
            )
          }
          extra={
            openBankDrawer && (
              <MPDrawerBooking
                hospital={''}
                province={[]}
                handleSearch={(e) => setSearchValue(e)}
                title=''
                placeholder={'Vietcombank, Vietinbank, BIDV, ...'}
                subTitle=' '
                isHiddenFilter={true}
                isHidden={false}
                form={form}
              />
            )
          }
        >
          <div className={cx(styles['drawerContent'])}>
            {openBankDrawer ? renderBankList() : renderContent()}
          </div>
        </DefautDrawer>
      </>
    )
  }

  return (
    <Modal
      open={isCancelBooking}
      className={styles['modalCancelBooking']}
      footer={null}
      centered
      width={618}
      closable={false}
    >
      {renderContent()}
    </Modal>
  )
}

interface NotificationModalProps {
  isOpen: boolean
  onClose: () => void
  waitingMessage: string
  duration: number
  renderDurationNumber: (duration: number | string) => string | number
  onHomeClick: () => void
}

export const NotificationModal = ({
  isOpen,
  onClose,
  waitingMessage,
  duration,
  renderDurationNumber,
  onHomeClick
}: NotificationModalProps) => {
  if (!isOpen) return null

  return (
    <Modal
      title=''
      open={isOpen}
      className={styles['ModuleNotification']}
      footer={null}
      onCancel={onClose}
      centered
      maskClosable={false}
      bodyStyle={{ padding: '16px' }}
      destroyOnClose={false}
      closable={false}
    >
      <div className={styles['ModalHeaderNotifi']}>
        <span>Thông báo</span>
      </div>
      <div
        dangerouslySetInnerHTML={{
          __html: waitingMessage || ''
        }}
        className={styles['waitingMessage']}
      />
      {duration !== 0 && (
        <p className={styles['waitingTime']}>
          Thời gian đếm ngược <span>{renderDurationNumber(duration)}s</span>
        </p>
      )}
      {waitingMessage && duration === 0 ? (
        <div className={styles['btnNotifiContainer']}>
          <Button
            type='primary'
            key='cancel'
            className={styles['btnNotifiTel']}
          >
            <a href='tel:********'>
              <span>Gọi</span> 1900 2115
            </a>
          </Button>
          <Button
            type='primary'
            key='cancel'
            className={styles['btnNotifi']}
            onClick={onClose}
          >
            Đóng
          </Button>
        </div>
      ) : (
        <Button
          type='primary'
          key='cancel'
          className={styles['btnNotifi']}
          onClick={onHomeClick}
        >
          Về trang chủ
        </Button>
      )}
    </Modal>
  )
}

interface ComplaintSuccessModalProps {
  isOpen: boolean
  onClose: () => void
}

export const ComplaintSuccessModal = ({
  isOpen,
  onClose
}: ComplaintSuccessModalProps) => {
  if (!isOpen) return null

  return (
    <Modal
      open={isOpen}
      className={styles['modalComplain']}
      footer={null}
      onCancel={onClose}
      width={400}
      centered
    >
      <Result
        status='success'
        title={
          <div className={styles['titleComplain']}>
            <p>Yêu cầu khiếu nại thành công</p>
            <span>
              Chúng tôi sẽ xử lý và phản hồi trong vòng 24 giờ. Cám ơn bạn đã sử
              dụng dịch vụ
            </span>
          </div>
        }
        className={styles['resultComplain']}
        icon={
          <div className={styles['iconComplain']}>
            <AiTwotoneLike size={40} color='#11A2F3' />
          </div>
        }
        extra={[
          <Button type='primary' key='cancel' onClick={onClose}>
            Đóng
          </Button>
        ]}
      />
    </Modal>
  )
}
