import { Input, InputProps } from 'antd'
import React, { useEffect, useState } from 'react'
import { CiSearch } from 'react-icons/ci'
import { PLACEHOLDER_SEARCHING } from '../../common/constant'
import { size } from 'lodash'
import { useFocus } from '../../common/func'

interface MPInputTyping extends InputProps {
  minisearch: boolean
  onTyping: (event: any) => void
  value: string
  handleKeyPress: (event: any) => void
  onOpenPopover: any
}

export function MPInputTyping(props: MPInputTyping) {
  const [placeholderIndex, setPlaceholderIndex] = useState(0)
  const [placeholderText, setPlaceholderText] = useState('')
  const [isTyping, setIsTyping] = useState(true)
  const [characterIndex, setCharacterIndex] = useState(0)

  const animationTyping = () => {
    const currentPlaceholder = PLACEHOLDER_SEARCHING[placeholderIndex]
    const maxCharacterIndex = size(currentPlaceholder)

    if (isTyping) {
      if (characterIndex < maxCharacterIndex) {
        setCharacterIndex(characterIndex + 1)
      } else {
        setIsTyping(false)
      }
    } else {
      if (characterIndex > 0) {
        setCharacterIndex(characterIndex - 1)
      } else {
        setIsTyping(true)
        if (placeholderIndex === size(PLACEHOLDER_SEARCHING) - 1) {
          setPlaceholderIndex(0)
        } else {
          setPlaceholderIndex(placeholderIndex + 1)
        }
      }
    }

    setPlaceholderText(currentPlaceholder.substring(0, characterIndex))
  }

  useEffect(() => {
    const interval = setInterval(animationTyping, 100) // Adjust the speed of typing/deleting here

    return () => {
      clearInterval(interval)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTyping, characterIndex])

  const [isTouched, bind] = useFocus()

  useEffect(() => {
    if (isTouched && props.value) {
      props.onOpenPopover(true)
    }
  })

  return (
    <React.Fragment>
      <Input
        type='text'
        {...bind}
        allowClear
        placeholder={placeholderText}
        prefix={<CiSearch size={24} color='#B1B1B1' />}
        onChange={props.onTyping}
        value={props.value.normalize('NFC')}
        onKeyPress={props.handleKeyPress}
      />
    </React.Fragment>
  )
}

export default MPInputTyping
