.noteBillContent {
  text-align: left;
  margin-bottom: 30px;
  .attention {
    color: #df0000;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  .note {
    position: relative;
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 16.94px;

    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    ul {
      list-style-position: initial !important;
      padding-inline-start: 15px !important;
    }
  }
  .expanded {
    -webkit-line-clamp: unset !important;
    max-height: unset !important;
  }
}
.btnExpand {
  position: absolute;
  bottom: -14px;
  right: 40%;
  margin-bottom: 0;
  font-size: 14px;
  line-height: normal;
  color: #1890ff;
  cursor: pointer;
  .up {
    animation: bounceUp 1s infinite;
    display: inline-block;
    transition: all 0.3s ease;
  }
  .down {
    animation: bounce 1s infinite;
    display: inline-block;
    transition: all 0.3s ease;
  }
}
@keyframes bounceUp {
  0%,
  100% {
    transform: translateY(0); /* Start and end at the original position */
  }
  50% {
    transform: translateY(-2px); /* Move up by 10px */
  }
}
@keyframes bounce {
  0%,
  100% {
    transform: translateY(0); /* Start and end at the original position */
  }
  50% {
    transform: translateY(2px); /* Move down by 10px */
  }
}
.copyRight {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  > span {
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    color: #27aae1;
  }
  a {
    width: fit-content;
  }
  img {
    width: 60px;
    height: 26px;
  }

  border-top: 2px dashed #f0f2f5;
  position: relative;
  padding-top: 14px;
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: -15px;
    width: 28px;
    height: 28px;
    background-color: #f6f6f6;
    border-radius: 50%;
  }
  &::after {
    right: -30px;
  }
  &::before {
    left: -30px;
  }
}
.introMedpro {
  text-align: center;
  position: relative;
  padding-bottom: 19px;
  margin-top: 4px;
  font-size: 12px;
  line-height: 14px;
  font-weight: 400;
  &::before,
  &::after {
    content: '';
    position: absolute;
    bottom: -15px;
    width: 28px;
    height: 28px;
    background-color: #f6f6f6;
    border-radius: 50%;
  }
  &::after {
    right: -34px;
  }
  &::before {
    left: -34px;
  }
  &:last-child::before {
    background-color: #ffffff !important;
    width: 0;
    height: 0;
  }
  &:last-child::after {
    background-color: #ffffff !important;
    width: 0;
    height: 0;
  }
}
