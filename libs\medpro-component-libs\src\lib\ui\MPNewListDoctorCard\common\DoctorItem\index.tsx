import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd'
import { SEARCH_LIST } from 'libs/medpro-component-libs/src/lib/common/constant'
import { size } from 'lodash'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { AiFillStar } from 'react-icons/ai'
import { FaUser } from 'react-icons/fa'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import MPButton from '../../../MPButton'
import styles from './styles.module.less'
import cx from 'classnames'
import { useWindowDimensions } from 'libs/medpro-component-libs/src/lib/hooks/useWindowDimesion'
import DefaultDrawer from '../../../DefaultDrawer'
import { HiCheckBadge } from 'react-icons/hi2'
// import { useRouter } from 'next/router'

// const RatingDefault = {
//   count: 4.8,
//   evaluate: 'Chưa đánh giá'
// }
interface Props {
  data: any
  selectedHospital: any
  partnerDetail?: boolean
  isSelect?: boolean
  handleBooking: (item: any) => void
}
const DoctorItem = (props: Props) => {
  const { data } = props
  const router = useRouter()
  const { windowWidth } = useWindowDimensions()
  const partner_slug = router.query.partner_slug
  const isDetail =
    data?.description?.isDetailVisible ||
    data?.doctorDescription?.isDetailVisible ||
    partner_slug === 'benh-vien-dai-hoc-y-duoc-co-so-1' ||
    props?.partnerDetail
  const slugDoctor = data?.description?.slug || data?.doctorDescription?.slug
  const [openBookingTelemed, setOpenBookingTelemed] = useState<any>(false)
  const [remainingSpecialist, setRemainingSpecialist] = useState([])
  const [warningBooking, setWarningBooking] = useState<boolean>()
  const [drawerCashBack, setDrawerCashBack] = useState(false)
  const [Visible, setVisible] = useState(false)
  const trigger = windowWidth > 576 ? 'hover' : 'click'
  const behaviorQuery = router.query.behavior
    ? `&behavior=${router.query.behavior}`
    : ''

  useEffect(() => {
    // 3CK thì show 3 ; 4CK show 2, hide 2
    // đảm bảo show 3 button trên UI
    if (data?.tags) {
      const remainingSpecialist = data?.tags.map((item: any) => item.name)
      setRemainingSpecialist(remainingSpecialist)
    }
  }, [data])
  const capitalizeWords = (inputString: string): string => {
    return inputString
      .toLowerCase()
      .replace(/(?:^|\s)\S/g, (match) => match.toUpperCase())
  }

  const onShow = (e: any) => {
    e.stopPropagation()
    if (trigger === 'click') {
      setDrawerCashBack(true)
    }
  }
  const onHidden = (e: any) => {
    e.stopPropagation()
    setDrawerCashBack(false)
  }
  return (
    <div
      className={cx(
        styles['cardDoctor'],
        props.isSelect && styles['cardDoctor_select']
      )}
    >
      {data?.partner?.isCashBack && (
        <div className={styles['tagCashBack']} onClick={onShow}>
          <Popover
            showArrow={true}
            overlayClassName={styles['popoverCashBack']}
            overlayInnerStyle={{ width: 510 }}
            content={
              data?.partner?.popup?.content && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: data?.partner?.popup?.content
                  }}
                />
              )
            }
            onOpenChange={(visible) => {
              if (windowWidth > 576 && visible) {
                setVisible(true)
              } else {
                setVisible(false)
              }
            }}
            open={Visible}
            placement='bottomLeft'
          >
            Hoàn tiền
          </Popover>
        </div>
      )}

      <div className={styles['leftGroup']}>
        <div className={styles['logoImg']}>
          <div
            style={{
              backgroundImage: `url(${data.imageUrl})`
            }}
            className={styles['Avatar']}
          ></div>
          {isDetail && (
            <Link
              href={`/bac-si/${slugDoctor}?treeId=${props.data.treeId}${behaviorQuery}`}
            >
              <a className={styles['detailDoctor']}>Xem chi tiết</a>
            </Link>
          )}
        </div>

        <div className={styles['groupInfo']}>
          <h3>
            {data?.role}{' '}
            <strong>
              {data?.title} | {capitalizeWords(remainingSpecialist.join(' - '))}
            </strong>
          </h3>
          <div className={styles['treatment']}>
            <strong>Chuyên trị:</strong>{' '}
            {data?.treatments || 'Đang cập nhật...'}
          </div>
          {data?.days && (
            <div className={styles['treatment']}>
              <strong>Lịch khám:</strong> {data?.days}
            </div>
          )}
          {data?.price && (
            <div className={styles['treatment']}>
              <strong>Giá khám:</strong> {data?.price}
            </div>
          )}
          <div className={styles['rating']}>
            {size(data?.doctorDescription?.rating?.rate) > 0 && (
              <div className={styles['rate']}>
                <p>
                  <AiFillStar color='#FFB54A' size={15} />
                </p>
                <span>{data?.doctorDescription?.rating?.rate}</span>
              </div>
            )}

            {size(data?.doctorDescription?.bookingTotal) > 0 && (
              <div className={styles['totalRate']}>
                <p>
                  <FaUser color='#FFB54A' size={12} />
                </p>
                <span>{data?.doctorDescription?.bookingTotal}</span>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className={styles['frame']}>
        <div className={styles['bottomLeft']}>
          <HiOutlineLocationMarker className={styles['linear-location']} />
          <div className={styles['groupAddress']}>
            <p className={styles['hospital']}>
              {data.partner.name || 'Đang cập nhật'}
              {data.partner?.listingPackagePaid && (
                <HiCheckBadge color='#0097FF' size={16} />
              )}
            </p>
            <p className={styles['address']}>
              {data.hospitalAddress || 'Đang cập nhật'}
            </p>
          </div>
        </div>
        <div className={styles['bottomRight']}>
          <MPButton
            onClick={(e) => {
              e.stopPropagation()
              if (data.description?.disabled) {
                setWarningBooking(true)
                return
              }
              if (data?.description?.notiBookingTelemed) {
                setOpenBookingTelemed(true)
                return
              } else {
                props.handleBooking(props.data)
              }
            }}
            className={styles['btnBooking']}
          >
            Đặt ngay
          </MPButton>
        </div>
      </div>
      {openBookingTelemed && (
        <Modal
          title={'Thông báo'}
          open={openBookingTelemed}
          okText='Đồng ý'
          onOk={() => props?.handleBooking(data)}
          centered
          onCancel={() => setOpenBookingTelemed(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.description?.notiBookingTelemed
            }}
          />
        </Modal>
      )}
      {warningBooking && (
        <Modal
          title={'Thông báo'}
          open={warningBooking}
          footer={null}
          centered
          onCancel={() => setWarningBooking(false)}
          className={styles['modal']}
        >
          <div
            className={styles['description']}
            dangerouslySetInnerHTML={{
              __html: data?.description.message
            }}
          />
        </Modal>
      )}
      {
        <DefaultDrawer
          title={data?.partner?.popup?.title}
          open={drawerCashBack}
          onClose={onHidden}
          children={
            <div
              dangerouslySetInnerHTML={{
                __html: data?.partner?.popup?.content
              }}
            />
          }
          height={'calc(70%)'}
          style={{ zIndex: 999999999 }}
        />
      }
    </div>
  )
}

export default DoctorItem
