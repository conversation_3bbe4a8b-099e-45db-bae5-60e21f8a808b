import { PaymentMethod, PaymentType, SelectionData } from '@medpro-libs/libs'
import { ListPaymentMethod } from '@medpro-libs/medpro-booking'
import { compose } from '@reduxjs/toolkit'
import { size } from 'lodash'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import { connect } from 'react-redux'
import client from '../../../config/medproSdk'
import { RootState } from '../../../store'
import { RepaymentData, Schedule } from '../../../store/booking/interface'
import {
  selectCanReserveBooking,
  selectMultiScheduleFromBookingSharePayment,
  selectPaymentFeeInfo,
  selectPaymentInfo,
  selectPaymentMethods,
  selectRepaymentData,
  selectSelectedPaymentMethod,
  selectSelectedPaymentType,
  selectTransactionId,
  selectTreeId
} from '../../../store/booking/selector'
import {
  bookingActions,
  getSharePayment,
  setSelectedPaymentMethod,
  setSelectedPaymentType
} from '../../../store/booking/slice'
import { useAppDispatch, useAppSelector } from '../../../store/hooks'
import { hospitalActions } from '../../../store/hospital/hospitalSlice'
import { selectExtraConfig } from '../../../store/hospital/selector'
import { showErrorNotification } from '../../../utils/utils.error'
import {
  handleRedirectPaymentNoAuth,
  repaymentNoAuth
} from '../../components/pages/Booking/func'
import withBreadCrumb from '../../HOCs/withBreadCrumb'
import { BaseAppProps } from '../../type'

export interface PaymentMethodProps extends BaseAppProps {
  selectedPaymentMethod: PaymentMethod
  selectedPaymentType: PaymentType
  partnerId: string
  treeId: string
  transactionId: string
  canReserveBooking: boolean
  repaymentData: RepaymentData
  multiSchedules: Schedule[]
}

const CskhPaymentDesktop = (props: PaymentMethodProps) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const smsCode = router.query.slug as string
  const transactionCode = router.query.transactionId as string
  const [partnerInfo, setPartnerInfo] = useState('')
  const paymentMethods = useAppSelector(selectPaymentMethods)
  const paymentFeeInfo = useAppSelector(selectPaymentFeeInfo)
  const repaymentData = useAppSelector(selectRepaymentData)
  const extraConfig = useAppSelector(selectExtraConfig)
  const paymentInfo = useAppSelector(selectPaymentInfo)
  const bookingInfo = paymentInfo ? paymentInfo[0]?.bookingInfo : []
  const { selectedPaymentMethod, selectedPaymentType, canReserveBooking } =
    props
  const [ipAddress, setIPAddress] = useState('')
  const loadPartner = async () => {
    try {
      const { data } = await client.partner.getPartnerInfo({
        partnerid: bookingInfo?.partnerId
      })
      setPartnerInfo(data)
    } catch (error) {
      showErrorNotification(error)
    }
  }
  useEffect(() => {
    loadPartner()
  }, [bookingInfo])
  useEffect(() => {
    dispatch(setSelectedPaymentMethod(undefined))
    dispatch(setSelectedPaymentType(undefined))
    getIPAddress()
    if (!extraConfig) {
      dispatch(hospitalActions.getExtraConfig())
    }
  }, [])

  useEffect(() => {
    if (router.query.bookingId) {
      dispatch(
        getSharePayment({
          bookingId: router.query.bookingId as string,
          shareToPay: true
        })
      )
    }
  }, [router.query.bookingId])

  const getIPAddress = () => {
    fetch('https://api.ipify.org?format=json')
      .then((response) => response.json())
      .then((data) => {
        setIPAddress(data.ip)
      })
      .catch((error) => {
        console.error('Error fetching IP address:', error)
      })
  }

  const paymentMethodsFiltered = useMemo(() => {
    if (size(paymentMethods) === 0) {
      return paymentMethods
    }

    return paymentMethods?.filter((p) => p.methodId !== 'SHARE_PAYMENT')
  }, [paymentMethods])

  const onSelectPaymentMethod = ({
    item
  }: SelectionData<{
    paymentType: PaymentType
    paymentMethod: PaymentMethod
  }>) => {
    const { paymentType, paymentMethod } = item
    console.log('paymentType', paymentType)
    dispatch(setSelectedPaymentMethod(paymentMethod))
    dispatch(setSelectedPaymentType(paymentType))
  }

  const onReserveSharePayment = async () => {
    await repaymentNoAuth({
      handleRedirectPayment: (data: any) =>
        handleRedirectPaymentNoAuth({
          data,
          router,
          dispatch,
          appId: 'momo',
          smsCode
        }),
      selectedPaymentMethod,
      selectedPaymentType,
      transactionId: bookingInfo?.transactionId,
      smsCode,
      redirectUrl: transactionCode
        ? `${window.location.origin}/transactions/${transactionCode}`
        : `${window.location.origin}/booking/${smsCode}`,
      ...repaymentData
    })
    // dispatch(bookingActions.setPaymentInfo({ loading: false, data: undefined }))
  }

  return (
    <ListPaymentMethod
      paymentFeeInfo={paymentFeeInfo}
      selectedPaymentMethod={selectedPaymentMethod}
      selectedPaymentType={selectedPaymentType}
      paymentMethods={paymentMethodsFiltered}
      canReserveBooking={canReserveBooking}
      onReserve={onReserveSharePayment}
      patient={bookingInfo?.patient}
      onConfirmSelectPaymentMethod={onSelectPaymentMethod}
      allowBack={false}
      partnerInfo={partnerInfo}
      partnerId={bookingInfo?.partnerId}
      makePayment='SharePayment'
      extraConfig={extraConfig}
      hideRefferralCode={true}
    />
  )
}

export default compose(
  withBreadCrumb([{ title: 'Phương thức thanh toán' }]),
  connect((state: RootState) => {
    return {
      selectedPaymentMethod: selectSelectedPaymentMethod(state),
      selectedPaymentType: selectSelectedPaymentType(state),
      treeId: selectTreeId(state),
      transactionId: selectTransactionId(state),
      canReserveBooking: selectCanReserveBooking(state),
      multiSchedules: selectMultiScheduleFromBookingSharePayment(state)
    }
  })
)(CskhPaymentDesktop)
