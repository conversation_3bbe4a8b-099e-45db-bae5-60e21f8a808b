.ItemExamination {
  position: relative;
  overflow: hidden;
  display: flex;
  width: 292px;
  height: fit-content;
  flex-direction: column;
  // align-items: center;
  flex-shrink: 0;
  .infoPosition {
    margin-bottom: 4px;
    color: var(--grey-text, #858585);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  .banner {
    height: 200px;
    width: 100%;
    img {
      border-radius: 8px;
    }
    .showStar {
      position: absolute;
      padding: 6px 10px;
      align-items: center;
      display: inline-flex;
      gap: 4px;
      bottom: 40%;
      right: 21px;
      border-radius: 50px;
      width: 62px !important;
      border: 0.5px solid var(--accent-yellow, #ffb54a);
      background: var(--white, #fff);
      box-shadow: 0px 2px 5px 0px rgba(16, 36, 65, 0.15);
      p {
        margin: 3px 0px 0 -9px;

        color: var(--primary-body-text, #003553) !important;
        font-size: 16px !important;
        font-style: normal !important;
        font-weight: 500 !important;
        line-height: normal !important;
      }
      :global {
        .ant-rate-star-zero {
          display: none !important;
        }
      }
    }
  }
  .tagCashBack {
    position: absolute;
    top: 13px;
    right: -62px;
    width: 190.81px;
    height: 25.86px;
    border: none;
    transform: rotate(35deg);
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    text-transform: uppercase;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 12px;
    font-weight: 700;
    line-height: 17.58px;
    @media (max-width: 576px) {
      width: 107.53px;
      height: 20.77px !important;
      top: 10px;
      right: -25px;
      font-size: 10px;
      line-height: 11.72px;
    }
    &:hover {
      cursor: pointer;
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }
    &:focus {
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }
  }
  .popoverCashBack {
    :global {
      .ant-popover-content {
        border-radius: 12px !important;
        max-width: 510px;
      }
      .ant-popover-inner {
        backdrop-filter: blur(30px);
        background-color: rgba(255, 255, 255, 0.9);
      }
      .ant-popover-inner-content {
        p {
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
  }
  h3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-line-clamp: 2;
    font-size: 20px !important;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    margin-bottom: 4px;
    margin-top: 20px;
    text-decoration: none;
  }

  .location {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-line-clamp: 2;
    color: var(--primary-body-text, #003553);
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 4px;
  }
  p {
    width: 100%;
    text-align: left !important;
    color: var(--accent-yellow, #ffb54a) !important;
    font-size: 16px !important;
    font-style: normal !important;
    font-weight: 400 !important;
    line-height: normal !important;
    margin-bottom: 0px;
    strong {
      font-weight: 700 !important;
    }
  }
  // :global {
  //   .ant-card-cover {
  //     padding: 16px 16px 0 16px;
  //   }
  //   .ant-card-body {
  //     padding: 20px 16px 16px 16px;
  //   }
  //   .ant-card-bordered {
  //     display: none;
  //   }
  // }
  .groupInfo {
    align-items: center;
    width: 100%;
    .info {
      align-items: flex-start;
      display: flex;
      flex: 0 0 auto;

      gap: 8px;
      @media (max-width: 992px) {
        margin-bottom: 6px !important;
      }
    }
    .infoDegree {
      color: var(--primary-body-text, #003553);
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-top: -1px;
      word-wrap: break-word;
      margin: 0;
      margin-bottom: 4px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      strong {
        font-weight: 700;
      }
      @media (max-width: 992px) {
        font-size: 18px;
      }
    }

    .rateMobile {
      display: flex;
      margin-bottom: 4px;
      .rate {
        width: fit-content !important;
        margin: 5px 0 0 6px;
        color: var(--primary-body-text, #003553) !important;
        font-size: 16px !important;
        font-weight: 500 !important;
      }
    }
    .specialist {
      display: flex;
      white-space: nowrap;

      @media (max-width: 992px) {
        margin-bottom: 6px !important;
      }

      .specialistItem {
        width: 50%;
        -webkit-line-clamp: 1; /* số dòng hiển thị */
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        height: fit-content;
        color: var(--text-blue, #11a2f3);
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        padding: 6px 12px;
        text-align: center;
        border-radius: 20px;
        background: var(--Light-blue, #e6f2ff);
        margin-right: 8px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
.doctorAddress {
  background: #eff7ff;
  width: 292px;
  min-height: 150px;
  padding: 12px 14px;
  position: relative;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  .bottomLeft {
    align-items: flex-start;
    display: flex;
    flex: 0 0 auto;
    gap: 4px;

    .linear-location {
      height: 21px !important;
      width: 21px !important;
    }
    .groupAddress {
      align-items: flex-start;
      display: flex;
      flex: 1;
      flex-direction: column;
      flex-grow: 1;
      gap: 4px;
    }
    .hopital {
      align-self: stretch;
      color: #003553;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: normal;
      margin-top: -1px;
      margin-bottom: 0px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      @media (max-width: 768px) {
        font-size: 16px;
      }
    }
    .address {
      color: var(--grey-text, #858585);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      word-wrap: break-word;
      margin-bottom: 12px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      @media (max-width: 768px) {
        font-size: 13px;
      }
    }
  }
  .groupButton {
    width: calc(100% - 24px);
    display: flex;
    position: absolute;
    bottom: 12px;
    gap: 8px;
  }
  .btnView {
    background: #fff;
    border-radius: 30px;
    border: 1px solid #00b5f1;
    font-weight: 500;
    font-size: 13px;
    line-height: 19px;
    color: #00b5f1;
    display: flex;
    padding: 10px 30px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex: 1 0 0;
    align-self: stretch;
    @media (max-width: 992px) {
      display: flex;
      width: 130px;
      height: 35px;
      align-items: flex-start;
      display: flex;
      padding: 10px 30px;
      justify-content: center;
      align-items: center;
      flex: 1 0 0;
      align-self: stretch;
    }
    &:hover {
      cursor: pointer;
      box-shadow: 0px 2px 7px 0px rgba(16, 36, 65, 0.35);
    }
  }
  .btnBooking {
    background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
    border-radius: 30px;
    border: none;
    font-weight: 500;
    font-size: 13px;
    line-height: 19px;
    color: white;
    display: flex;
    padding: 10px 30px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    @media (max-width: 992px) {
      display: flex;
      align-items: flex-start;
      display: flex;
      padding: 10px 30px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      flex: 1 0 0;
      align-self: stretch;
    }
    &:hover {
      cursor: pointer;
      background: linear-gradient(23.63deg, #07aae0, #00c6e1 113.91%);
      color: #fff;
    }
  }
  .onlyBtn {
    width: calc(100%) !important;
  }
}

.body {
  margin-left: 12px !important;
  border-radius: 16px;
  border: 1px solid transparent;
  &:hover {
    border: 1px solid #07aae0;
  }
  @media (max-width: 992px) {
    display: none;
  }
  :global {
    // .ant-card-cover {
    //   padding: 16px 16px 0 16px;
    // }
    // .ant-card-body {
    //   padding: 20px 16px 16px 16px;
    // }
    .ant-card-body {
      padding: 8px 0 0 0;
    }
    .ant-card-bordered {
      padding: 16px;
      border-top-right-radius: 16px;
      border-top-left-radius: 16px;
      min-height: 345px;
    }
  }
}
