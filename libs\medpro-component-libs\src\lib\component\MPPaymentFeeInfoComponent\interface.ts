import { PaymentMethod, PaymentType } from '@medpro-libs/libs'

export interface Schedule {
  partnerId: string
  service?: any
  subject?: any
  doctor?: any
  room?: any
  timeslot?: any
  day?: any
  serviceId?: string
  subjectId?: string
  roomId?: string
  doctorId?: string
  date?: string
  timeId?: string
  addonServices?: any[]
  treeId?: any
}

export interface PaymentFeeInfo {
  subjectName?: string
  doctorName?: string
  serviceName?: string
  displayDetail?: string
  servicePrice?: number
  serviceAdvanced?: number
  addonServices?: any
  dateTime?: any
}

export interface PaymentFeeTotalInfo {
  paymentFeeInfoList: Partial<Schedule>[]
  totalPaymentFee?: number
  medproFee?: number
  subTotal?: number
  totalFee?: number
  grandTotal?: number
  agreement?: string
  isPaymentSelected?: boolean
  displayDetail?: string
  subTotalNote?: string
  totalFeeNote?: string
  selectedPaymentMethod?: PaymentMethod
  partnerId?: any
  selectedPaymentType?: PaymentType
  selectedMedproCare?: ItemMedproCare
}

interface ItemMedproCare {
  currency: string
  description: string
  duration: string
  id: string
  name: string
  originalPrice: number
  price: number
}
