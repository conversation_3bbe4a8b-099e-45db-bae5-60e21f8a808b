import React from 'react'
import { PaymentFeeTotalInfo } from '../../component/MPPaymentFeeInfoComponent/interface'
import MPPaymentFeeInfoCardItem from './common/MPPaymentFeeInfoCardItem'
import MPPaymentTotalInfo from './common/MPPaymentTotalInfo'
import styles from './styles.module.less'
import { first, get } from 'lodash'
import { IoCardOutline } from 'react-icons/io5'
import { ExtraConfig } from '@medpro-libs/types'

interface Props {
  paymentFeeInfo: PaymentFeeTotalInfo
  partnerId: any
  extraConfig: ExtraConfig
  setReferralCode?: (code: string) => void
  isRepayment?: boolean
  hideRefferralCode?: boolean
}

const MPPaymentFeeInfoCard = ({
  paymentFeeInfo,
  partnerId,
  extraConfig,
  setReferralCode,
  isRepayment,
  hideRefferralCode
}: Props) => {
  return (
    <>
      <div className={styles['paymentTitle']}>
        <IoCardOutline
          className={styles['paymentTitleIcon']}
          color={'#11a2f3'}
          size={20}
        />
        <h3>Thông tin thanh toán</h3>
      </div>
      <div className={styles['cardView']}>
        <ul className={styles['listBill']}>
          {paymentFeeInfo?.paymentFeeInfoList?.map((feeInfo, i) => {
            return (
              <React.Fragment key={i}>
                <MPPaymentFeeInfoCardItem
                  serviceName={get(feeInfo, 'service.name')}
                  servicePrice={get(feeInfo, 'service.price')}
                  subjectName={get(feeInfo, 'subject.name')}
                  doctorName={get(feeInfo, 'doctor.name')}
                  serviceAdvanced={get(feeInfo, 'service.advanced')}
                  displayDetail={get(feeInfo, 'service.displayDetail')}
                  addonServices={get(feeInfo, 'service.addonServices')}
                  dateTime={get(feeInfo, 'dateTime')}
                />
              </React.Fragment>
            )
          })}
        </ul>

        <ul className={styles['listPayment']}>
          <MPPaymentTotalInfo
            paymentFeeInfo={{
              ...paymentFeeInfo,
              partnerId: partnerId,
              displayDetail: get(
                first(paymentFeeInfo?.paymentFeeInfoList),
                'service.displayDetail'
              )
            }}
            extraConfig={extraConfig}
            setReferralCode={setReferralCode}
            isRepayment={isRepayment}
            hideRefferralCode={hideRefferralCode}
          />
        </ul>
      </div>
    </>
  )
}

export default MPPaymentFeeInfoCard
