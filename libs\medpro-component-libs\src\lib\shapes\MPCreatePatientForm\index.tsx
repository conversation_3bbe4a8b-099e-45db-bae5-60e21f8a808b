import React from 'react'
import MPCreatePatientFormComponent from '../../component/MPCreatePatientFormComponent'
import MPCreatePatientFormCard from '../../ui/MPCreatePatientFormCard'

interface Props {
  data: any
  onSubmit: (values: any) => void
  onChangeAddress: (type: string, id: string) => void
  cleanDistrictsAndWards?: () => void
  appId?: string
  submitting?: boolean
  userCountry?: string
  patientYearOldAccepted?: number
  partnerId?: string
  userId?: string
}

export const MPCreatePatientForm = ({
  data,
  onSubmit,
  onChangeAddress,
  submitting,
  patientYearOldAccepted,
  cleanDistrictsAndWards,
  appId = 'ssr',
  userCountry = 'VN',
  partnerId,
  userId
}: Props) => {
  return (
    <MPCreatePatientFormComponent
      onSubmit={onSubmit}
      onChangeAddress={onChangeAddress}
      appId={appId}
      renderItem={(
        handleSubmit: (values: any) => void,
        handleChangeAddress: (type: string, id: string) => void,
        isCSKHApp: () => boolean
      ) => {
        return (
          <MPCreatePatientFormCard
            data={data}
            patientYearOldAccepted={patientYearOldAccepted}
            isCSKHApp={isCSKHApp()}
            cleanDistrictsAndWards={cleanDistrictsAndWards}
            userCountry={userCountry}
            handleSubmit={handleSubmit}
            handleChangeAddress={handleChangeAddress}
            submitting={submitting}
            partnerId={partnerId}
            userId={userId}
          />
        )
      }}
    />
  )
}

export default MPCreatePatientForm
