export const INSERT_PATIENT = 'mongo/patient/insert'
export const INSERT_PATIENT_BASIC_INFO = 'mongo/patient/insert-basic-info'
export const GET_PATIENTS_BY_USER_ID_V2 = 'mongo/patient/getbyuserid'
export const GET_PATIENTS_BY_USER_ID = 'mongo/patient/getbyuserid-v2'
export const CREATE_PATIENT_PROFILE = 'mongo/patient/create-patient-profile'
export const GET_PATIENT_PROFILES = 'mongo/patient/get-patient-profiles'
export const FIND_PATIENT_BY_MSBN = 'mongo/patient/getbymsbn'
export const FIND_PATIENT_BY_EXTRA = 'mongo/patient/find-patient-by-extra-info'
export const VERIFY_PHONE_FIND_PATIENT = 'mongo/patient/verify-phone'
export const VERIFY_INSURANCE_CODE_FIND_PATIENT =
  'mongo/patient/verify-insurance-code'
export const ADD_PATIENT_INTO_USER_FIND_PATIENT =
  'mongo/patient/add-patient-to-user'
export const GET_PATIENT_DETAIL_FOR_UPDATE = 'mongo/patient/detail-for-update'
export const UNLINK_PATIENT = 'mongo/patient/unlink-patient'
export const UPDATE_PATIENT_WITH_PATIENT_CODE = 'mongo/patient/update-msbn'
export const UPDATE_PATIENT_WITHOUT_PATIENT_CODE =
  'mongo/patient/update-without-msbn'
export const GET_INSURANCE_INFO = 'mongo/patient/insurance/getInfo'
export const GET_INSURANCE_DATE = 'mongo/patient/insurance/getDate'
export const GET_INSURANCE_PARSE_ADDRESS =
  'mongo/patient/insurance/parse-address'
export const VALIDATE_BOOKING_RULE_PATIENT =
  'mongo/patient/validate-booking-rule'
export const GET_PHONE_LOCALE = 'user/phone-locale-list-patient'
export const FIND_PATIENT_HIS = 'mongo/patient/find-patient-his'
export const UPDATE_USER_INFO = 'user/update-user'

export const GET_USER_CONFIG_BTN = 'mongo/patient/config-btn'
