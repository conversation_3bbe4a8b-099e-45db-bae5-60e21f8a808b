import * as React from 'react'
import styles from './styles.module.less'
import { Checkbox } from 'antd'
import cx from 'classnames'
import MPButton from '../MPButton'
import { useState } from 'react'
import handleInfoPatient from './common/handleInfoPatient'
import MPIcon from '../MPIcon'
import { BiLeftArrowAlt } from 'react-icons/bi'
import { useRouter } from 'next/router'

export interface Props {
  handleSubmit: () => void
  data: any
}

const MPConfirmProfileAppCard = ({ handleSubmit, data }: Props) => {
  const router = useRouter()
  const [check, setCheck] = useState(false)

  const onFinish = () => {
    handleSubmit()
  }

  return (
    <div className={styles['main']}>
      <div className={styles['headerTitle']}>
        <BiLeftArrowAlt
          size={24}
          onClick={() => router.back()}
          className={styles['headerSide']}
        />
        <p><PERSON><PERSON><PERSON> nhận thông tin bệnh nhân</p>
        <div className={styles['headerSide']}></div>
      </div>
      {/* <h1>Xác nhận thông tin bệnh nhân</h1> */}
      <div className={styles['infoBox']}>
        {handleInfoPatient({ item: data }).map((item, i) => {
          return (
            <div className={styles['infoItem']} key={i}>
              <span className={styles['label']}>
                <MPIcon name={item.icon as any} size={18} fill='#7B8794' />
                {item?.label}:
              </span>
              <span className={styles['value']}>{item?.value}</span>
            </div>
          )
        })}
      </div>
      <div className={styles['footerBtn']}>
        <div className={styles['checkedConfirm']}>
          {/* <hr /> */}
          <Checkbox
            checked={check}
            onChange={(e) => setCheck(e.target.checked)}
          >
            Tôi xác nhận đây là thông tin chính xác và tôi chịu trách nhiệm cho
            việc sử dụng hồ sơ này.
          </Checkbox>
        </div>
        <MPButton
          className={cx({
            [styles['btnSubmit']]: true,
            [styles['disabledBtn']]: !check
          })}
          type='primary'
          onClick={onFinish}
          full='true'
          disabled={!check}
        >
          Xác nhận
        </MPButton>
      </div>
    </div>
  )
}

export default MPConfirmProfileAppCard
