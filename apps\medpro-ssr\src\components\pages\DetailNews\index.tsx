import {
  MPNewNewsMedical,
  MPNewPost,
  MPNewSamePost,
  MPSuggest,
  customLoader,
  useWindowResize
} from '@medpro-libs/libs'
import { Col, Row } from 'antd'
import cx from 'classnames'
import { GetStaticPropsContext } from 'next'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { bookingActions } from '../../../../store/booking/slice'
import { useAppSelector } from '../../../../store/hooks'
import { totalDataActions } from '../../../../store/total/slice'
import { SEOPage } from '../../../../utils/SEOPage'
import { BannerLeft } from '../../../../utils/fakeData/news'
import { getAppInfo } from '../../../../utils/method'
import { SSG_REVALIDATE_SECOND } from '../../../../utils/utils.contants'
import {
  fetchDeeplinkBookingClient,
  fetchDeeplinkClient
} from '../../../../utils/utils.query'
import {
  fetchDetailNews,
  fetchNewsCategory,
  fetchSchemaNews,
  fetchSearch
} from '../../../../utils/utils.query-server'
import styles from './styles.module.less'
import { Banner } from './common/Banner'
const NewsPost = ({
  data,
  sameData = [],
  isWebview = false,
  subContent
}: any) => {
  const router = useRouter()
  const { asPath } = router
  const dispatch = useDispatch()
  let previousScrollPosition = 0
  const isMobile = useWindowResize(1024)
  const [stickyScroll, setStickyScroll] = useState(false)
  const bannerNew = useAppSelector((s) => s.total.banner)
  console.log('banner :>> ', bannerNew)
  const update = () => {
    const currentScrollY = window.scrollY
    const previousScrollY = (window as any).previousScrollY || 0
    if (currentScrollY > previousScrollY) {
      setStickyScroll(true)
    } else if (currentScrollY < previousScrollY) {
      setStickyScroll(false)
    }
    ;(window as any).previousScrollY = currentScrollY
  }

  useEffect(() => {
    window.addEventListener('scroll', update)
    return () => window.removeEventListener('scroll', update)
  }, [])

  const onBookingLink = (item: any) => {
    dispatch(
      bookingActions.handleBookingCta({
        type: !isMobile ? data?.ctaSearch?.category : 'bookingApp',
        item
      })
    )
  }
  const getDeeplinkDoctor = async (doctor_slug: any) => {
    const [dataDeeplink] = await Promise.all([fetchDeeplinkClient(doctor_slug)])
    return dataDeeplink?.shortLink
  }

  useEffect(() => {
    dispatch(totalDataActions.getBannerNews({}))
    previousScrollPosition = window.scrollY
  }, [])

  const getDeeplinkDoctorBooking = async (doctor_slug: any) => {
    const [dataDeeplinkBooking] = await Promise.all([
      fetchDeeplinkBookingClient(doctor_slug, 'booking')
    ])
    return dataDeeplinkBooking?.shortLink
  }

  return (
    <div
      className={cx(
        styles['post'],
        isWebview ? styles['isWebview'] : undefined
      )}
    >
      <div className={styles['container']}>
        <Banner isMobile={isMobile} data={bannerNew.header?.[0]} />
        <Row gutter={40}>
          <Col span={24} lg={16}>
            <MPNewPost data={data} />
          </Col>
          <Col
            span={24}
            lg={8}
            className={cx(
              styles['detailRight'],
              stickyScroll ? styles[`detailBottom`] : styles[`detailBottom`]
            )}
          >
            <div className={styles['banner']}>
              <Link href={bannerNew.stickySmall?.[0]?.cta?.url ?? '#'}>
                <a target={bannerNew.stickySmall?.[0]?.cta?.target ?? '#'}>
                  <Image
                    src={
                      isMobile
                        ? bannerNew.stickySmall?.[0]?.imageMobileUrl
                        : bannerNew.stickySmall?.[0]?.imageDesktopUrl
                    }
                    width={360}
                    height={180}
                    layout='responsive'
                    objectFit='contain'
                    alt={bannerNew.stickySmall?.[0]?.alt}
                  />
                </a>
              </Link>
              <Link href={bannerNew.stickyBig?.[0]?.cta?.url ?? '#'}>
                <a target={bannerNew.stickyBig?.[0]?.cta?.target ?? '_self'}>
                  <Image
                    src={
                      isMobile
                        ? bannerNew.stickyBig?.[0]?.imageMobileUrl
                        : bannerNew.stickyBig?.[0]?.imageDesktopUrl
                    }
                    width={367}
                    height={575}
                    layout='responsive'
                    alt='Banner tải app Medpro'
                  />
                </a>
              </Link>
            </div>
          </Col>
        </Row>
        {data?.ctaSearch && data?.ctaSearch?.status && (
          <MPSuggest
            data={subContent}
            new={data}
            onBookingLink={onBookingLink}
            getDeeplinkDoctor={getDeeplinkDoctor}
            getDeeplinkDoctorBooking={getDeeplinkDoctorBooking}
            category={data?.ctaSearch?.category}
            treeId={data?.ctaSearch?.treeId}
          />
        )}
        {!isWebview && (
          <div className={styles['pc']}>
            <MPNewSamePost data={sameData} />
          </div>
        )}

        {!isWebview && isMobile && (
          <div className={styles['mobile']}>
            <MPNewNewsMedical
              data={sameData}
              isMobile={isMobile}
              title='Tin liên quan'
              href={true}
            />
          </div>
        )}
      </div>
      {/* {popupQuery.isFetched &&
        data?.partners === 'medpro' &&
        popupQuery.data?.status && (
          <MPModalDownloadHoliday data={dataModalDownload} />
        )} */}
    </div>
  )
}
NewsPost.ssr = true
export default NewsPost

export async function getStaticPaths(context: any) {
  return { paths: [], fallback: 'blocking' }
}

export async function getStaticProps(context: GetStaticPropsContext) {
  const appInfo = await getAppInfo({ ctx: context })

  const { slug } = context.params
  let subContent = []
  const [data, sameData, schemaNews] = await Promise.all([
    fetchDetailNews(slug.toString()),
    fetchNewsCategory({
      partnerId: appInfo.partnerId,
      page: 1,
      limit: 4
    }),
    fetchSchemaNews(slug.toString())
  ])

  const firstData = data?.[0]
  if (firstData?.ctaSearch && firstData?.ctaSearch?.status) {
    const newData = await fetchSearch({
      search_key: firstData?.ctaSearch?.keywords,
      category: 'all',
      treeIds: firstData?.ctaSearch?.treeId,
      partnerId: firstData?.ctaSearch?.partnerId,
      limit: 6,
      offset: 1
    })
    subContent = newData.reduce((c, i) => {
      c[i.category] = {
        ...i,
        results: i.results
      }
      return c
    }, {})
  }
  if (!firstData?.partners?.includes('medpro')) {
    return {
      redirect: {
        permanent: true,
        destination: '/404'
      },
      revalidate: SSG_REVALIDATE_SECOND
    }
  }

  const detailSEO = {
    ...schemaNews?.seo,
    ogImage: schemaNews?.seo?.ogImage
      ? schemaNews?.seo?.ogImage
      : `https://cms.medpro.com.vn${firstData?.image?.[0]?.url}`,
    detailType: 'news',
    newsTitle: firstData?.title
  }

  return {
    props: {
      meta: SEOPage,
      data: firstData,
      sameData,
      breadcrumb: [
        {
          title: firstData?.subcategories?.[0]?.title || 'Tin tức',
          link: firstData?.subcategories?.[0]?.slug
            ? `/tin-tuc/${firstData?.subcategories?.[0]?.slug}`
            : '/tin-tuc'
        },
        { title: firstData?.title }
      ],
      subContent: subContent,
      seo_keywords: firstData.keywords,
      seo_description: firstData.metaDescription,
      detailNews: {
        ...firstData,
        urlSchema: `https://medpro.vn/tin-tuc/${slug}`
      },
      schemaNews: schemaNews,
      detailSEO
    },
    revalidate: SSG_REVALIDATE_SECOND
  }
}
